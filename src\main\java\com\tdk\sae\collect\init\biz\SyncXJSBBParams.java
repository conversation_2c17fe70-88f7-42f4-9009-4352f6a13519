package com.tdk.sae.collect.init.biz;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.xjsbb.service.XJSBBParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@RequiredArgsConstructor
@Component
@DependsOn("systemInfoService")
public class SyncXJSBBParams {

    private final XJSBBParamService xjsbbParamService;

    @PostConstruct
    public void init() {
        if (CollectionUtil.isEmpty(SystemInfoService.getEquipmentMap())) {
            return;
        }
        List<EquipmentDTO> xjsbb = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.XJSBB_LOG.getName());
        if (CollectionUtil.isEmpty(xjsbb)) {
            return;
        }
        xjsbb.forEach(xjsbbParamService::trySyncParams);
    }
}
