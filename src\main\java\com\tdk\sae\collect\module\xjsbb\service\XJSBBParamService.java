package com.tdk.sae.collect.module.xjsbb.service;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.xjsbb.mapper.XJSBBParamMapper;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBParamDTO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBParamPO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
public class XJSBBParamService extends ServiceImpl<XJSBBParamMapper, XJSBBParamPO> {

    private static final Logger logger = LoggerFactory.getLogger(XJSBBParamService.class);

    private final SystemProperties systemProperties;

    private final TimedCache<String, List<XJSBBParamPO>> xjsbbParamCache = CacheUtil.newTimedCache(30000);

    @Transactional
    public void trySyncParams(EquipmentDTO equip) {
        String code = systemProperties.getClientCode();
        if (code.equals("4D-CCM6") || code.equals("4D-CCM6-2")){
            return;
        }
        HttpRequest request = HttpUtil.createGet(systemProperties.getServer().getEndpoint() + "/equip/client/info/xjsbb/params/" + equip.getId());
        try {
            HttpResponse response = request.execute();
            JSONObject result = JSONObject.parseObject(response.body());
            List<XJSBBParamDTO> paramList = JSONObject.parseArray(result.get("data").toString(), XJSBBParamDTO.class);
            if (!paramList.isEmpty()) {
                try {
                    this.saveOrUpdateBatch(BeanUtil.copyToList(paramList, XJSBBParamPO.class));
                    xjsbbParamCache.remove(equip.getId());
                } catch (Exception e) {
                    logger.error("SyncXJSBB error! msg:{}", e.getMessage());
                }
            }
        } catch (IORuntimeException ioRuntimeException) {
            logger.error("Can't not connect to HDS Server! /equip/client/info/xjsbb/params fetch failed! Use local data instead.");
        }
    }

}
