package com.tdk.sae.collect.module.abDim.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.abDim.mapper.ABAdjustRulesApplyMapper;
import com.tdk.sae.collect.module.abDim.model.adjust.ABAdjustRulesPopulate;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustRulesApplyPO;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class ABAdjustRulesApplyService extends ServiceImpl<ABAdjustRulesApplyMapper, ABAdjustRulesApplyPO> {

    private final ABAdjustRulesApplyMapper icsAdjustRulesApplyMapper;

    public List<ABAdjustRulesPopulate> getRulesPopulate(Long equipId) {
        return icsAdjustRulesApplyMapper.getRulesPopulate(equipId);
    }

    public List<ABAdjustRulesApplyPO> getRulesApplyByType(Long equipId, Integer ruleType) {
        return icsAdjustRulesApplyMapper.getRulesApplyByType(equipId, ruleType);
    }
}
