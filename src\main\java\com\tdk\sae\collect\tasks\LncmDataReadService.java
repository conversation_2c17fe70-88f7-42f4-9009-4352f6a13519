package com.tdk.sae.collect.tasks;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.mapper.ICSTrendLogMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSTrendLog;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Component
public class LncmDataReadService {

    private static final Logger logger = LoggerFactory.getLogger(LncmDataReadService.class);

    @Resource(name = "${system.read-type}")
    private IReadPlcService readPlcService;
    private final SystemProperties systemProperties;
    private final KV8000ParamService kv8000ParamService;
    private final String[] LreadParamList={"Temp_control1_L","Temp_control2_L","Temp_control3_L","Temp_control4_L","Temp_control5_L",
            "Flow1_L","Flow2_L","Flow3_L","Flow4_L","Flow5_L"};
    private final String[] RreadParamList={"Temp_control1_R","Temp_control2_R","Temp_control3_R","Temp_control4_R","Temp_control5_R",
            "Flow1_R","Flow2_R","Flow3_R","Flow4_R","Flow5_R"};
    private final String[] LPurityParamList={"N2_purity_L"};
    private final String[] RPurityParamList={"N2_purity_R"};
    private final String LFlag="Mark_Conrrol";
    private final String RFlag="Mark_Purity";
    @Resource(name = "${system.write-type}")
    private IWritePlcService kv8000WriteService;
    private final ICSTrendLogMapper icsTrendLogMapper;

    //    @Scheduled(fixedRate = 250)
    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void readRelatedParams() {
        try {
            List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
            List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_TREND.getName());
            //判断是否有设备
            if(CollectionUtil.isEmpty(icsEquips)){
                return;
            }
            //读取标志位
            KV8000ParamPO LFlagParam = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getVariable, LFlag).last("limit 1").one();
            KV8000ParamPO RFlagParam = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getVariable, RFlag).last("limit 1").one();
            if (null == LFlagParam || null == RFlagParam) {
                logger.warn("No parameter found for " + LFlag + " or " + RFlag);
                return;
            }
            KV8000DataPO Detect_Completed_L = readPlcService.readParam(icsLogEquips.get(0), LFlagParam);
            KV8000DataPO Detect_Completed_R = readPlcService.readParam(icsLogEquips.get(0), RFlagParam);
            if(Detect_Completed_L.getReadData().equals("1")){
                String barCode="";
                EquipmentDTO e=icsEquips.stream().filter(i->{return i.getClientCode().contains("-L-");}).findFirst().orElse(null);
                ICSTrendLog LtrendLog=new ICSTrendLog();
                if(e!=null){
                    barCode=getBarCode(e.getAddress());
                    LtrendLog.setEquipId(Long.parseLong(e.getId()));
                }
                for(int i=0;i<LreadParamList.length;i++){
                    KV8000ParamPO tempParam = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getVariable, LreadParamList[i]).last("limit 1").one();
                    KV8000DataPO tempData = readPlcService.readParam(icsLogEquips.get(0), tempParam);
                    switch (LreadParamList[i]){
                        case "Temp_control1_L" :
                            LtrendLog.setTemperature1(tempData.getReadData());
                            break;
                        case "Temp_control2_L" :
                            LtrendLog.setTemperature2(tempData.getReadData());
                            break;
                        case "Temp_control3_L" :
                            LtrendLog.setTemperature3(tempData.getReadData());
                            break;
                        case "Temp_control4_L" :
                            LtrendLog.setTemperature4(tempData.getReadData());
                            break;
                        case "Temp_control5_L" :
                            LtrendLog.setTemperature5(tempData.getReadData());
                            break;
                        case "Flow1_L" :
                            LtrendLog.setFlow1(tempData.getReadData());
                            break;
                        case "Flow2_L" :
                            LtrendLog.setFlow2(tempData.getReadData());
                            break;
                        case "Flow3_L" :
                            LtrendLog.setFlow3(tempData.getReadData());
                            break;
                        case "Flow4_L" :
                            LtrendLog.setFlow4(tempData.getReadData());
                            break;
                        case "Flow5_L" :
                            LtrendLog.setFlow5(tempData.getReadData());
                            break;
                        }
                    }
                LtrendLog.setBarcode(barCode);
                LtrendLog.setDescription("TF");
                icsTrendLogMapper.insert(LtrendLog);

                String RbarCode="";
                EquipmentDTO equipmentR=icsEquips.stream().filter(i->{return i.getClientCode().contains("-R-");}).findFirst().orElse(null);
                ICSTrendLog RtrendLog=new ICSTrendLog();
                if(equipmentR!=null){
                    RbarCode=getBarCode(equipmentR.getAddress());
                    RtrendLog.setEquipId(Long.parseLong(equipmentR.getId()));
                }
                for(int i=0;i<RreadParamList.length;i++){
                    KV8000ParamPO tempParam = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getAddress, RreadParamList[i]).last("limit 1").one();
                    KV8000DataPO tempData = readPlcService.readParam(icsLogEquips.get(0), tempParam);
                    switch (RreadParamList[i]){
                        case "Temp_control1_R" :
                            RtrendLog.setTemperature1(tempData.getReadData());
                            break;
                        case "Temp_control2_R" :
                            RtrendLog.setTemperature2(tempData.getReadData());
                            break;
                        case "Temp_control3_R" :
                            RtrendLog.setTemperature3(tempData.getReadData());
                            break;
                        case "Temp_control4_R" :
                            RtrendLog.setTemperature4(tempData.getReadData());
                            break;
                        case "Temp_control5_R" :
                            RtrendLog.setTemperature5(tempData.getReadData());
                            break;
                        case "Flow1_R" :
                            RtrendLog.setFlow1(tempData.getReadData());
                            break;
                        case "Flow2_R" :
                            RtrendLog.setFlow2(tempData.getReadData());
                            break;
                        case "Flow3_R" :
                            RtrendLog.setFlow3(tempData.getReadData());
                            break;
                        case "Flow4_R" :
                            RtrendLog.setFlow4(tempData.getReadData());
                            break;
                        case "Flow5_R" :
                            RtrendLog.setFlow5(tempData.getReadData());
                            break;
                    }
                }
                RtrendLog.setBarcode(RbarCode);
                RtrendLog.setDescription("TF");
                //将标志位设置为0
                boolean flag=kv8000WriteService.write(icsLogEquips.get(0), BeanUtil.copyProperties(LFlagParam, KV8000ParamDTO.class),"0");
                icsTrendLogMapper.insert(RtrendLog);
                }
            if(Detect_Completed_R.getReadData().equals("1")){
                String barCode="";
                EquipmentDTO e=icsEquips.stream().filter(i->{return i.getClientCode().contains("-L-");}).findFirst().orElse(null);
                ICSTrendLog LtrendLog=new ICSTrendLog();
                if(e!=null){
                    barCode=getBarCode(e.getAddress());
                    LtrendLog.setEquipId(Long.parseLong(e.getId()));
                }
                for(int i=0;i<LPurityParamList.length;i++){
                    KV8000ParamPO tempParam = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getVariable, LPurityParamList[i]).last("limit 1").one();
                    KV8000DataPO tempData = readPlcService.readParam(icsLogEquips.get(0), tempParam);
                    switch (LPurityParamList[i]){
                        case "N2_purity_L" :
                            LtrendLog.setN2Purity(tempData.getReadData());
                            break;
                    }
                }
                LtrendLog.setBarcode(barCode);
                LtrendLog.setDescription("N2");
                //将标志位设置为0
                icsTrendLogMapper.insert(LtrendLog);
                String RbarCode="";
                EquipmentDTO equipmentR=icsEquips.stream().filter(i->{return i.getClientCode().contains("-R-");}).findFirst().orElse(null);
                ICSTrendLog RtrendLog=new ICSTrendLog();
                if(equipmentR!=null){
                    RbarCode=getBarCode(equipmentR.getAddress());
                    RtrendLog.setEquipId(Long.parseLong(equipmentR.getId()));
                }
                for(int i=0;i<RPurityParamList.length;i++){
                    KV8000ParamPO tempParam = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getAddress, RPurityParamList[i]).last("limit 1").one();
                    KV8000DataPO tempData = readPlcService.readParam(icsLogEquips.get(0), tempParam);
                    switch (RPurityParamList[i]){
                        case "N2_purity_R" :
                            RtrendLog.setN2Purity(tempData.getReadData());
                            break;
                    }
                }
                RtrendLog.setBarcode(RbarCode);
                RtrendLog.setDescription("N2");
                //将标志位设置为0
                boolean flag=kv8000WriteService.write(icsLogEquips.get(0), BeanUtil.copyProperties(RFlagParam, KV8000ParamDTO.class),"0");
                icsTrendLogMapper.insert(RtrendLog);
            }
        }catch (Exception e){
            logger.error("PLCRwService Read error:{}", e.getMessage());
        }


    }


    private String getBarCode(String directoryPath) {
        try {
            // 获取当天日期作为文件名过滤条件
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 查找当前路径下以当天日期命名的 .txt 文件
            List<Path> txtFiles = Files.walk(Paths.get(directoryPath))
                    .filter(path -> Files.isRegularFile(path) && path.getFileName().toString().endsWith(".txt"))
                    .filter(path -> path.getFileName().toString().contains(today))
                    .collect(Collectors.toList());

            if (txtFiles.isEmpty()) {
                logger.warn("No .txt file needed found in directory: {}", directoryPath);
                return StringUtils.EMPTY;
            }

            // 读取最新文件不断覆盖获取最后一行内容
            Stream<String> lines = Files.lines(txtFiles.get(0));
            String lastLine = null;
            for (String line : lines.collect(Collectors.toList())) {
                lastLine = line;
            }
            return extractBarcode(lastLine);
        } catch (IOException e) {
            logger.error("Failed to read barcode from file: {}", e.getMessage());
            return StringUtils.EMPTY;
        }
    }


    private  String extractBarcode(String line) {
        if (line == null || line.trim().isEmpty()) {
            return StringUtils.EMPTY;
        }
        String[] parts = line.split(",");
        if (parts.length == 2) {
            return parts[1].trim();
        }
        return StringUtils.EMPTY;
    }



}
