package com.tdk.sae.collect.module.ivs.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ivs.mapper.IVSConfMapper;
import com.tdk.sae.collect.module.ivs.module.po.IVSConfPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class IVSConfService  extends ServiceImpl<IVSConfMapper, IVSConfPO> {
}
