package com.tdk.sae.collect.module.plc.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.kv8000.rw.KVDataReader;
import com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;

@RequiredArgsConstructor
@Service("kv8000read")
public class KV8000ReadService implements IReadPlcService {

    private final Logger logger = LoggerFactory.getLogger(KV8000ReadService.class);

    private final KV8000ParamService kv8000ParamService;

    private final KV8000DataService kv8000DataService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    // 上次读取的数据缓存
    private final TimedCache<String, List<KV8000DataDTO>> kv8000DataCache = CacheUtil.newTimedCache(30000);

    @Override
    public void readParams(EquipmentDTO equip) {
        String kv8000Address = equip.getAddress();
        if (StrUtil.isEmpty(kv8000Address)) {
            return;
        }
        List<KV8000ParamPO> params = kv8000ParamService.getCacheByKvId(equip.getId());
        if (CollectionUtil.isEmpty(params)) {
            return;
        }

        String[] ipPort = kv8000Address.split(":");
        String ip = ipPort[0];
        int port = Integer.parseInt(ipPort[1]);
        try (KVDataReader reader = KVDataReader.getInstance(ip, port)) {
            Map<String, KV8000DataPO> dataMap = new HashMap<>();
            params.forEach(p -> {
                String address = p.getAddress();
                if (address.contains(".")) {
                    address = address.substring(0, address.indexOf("."));
                }
                if (dataMap.get(address) == null || p.getVariable().contains("DIM")) {
                    if (p.getDataType().startsWith("string[")) {
                        p.setDataType("string");
                    }
                    KVData data = reader.setAddress(address)
                            .setType(KVTypeFormat.getTypeFormat(p.getDataType()))
                            .setDataLength(p.getDataLength())
                            .read();
                    if (data != null) {
                        dataMap.put(address, data.convert(p));
                    }
                }
            });
            if (CollectionUtil.isNotEmpty(dataMap)) {
                List<KV8000DataPO> newDataList = new ArrayList<>();
                List<KV8000DataDTO> oldData = kv8000DataCache.get(equip.getId());
                if (CollectionUtil.isEmpty(oldData)) {
//                    oldData = kv8000DataService.getLatestParamDataByEquipId(equip.getId());
                    List<KV8000DataDTO> tempData=new ArrayList<>();
                    List<Future<Map<String,Object>>> checkList = new ArrayList<>();
                    List<KV8000ParamPO> special_params=kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getKvId,equip.getId()).list();
                    special_params.forEach(param->{
                        FutureTask<Map<String,Object>> task = new FutureTask<>(() -> {
                            Map<String,Object> mp=new HashMap<>();
                            KV8000DataPO temp=kv8000DataService.lambdaQuery().eq(KV8000DataPO::getParamId,param.getId())
                                    .orderByDesc(KV8000DataPO::getReadTime).last("limit 1").one();
                            mp.put("data",temp);
                            mp.put("param",param);
                            return mp;
                        });
                        threadPoolTaskExecutor.submit(task);
                        checkList.add(task);
                    });
                    checkList.forEach(task -> {
                        try {
                            Map<String,Object> mp=task.get();
                            KV8000DataPO kk= (KV8000DataPO) mp.get("data");
                            KV8000ParamPO param= (KV8000ParamPO) mp.get("param");
                            if(kk!=null){
                                KV8000DataDTO dto=BeanUtil.toBean(kk,KV8000DataDTO.class);
                                dto.setAddress(param.getAddress());
                                tempData.add(dto);
                            }
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        } catch (ExecutionException e) {
                            e.printStackTrace();
                        }
                    });
                    oldData = tempData;
                }
                List<KV8000DataDTO> finalOldData = oldData;
                Map<String, KV8000DataDTO> memo = new HashMap<>();
                List<KV8000DataDTO> tmpCache = new ArrayList<>();
                dataMap.forEach((address, value) -> {
                    // 记忆循环历史, 降复杂度
                    KV8000DataDTO odc = memo.get(address);
                    if (odc == null) {
                        // 查找对应的oldData
                        for (KV8000DataDTO od : finalOldData) {
                            String addr = od.getAddress();
                            // 警告类型地址去重,只需要保存一个.0的位置即可
                            if (od.getAddress().contains(".")) {
                                if (od.getAddress().contains(".0")) {
                                    addr = od.getAddress().substring(0, od.getAddress().indexOf("."));
                                } else {
                                    continue;
                                }
                            }
                            // 命中使用
                            if (addr.equals(address)) {
                                odc = od;
                            }
                            memo.put(addr, od);
                        }
                    }
                    // 缓存此次读取值
                    KV8000DataDTO cacheData = BeanUtil.toBean(value, KV8000DataDTO.class);
                    cacheData.setAddress(address);
                    tmpCache.add(cacheData);
                    if (odc == null || !value.getRawData().equals(odc.getRawData())) {
                        newDataList.add(value);
                    }
                });
                kv8000DataCache.put(equip.getId(), tmpCache);
                if (newDataList.size() > 0) {
                    kv8000DataService.saveBatch(newDataList);
                }
            }
        } catch (Exception e) {
            if (!"connect timed out".equals(e.getMessage())) {
                logger.error("Error reading KV8000 params! error:{}", e.getMessage(), e);
            }
        }
    }

    @Override
    public KV8000DataPO readParam(EquipmentDTO equip, KV8000ParamPO param) {
        String kv8000Address = equip.getAddress();
        String[] ipPort = kv8000Address.split(":");
        String ip = ipPort[0];
        int port = Integer.parseInt(ipPort[1]);
        try (KVDataReader reader = KVDataReader.getInstance(ip, port)) {
            String address = param.getAddress();
            if (address.contains(".")) {
                address = address.substring(0, address.indexOf("."));
            }

            if (param.getDataType().startsWith("string[")) {
                param.setDataType("string");
            }
            KVData data = reader.setAddress(address)
                    .setType(KVTypeFormat.getTypeFormat(param.getDataType()))
                    .setDataLength(param.getDataLength())
                    .read();
            if (data != null) {
                return data.convert(param);
            }
        } catch (Exception e) {
            if (!"connect timed out".equals(e.getMessage())) {
                logger.error("Error reading KV8000 params! error:{}", e.getMessage(), e);
            }
        }
        return null;
    }

    public List<KV8000DataDTO> getDataCacheByKvId(String kvId){
        List<KV8000DataDTO> data=kv8000DataCache.get(kvId);
        return data;
    }

}
