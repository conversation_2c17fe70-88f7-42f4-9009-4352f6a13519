package com.tdk.sae.collect.module.auth.cache;

import com.tdk.sae.collect.cache.redis.RedisCache;
import com.tdk.sae.collect.module.auth.param.LoginParams;
import org.springframework.stereotype.Component;

@Component
public class LoginParamsCache extends RedisCache<LoginParams> {

    private static final LoginParamsKeyDefine LOGIN_PARAMS_KEY_DEFINE = new LoginParamsKeyDefine();

    @Override
    protected void initKeyDefine() {
        keyDefine = LOGIN_PARAMS_KEY_DEFINE;
    }

}
