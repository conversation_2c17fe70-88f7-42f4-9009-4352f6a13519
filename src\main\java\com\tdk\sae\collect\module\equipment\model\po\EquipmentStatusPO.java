package com.tdk.sae.collect.module.equipment.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_equip_status")
public class EquipmentStatusPO extends DomainPO {

    private static final long serialVersionUID = 543072914893552277L;

    public EquipmentStatusPO() {
        super();
    }

    public EquipmentStatusPO(Long equipId, Integer equipStatus, String equipMsg, LocalDateTime checkTime) {
        super();
        this.equipId = equipId;
        this.equipStatus = equipStatus;
        this.equipMsg = equipMsg;
        this.checkTime = checkTime;
    }

    @TableField("equip_id")
    private Long equipId;

    @TableField(value = "equip_status")
    private Integer equipStatus;

    @TableField(value = "equip_msg")
    private String equipMsg;

    @TableField(value = "check_time")
    private LocalDateTime checkTime;

    @TableField(value = "synced")
    private Integer synced;

}
