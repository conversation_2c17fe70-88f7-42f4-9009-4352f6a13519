package com.tdk.sae.collect.module.abDim.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.abDim.mapper.ABAdjustRulesMapper;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustRulesPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ABAdjustRulesService extends ServiceImpl<ABAdjustRulesMapper, ABAdjustRulesPO> {
}
