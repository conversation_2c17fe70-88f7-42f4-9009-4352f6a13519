package com.tdk.sae.collect.domain.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import org.springframework.lang.Nullable;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Calendar;

public class Response<D> implements Serializable {

    private static final long serialVersionUID = 6669996669990L;

    private final String code;

    private final String msg;

    private final D data;

    private final String time;

    public Response(String code, String msg, D data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss").format(Calendar.getInstance().getTime());
    }

    private static ResponseBuilder code(ResponseCodeEnum code) {
        return new DefaultResponseBuilder(code);
    }

    public static <D> Response<D> ok() {
        return code(ResponseCodeEnum.OK).build();
    }

    public static <D> Response<D> ok(D data) {
        return code(ResponseCodeEnum.OK)
                .setData(data);
    }

    public static <D> Response<D> ok(String msg) {
        return code(ResponseCodeEnum.OK)
                .setMsg(msg).build();
    }

    public static <D> Response<D> ok(String msg, D data) {
        return code(ResponseCodeEnum.OK)
                .setMsg(msg)
                .setData(data);
    }

    public static <D> Response<D> error(@Nonnull String msg) {
        return code(ResponseCodeEnum.FAIL)
                .setMsg(msg).build();
    }

    public static <D> Response<D> error(@Nonnull String msg, D data) {
        return code(ResponseCodeEnum.FAIL)
                .setMsg(msg)
                .setData(data);
    }

    public static <D> Response<D> error(ResponseCodeEnum code) {
        return code(code).build();
    }

    public static <D> Response<D> error(ResponseCodeEnum code, @Nonnull String msg) {
        return code(code)
                .setMsg(msg)
                .build();
    }

    public static <D> Response<D> error(ResponseCodeEnum code, @Nonnull String msg, D data) {
        return code(code)
                .setMsg(msg)
                .setData(data);
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public D getData() {
        return data;
    }

    public String getTime() {
        return time;
    }

    @JsonIgnore
    public boolean isOk(){
        return ResponseCodeEnum.OK.getCode().equals(this.code);
    }

    public interface ResponseBuilder {

        ResponseBuilder setMsg(String msg);

        <D> Response<D> build();

        <D> Response<D> setData(@Nullable D data);
    }

    private static class DefaultResponseBuilder implements ResponseBuilder {

        private final String code;

        private String msg;

        public DefaultResponseBuilder(ResponseCodeEnum code) {
            this.code = code.getCode();
            this.msg = code.getMessage();
        }

        @Override
        public DefaultResponseBuilder setMsg(String msg) {
            this.msg = msg;
            return this;
        }

        @Override
        public <D> Response<D> build() {
            return setData(null);
        }

        @Override
        public <D> Response<D> setData(@Nullable D data) {
            return new Response<>(this.code, this.msg, data);
        }

    }
}

