package com.tdk.sae.collect.module.auth.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_permission")
public class PermissionPO extends DomainPO {

    private static final long serialVersionUID = 1L;

    @TableField("name")
    private String name;

    @TableField("value")
    private String value;

    @TableField("menu_id")
    private Long menuId;

    @TableField("sort")
    private Integer sort;

}
