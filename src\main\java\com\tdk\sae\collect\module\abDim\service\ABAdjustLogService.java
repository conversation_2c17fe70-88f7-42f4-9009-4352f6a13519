package com.tdk.sae.collect.module.abDim.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.abDim.mapper.ABAdjustLogMapper;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDTO;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDetailDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ABAdjustLogService extends ServiceImpl<ABAdjustLogMapper, ABAdjustLogPO> {

    private final ABAdjustLogDetailService icsAdjustLogDetailService;

    @Transactional
    public void saveLogs(ABAdjustLogDTO adjustLog) {
        ABAdjustLogPO icsAdjustLogPO = BeanUtil.copyProperties(adjustLog, ABAdjustLogPO.class, "logDetails");
        // TODO: adjustType is null problem need to be fix
        if (icsAdjustLogPO.getAdjustType() == null || icsAdjustLogPO.getAdjustType() > 2) {
            return;
        }
        if (icsAdjustLogPO.getEndTime() == null) {
            icsAdjustLogPO.setEndTime(LocalDateTime.now());
        }
        List<ABAdjustLogDetailDTO> logDetails = adjustLog.getLogDetails();
        if (logDetails.size() > 0) {
            icsAdjustLogDetailService.saveDetails(logDetails);
        }
        this.save(icsAdjustLogPO);
    }

}
