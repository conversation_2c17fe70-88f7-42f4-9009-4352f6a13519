package com.tdk.sae.collect.module.abDim.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_ab_adjust_rules_apply")
public class ABAdjustRulesApplyPO extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "rule_id")
    private Long ruleId;

    @TableField(value = "rule")
    private String rule;

    @TableField(value = "enabled")
    private Integer enabled;

}
