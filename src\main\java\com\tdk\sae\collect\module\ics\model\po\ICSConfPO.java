package com.tdk.sae.collect.module.ics.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ics_conf")
public class ICSConfPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "equip_id")
    private Long equipId;

    // ED1, ED2, EX1, EX2, EY1, EY2
    @TableField(value = "conf_name")
    private String confName;

    // USL, LSL, UCL, LCL, TARGET, ENABLE
    @TableField(value = "conf_key")
    private String confKey;

    @TableField(value = "conf_value")
    private String confValue;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "remark")
    private String remark;

}
