package com.tdk.sae.collect.module.equipment.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.equipment.mapper.EquipmentMapper;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class EquipmentService extends ServiceImpl<EquipmentMapper, EquipmentPO> {

}
