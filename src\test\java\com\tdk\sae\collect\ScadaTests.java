package com.tdk.sae.collect;

import com.scada.dbcomm.DBComm;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.scada.ScadaDataReader;
import com.tdk.sae.collect.module.plc.scada.ScadaDataWriter;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

//@SpringBootTest
public class ScadaTests {

    public static void main(String[] args) {
        DBComm comm = new DBComm();
        long initial = comm.Initial();
        boolean ConnectRemoteServer = comm.ConnectRemoteServer(initial,"192.168.1.11", 2006, "", false);
        if(ConnectRemoteServer)
        {
            System.out.println("连接远程数据库成功");
        }
        else
        {
            System.out.println("连接远程数据库失败");
        }
        try {
            Thread.sleep(1000);
        } catch (InterruptedException ignore) {}

        boolean IsConnected = comm.IsConnected(initial);
        if(IsConnected)
        {
            System.out.println("当前已连接");
        }
        else
        {
            System.out.println("当前未连接");
        }

        System.out.println("============获取数据库点的个数=============");
        //获取数据库点的个数
        int nTagCount = comm.GetTagCount(initial);
        System.out.println("当前数据库点的个数是"+nTagCount);

        System.out.println("===========获取数据点类型个数==============");
        //获取数据点类型个数
        int nTagTypeCount = comm.GetTypeCount(initial);
        System.out.println("当前数据点类型个数是"+nTagTypeCount);

        System.out.println("============获取全部点名=============");
        //获取全部点名
        String[] strtagname = comm.GetAllTagName(initial);
        if (strtagname != null)
        {
            for(String _strtagname:strtagname)
            {
                System.out.println(_strtagname+",");
            }
        }

        System.out.println("============获取所有点值=============");
        String[] strTagData = comm.GetData(initial,strtagname);
        if (strTagData != null)
        {
            for(String _strtagname:strTagData)
            {
                System.out.println(_strtagname+",");
            }
        }

        System.out.println("============获取单个点值=============");
        System.out.println("start time:" + LocalDateTime.now());
        String[] tagData = new String[1];
        tagData[0] = "GLUE_TIME1";
        strTagData = comm.GetData(initial,tagData);
        if (strTagData != null)
        {
            String rdata = strTagData[0].split(",")[1];
            String rdatas = new BigDecimal(rdata).multiply(new BigDecimal("0.001")).toPlainString();
            System.out.println(rdatas);

            // GLUE_TIME1,302.00
            for(String _strtagname:strTagData)
            {
                System.out.println(_strtagname+",");
            }
        }
        System.out.println("end time:" + LocalDateTime.now());

        System.out.println("=============设置当前点的值============");
        //设置当前点的值
        Double inputVal = new BigDecimal("0.303").doubleValue();
        Integer wVal = new BigDecimal(inputVal).divide(new BigDecimal("0.001"), RoundingMode.HALF_UP).intValue();
        System.out.println(wVal);
        comm.SetData(initial,"GLUE_TIME1", wVal);

        System.out.println("=============断开连接============");
        //断开连接
        comm.DisConnect(initial);
    }

//    public static void main(String[] args) {
//        System.out.println("Test read/write on GLUE_TIME1......");
//        ScadaDataReader reader = new ScadaDataReader("192.168.1.10", 2006);
//        KVData data = reader.setDataPoint("GLUE_TIME1")
//                .setDataType("real")
//                .setScale("0.001")
//                .read();
//
//        System.out.println(data);
//
//        ScadaDataWriter writer = new ScadaDataWriter("192.168.1.10", 2006);
//        writer.setDataPoint("GLUE_TIME1")
//                .setDataType("real")
//                .setScale("0.001")
//                .write(0.321);
//
//        System.out.println("Test read/write on SBB_NOZZLE_LIFE_A......");
//        reader = new ScadaDataReader("192.168.1.10", 2006);
//        data = reader.setDataPoint("SBB_NOZZLE_LIFE_A")
//                .setDataType("dint")
//                .setScale(null)
//                .read();
//
//        System.out.println(data);
//
//        writer = new ScadaDataWriter("192.168.1.10", 2006);
//        writer.setDataPoint("SBB_NOZZLE_LIFE_A")
//                .setDataType("dint")
//                .setScale(null)
//                .write(Long.parseLong(data.getTranslatedData()));
//    }

}
