package com.tdk.sae.collect.module.plc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;

import com.tdk.sae.collect.module.plc.mapper.KV8000ParamFormulaMapper;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamFormulaPO;
import lombok.RequiredArgsConstructor;
import org.nfunk.jep.JEP;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class KV8000ParamFormulaService extends ServiceImpl<KV8000ParamFormulaMapper, KV8000ParamFormulaPO> {
    private final SystemProperties systemProperties;

    public void calculateRuleAdvice(AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice) {
        KV8000ParamFormulaPO formulaPO = lambdaQuery()
                .eq(KV8000ParamFormulaPO::getParamId, ruleAdvice.getKv8000Param().getId()).last("limit 1").one();
        if (formulaPO == null) {
            return;
        }
        String formula = formulaPO.getFormula();
        if (StrUtil.isEmpty(formula)) {
            return;
        }
        Double calculatedVal = null;
        try {
            JEP jep = new JEP();
            jep.addVariable("x", ruleAdvice.getDiffValue().doubleValue());
            jep.parseExpression(formula);
            calculatedVal = jep.getValue();
        } catch (Exception ignore) {}
        if (calculatedVal != null) {
            BigDecimal newValue = new BigDecimal(calculatedVal).setScale(3, RoundingMode.HALF_UP);
            ruleAdvice.setNewValue(ruleAdvice.getLatestReadData().add(newValue));
            ruleAdvice.setNewValueStr(ruleAdvice.getNewValue().toPlainString());
        }
    }

    public void calculateABRuleAdvice(com.tdk.sae.collect.module.abDim.model.adjust.AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice,String head) {
        KV8000ParamFormulaPO formulaPO = lambdaQuery()
                .eq(KV8000ParamFormulaPO::getParamId, ruleAdvice.getKv8000Param().getId()).eq(KV8000ParamFormulaPO::getDescription,head).last("limit 1").one();
        if (formulaPO == null) {
            return;
        }
        String formula = formulaPO.getFormula();
        if (StrUtil.isEmpty(formula)) {
            return;
        }
        Double calculatedVal = null;
        try {
            JEP jep = new JEP();
            jep.addVariable("x", ruleAdvice.getDiffValue().doubleValue());
            jep.parseExpression(formula);
            calculatedVal = jep.getValue();
        } catch (Exception ignore) {}
        if (calculatedVal != null) {
            BigDecimal newValue = new BigDecimal(calculatedVal).setScale(2, RoundingMode.HALF_UP);
            String code = systemProperties.getClientCode();
            if(code.equals("4D-GPM2")){
                ruleAdvice.setNewValue(ruleAdvice.getLatestReadData().add(newValue));
            }else{
                ruleAdvice.setNewValue(ruleAdvice.getLatestReadData().add(newValue).setScale(0, RoundingMode.HALF_UP));
            }
            ruleAdvice.setNewValueStr(ruleAdvice.getNewValue().toPlainString());
        }
    }
}
