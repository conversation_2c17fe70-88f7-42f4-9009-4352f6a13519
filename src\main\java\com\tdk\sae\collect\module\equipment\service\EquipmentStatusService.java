package com.tdk.sae.collect.module.equipment.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.equipment.mapper.EquipmentStatusMapper;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentStatusPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class EquipmentStatusService extends ServiceImpl<EquipmentStatusMapper, EquipmentStatusPO> {

    private final EquipmentStatusMapper equipmentStatusMapper;

    public List<EquipmentStatusPO> getLatestStatus() {
        return equipmentStatusMapper.getLatestStatus();
    }

}
