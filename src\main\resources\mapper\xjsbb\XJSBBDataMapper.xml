<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.xjsbb.mapper.XJSBBDataMapper">

    <select id="getLatestData" resultType="com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBDataDTO">
        select
            t1.*,t3.variable
        from t_biz_xjsbb_data t1
        inner join (
            select
                param_id,
                max(read_time) as read_time
            from t_biz_xjsbb_data
            group by param_id
        ) t2 on t1.param_id = t2.param_id and t1.read_time = t2.read_time
        left join t_biz_xjsbb_param t3 on t1.param_id = t3.id
    </select>

</mapper>
