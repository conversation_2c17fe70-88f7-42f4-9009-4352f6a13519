package com.tdk.sae.collect.module.abDim.adjust.rules;

import com.tdk.sae.collect.module.abDim.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.abDim.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDetailDTO;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component("ABICSAutoAdjustOnOffRule")
public class ABAutoAdjustOnOffRule extends AbstractRulesHandler {

    @Override
    public void checkKV8000ParamCache() {
        KV8000ParamCache.computeIfAbsent(getEquipment().getId(), k -> new HashMap<>());
    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("ICSAutoAdjustOnOffRule");
        if (isDisabled()) {
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
            ABAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
            detail.setApplyId(getRulePopulate().getApplyId());
            detail.setLogMsg("Auto adjust stopped. Cause: Auto adjust is disabled");
            detail.setLogLevel(0);
            return advice;
        }
        advice.getAdjustLog().setEquipId(getRulePopulate().getEquipId());
        return doNextRule(advice);
    }
}
