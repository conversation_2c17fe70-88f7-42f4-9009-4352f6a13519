package com.tdk.sae.collect.module.xjsbb.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_xjsbb_log")
public class XJSBBLogPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "file_path")
    private String filePath;

    @TableField(value = "raw_data")
    private String rawData;

    @TableField(value = "var_name")
    private String varName;

    @TableField(value = "var_value")
    private String varValue;

    @TableField(value = "log_time")
    private LocalDateTime logTime;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "synced")
    private Integer synced = 0;

}
