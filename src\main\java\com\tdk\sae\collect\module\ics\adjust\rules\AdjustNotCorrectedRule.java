package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.adjust.utils.ReachFullDropHalfList;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogTrayMeanDTO;
import com.tdk.sae.collect.module.ics.service.ICSAutoAdjustService;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component("AdjustNotCorrectedRule")
public class AdjustNotCorrectedRule extends AbstractRulesHandler {


    private final KV8000ParamService kv8000ParamService;

    private final SpecInfo specInfo;

    private static final String KV_ADDRESS_REPRESENT_STOP = "W_STOP";

    @Override
    public void checkKV8000ParamCache() {
        KV8000ParamPO param = KV8000ParamCache.get(getEquipment().getId()).get(KV_ADDRESS_REPRESENT_STOP);
        if (param == null) {
            List<KV8000ParamPO> paramList = kv8000ParamService.getCacheByEquipId(getEquipment().getId());
            List<KV8000ParamPO> filteredList = paramList.stream().filter(p -> p.getAddressRepresent().equals(KV_ADDRESS_REPRESENT_STOP)).collect(Collectors.toList());
            if (filteredList.size() > 0) {
                KV8000ParamCache.get(getEquipment().getId()).put(KV_ADDRESS_REPRESENT_STOP, filteredList.get(0));
            }
        }
    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
        if (isDisabled()) {
            return doNextRule(advice);
        }
        Map<String,Integer> continueMap = ICSAutoAdjustService.CONTINUE_CACHE.get(getEquipment().getId());
        Map<String, ReachFullDropHalfList<ICSLogTrayMeanDTO>> paramDataMap = ICSAutoAdjustService.DATA_CACHE.get(getEquipment().getId());
        continueMap.forEach((param,value) -> {

            ReachFullDropHalfList<ICSLogTrayMeanDTO> halfList = paramDataMap.get(param);
            //初始化为空则跳过
            if (halfList != null){
                //获取第一个点，判断是否在黄线范围
                ICSLogTrayMeanDTO icsLogTrayMeanDTO = halfList.getList().get(0);
                BigDecimal meanValue = icsLogTrayMeanDTO.getMeanValue();
                Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(getEquipment().getId());
                BigDecimal specUpperLimit = specMap.get(param).get("ADJUST_UPPER_SPEC_LIMIT");
                BigDecimal specLowerLimit = specMap.get(param).get("ADJUST_LOWER_SPEC_LIMIT");
                BigDecimal controlUpperLimit = specMap.get(param).get("ADJUST_UPPER_CONTROL_LIMIT");
                BigDecimal controlLowerLimit = specMap.get(param).get("ADJUST_LOWER_CONTROL_LIMIT");
                boolean isInUpperRange = meanValue.compareTo(controlUpperLimit) >= 0 && meanValue.compareTo(specUpperLimit) <= 0;
                boolean isInLowerRange = meanValue.compareTo(specLowerLimit) >= 0 && meanValue.compareTo(controlLowerLimit) <= 0;
                if (value >= 3){
                    continueMap.put(param, 0); // 重新设置 value 为 0
                    if ((isInUpperRange|| isInLowerRange)){
                        AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice;
                        if (advice.getRuleAdvices().get("AdjustNotCorrectedRule") == null) {
                            advice.newRuleAdvice("AdjustNotCorrectedRule");
                        }
                        // 记录log, 并写入警告信号
                        ICSAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
                        detail.setApplyId(getRulePopulate().getApplyId());
                        detail.setParamId(KV8000ParamCache.get(getEquipment().getId()).get(KV_ADDRESS_REPRESENT_STOP).getId());
                        detail.setMeanId(icsLogTrayMeanDTO.getId());
                        detail.setLogLevel(3);
                        detail.setLogMsg(StrUtil.format("Multiple adjustments have not been corrected! mean value: {}",
                                meanValue));
//                        detail.setFromValue("0");
//                        detail.setToValue("1");
                        detail.setWriteTime(LocalDateTime.now());
                    }

                }
            }
        });
        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> ruleAdvices = advice.getRuleAdvices().get("AdjustNotCorrectedRule");
        if (ruleAdvices != null) {
            advice.getAdjustLog().setAdjusted(1);
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
            AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice = ruleAdvices.get(0);
            ruleAdvice.setEquipment(getEquipment());
            KV8000ParamPO po = KV8000ParamCache.get(getEquipment().getId()).get(KV_ADDRESS_REPRESENT_STOP);
            ruleAdvice.setKv8000Param(BeanUtil.copyProperties(po, KV8000ParamDTO.class));
            ruleAdvice.setAddressRepresent(ruleAdvice.getKv8000Param().getAddressRepresent());
            ruleAdvice.setNewValueStr("1");
            return advice;
        }
        return doNextRule(advice);
    }
}
