DROP PROCEDURE IF EXISTS add_column_if_not_exists;$$
CREATE PROCEDURE add_column_if_not_exists()
BEGIN
    DECLARE column_exists INT DEFAULT 0;

    -- 检查列是否存在
    SELECT COUNT(*)
    INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 't_biz_ab_log_tray_mean'
      AND COLUMN_NAME = 'head';

    -- 如果列不存在，则添加列
    IF column_exists = 0 THEN
        ALTER TABLE t_biz_ab_log_tray_mean
        ADD COLUMN head VARCHAR(10) NULL;
    END IF;
END$$

-- 调用存储过程
CALL add_column_if_not_exists()$$
DROP PROCEDURE IF EXISTS add_column_if_not_exists;$$