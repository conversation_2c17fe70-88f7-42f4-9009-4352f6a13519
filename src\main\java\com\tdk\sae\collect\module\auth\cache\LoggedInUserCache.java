package com.tdk.sae.collect.module.auth.cache;

import com.tdk.sae.collect.cache.redis.RedisCache;
import com.tdk.sae.collect.module.auth.dto.LoggedInUser;
import org.springframework.stereotype.Component;

@Component
public class LoggedInUser<PERSON>ache extends RedisCache<LoggedInUser> {

    private static final LoggedInUserKeyDefine LOGGED_IN_USER_KEY_DEFINE = new LoggedInUserKeyDefine();

    @Override
    protected void initKeyDefine() {
        keyDefine = LOGGED_IN_USER_KEY_DEFINE;
    }

}
