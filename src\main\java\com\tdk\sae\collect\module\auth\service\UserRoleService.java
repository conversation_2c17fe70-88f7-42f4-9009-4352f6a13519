package com.tdk.sae.collect.module.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.tdk.sae.collect.module.auth.mapper.UserRoleMapper;
import com.tdk.sae.collect.module.auth.po.UserRolePO;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class UserRoleService extends ServiceImpl<UserRoleMapper, UserRolePO> {

    private final UserRoleMapper userRoleMapper;

    @Nullable
    public Collection<Long> getRoleByUserId(@NotNull Long userId) {
        Assert.notNull(userId, "用户id不为空");
        LambdaQueryWrapper<UserRolePO> query = Wrappers.lambdaQuery(UserRolePO.class)
                .eq(UserRolePO::getUserId, userId).eq(UserRolePO::getIsDeleted, 0).select(UserRolePO::getRoleId);
        List<UserRolePO> userRolePOList = userRoleMapper.selectList(query);
        if (CollectionUtils.isEmpty(userRolePOList)) {
            return Lists.newArrayList();
        }
        return userRolePOList.parallelStream().map(UserRolePO::getRoleId).collect(Collectors.toSet());
    }

}
