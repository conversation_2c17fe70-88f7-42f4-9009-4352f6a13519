package com.tdk.sae.collect.tasks;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.mapper.ICSHotLaserLogMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSHotLaserLog;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import com.tdk.sae.collect.tasks.enums.LncmHotLaserEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@RequiredArgsConstructor
@Component
public class LncmHotLaserReadService {
    private static final Logger logger = LoggerFactory.getLogger(LncmHotLaserReadService.class);

    @Resource(name = "${system.read-type}")
    private IReadPlcService readPlcService;
    private final SystemProperties systemProperties;
    private final KV8000ParamService kv8000ParamService;
    @Resource(name = "${system.write-type}")
    private IWritePlcService kv8000WriteService;
    private final ICSHotLaserLogMapper icsHotLaserLogMapper;

    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void readRelatedParams(){
        try{
            List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
            List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_TREND.getName());
            //判断是否有设备
            if(CollectionUtil.isEmpty(icsEquips)){
                return;
            }
            for (LncmHotLaserEnum.GroupType LncmHotLaser : LncmHotLaserEnum.GroupType.values()) {
                KV8000ParamPO param = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getVariable, LncmHotLaser.getValue()).last("limit 1").one();
                if (null == param) {
                    logger.warn("No parameter found for " + LncmHotLaser.getValue());
                    continue;
                }
                KV8000DataPO Completed_Flag = readPlcService.readParam(icsLogEquips.get(0), param);
                if(Completed_Flag.getReadData().equals("1")){
                    EquipmentDTO e = icsEquips.stream().filter(i-> i.getClientCode().contains(LncmHotLaser.getDirection().getValue())).findFirst().orElse(null);
                    ICSHotLaserLog log = new ICSHotLaserLog();
                    if(e!=null){
                        log.setEquipId(Long.parseLong(e.getId()));
                    }
                    List<LncmHotLaserEnum> readParams = LncmHotLaserEnum.getEnumsByFlag(LncmHotLaser);
                    for (LncmHotLaserEnum readParam : readParams) {
                        KV8000ParamPO readParamPO = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getVariable, readParam.getName()).last("limit 1").one();
                        KV8000DataPO readData = readPlcService.readParam(icsLogEquips.get(0), readParamPO);
                        // 根据不同参数名设置不同的温度字段
                        switch (readParam.getName()) {
                            case "Temp_HotAir1_L":
                            case "Temp_HotAir1_R":
                            case "Temp_Laser1_L":
                            case "Temp_Laser1_R":
                                log.setTemperature1(readData.getReadData());
                                break;

                            case "Temp_HotAir2_L":
                            case "Temp_HotAir2_R":
                            case "Temp_Laser2_L":
                            case "Temp_Laser2_R":
                                log.setTemperature2(readData.getReadData());
                                break;

                            case "Temp_HotAir3_L":
                            case "Temp_HotAir3_R":
                            case "Temp_Laser3_L":
                            case "Temp_Laser3_R":
                                log.setTemperature3(readData.getReadData());
                                break;

                            case "Temp_HotAir4_L":
                            case "Temp_HotAir4_R":
                            case "Temp_Laser4_L":
                            case "Temp_Laser4_R":
                                log.setTemperature4(readData.getReadData());
                                break;

                            case "Temp_HotAir5_L":
                            case "Temp_HotAir5_R":
                            case "Temp_Laser5_L":
                            case "Temp_Laser5_R":
                                log.setTemperature5(readData.getReadData());
                                break;

                            case "Temp_Laser6_L":
                            case "Temp_Laser6_R":
                                log.setTemperature6(readData.getReadData());
                                break;

                            case "Temp_Laser7_L":
                            case "Temp_Laser7_R":
                                log.setTemperature7(readData.getReadData());
                                break;

                            case "Temp_Laser8_L":
                            case "Temp_Laser8_R":
                                log.setTemperature8(readData.getReadData());
                                break;

                            case "Temp_Laser9_L":
                            case "Temp_Laser9_R":
                                log.setTemperature9(readData.getReadData());
                                break;

                            case "Temp_Laser10_L":
                            case "Temp_Laser10_R":
                                log.setTemperature10(readData.getReadData());
                                break;

                            default:
                                logger.warn("Unknown parameter: " + readParam.getName());
                                break;

                        }
                    }
                    log.setDescription(LncmHotLaser.getValue());
                    icsHotLaserLogMapper.insert(log);
                    //将标志位设置为0
                    boolean flag=kv8000WriteService.write(icsLogEquips.get(0), BeanUtil.copyProperties(param, KV8000ParamDTO.class),"0");
                }
            }
        }catch (Exception e){
            logger.error("Read Lncm Hot Laser Data Error", e);
        }
    }

}
