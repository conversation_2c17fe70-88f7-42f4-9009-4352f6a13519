package com.tdk.sae.collect.module.abDim.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.init.utils.FileMonitor;
import com.tdk.sae.collect.module.abDim.mapper.ABLogMapper;
import com.tdk.sae.collect.module.abDim.model.dto.ABLogDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
public class ABLogService extends ServiceImpl<ABLogMapper,ABLogPO>  implements ILogFileService {

    private static final String REGEX_HEADER = "Order,Result,.*,Head,HGA,Time";
    private static final String REGEX_LOG_ROW = ".*,\\d{4}-\\d{1,2}-\\d{1,2} .*\n|.*\r\n";

    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        ABLogPO lastLog = getOne(Wrappers.lambdaQuery(ABLogPO.class)
                .eq(ABLogPO::getEquipId, equipId)
                .eq(ABLogPO::getFilePath, filePath)
                .orderByDesc(ABLogPO::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, ABLogDTO.class);
    }


    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        return saveBatch(BeanUtil.copyToList(newLogs, ABLogPO.class), newLogs.size());
    }

    /**
     * read file to Log records
     * @param f file
     * @return Log records
     */
    public List<ABLogDTO> digestLogFile(Long equipId, File f) {
        return digestLogFile(equipId, f, null);
    }

    /**
     * read file to Log records which logTime is greater(>) than parameter 'logTime'
     * @param f file
     * @param logTime logTime
     * @return Log records
     */
    public List<ABLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
        boolean flag= FileMonitor.judgeFileDateIsInRange(f.getPath());
        if(!flag){
            Console.log("file:{},标志：{}", f.getPath(),flag);
            return new ArrayList<>();
        }
        String fileContent = FileUtil.readString(f, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        String[] headers = splitLogHeader(fileContent);
        if (headers == null) {
            return new ArrayList<>();
        }
        return doDigest(equipId, f.getPath(), fileContent, headers, logTime);
    }

    private String[] splitLogHeader(String fileContent) {
        Pattern pattern_t = Pattern.compile(REGEX_HEADER);
        Matcher matcher_t = pattern_t.matcher(fileContent);
        String[] headers = null;
        if(matcher_t.find()){
            String s = matcher_t.group();
            headers = s.split(",");
        }
        return headers;
    }

    private List<ABLogDTO> doDigest(Long equipId, String filePath, String fileContent, String[] headers, LocalDateTime logTime) {
        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);

        String headerDescription = String.join(",", headers);
        List<ABLogDTO> logDTOList = new ArrayList<>();
        while (matcher.find()) {
            try {
                ABLogDTO logDTO = new ABLogDTO();
                logDTO.setFilePath(filePath);
                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
                logDTO.setRawData(rawData);
                logDTO.setEquipId(equipId);
                logDTO.setDescription(headerDescription);

                String[] columns = rawData.split(",");
                StringBuilder logVal = new StringBuilder();
                boolean isFilled = true;
                for (int i = 0; i < headers.length; i++) {
                    String header = headers[i];
                    String column = columns[i].trim();
                    if (header.equals(column)) {
                        isFilled = false;
                        break;
                    }
                    switch (header) {
                        case "Time":
                            column = StrUtil.padAfter(column, 23, "0");
                            logDTO.setLogTime(LocalDateTimeUtil.parse(column, "yyyy-MM-dd HH:mm:ss:SSS"));
                            break;
                        case "POS":
                            logDTO.setPos(column);
                            break;
                        case "Master":
                            logDTO.setMaster(column);
                            break;
                        case "A_Dim(um)":
                            logVal.append(column).append(",");
                            break;
                        case "B_Dim(um)":
                            logVal.append(column);
                            logDTO.setLogValue(logVal.toString());
                            break;
                        case "Result":
                            logDTO.setResult(column);
                            break;
                    }
                }
                if (logTime != null && logDTO.getLogTime().compareTo(logTime) <= 0) {
                    continue;
                }
                if (isFilled) {
                    logDTOList.add(logDTO);
                }
            } catch (Exception ignore) {}
        }
        return logDTOList;
    }
}
