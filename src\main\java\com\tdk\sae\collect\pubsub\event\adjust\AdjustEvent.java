package com.tdk.sae.collect.pubsub.event.adjust;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class AdjustEvent extends ApplicationEvent {

    private String parameter;
    private String equid;

    public AdjustEvent(Object source, String parameter,String equid) {
        super(source);
        this.parameter = parameter;
        this.equid = equid;
    }
}