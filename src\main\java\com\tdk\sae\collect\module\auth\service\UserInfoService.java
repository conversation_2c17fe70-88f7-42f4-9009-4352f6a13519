package com.tdk.sae.collect.module.auth.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.module.auth.dto.UserDTO;
import com.tdk.sae.collect.module.auth.po.PermissionPO;
import com.tdk.sae.collect.module.auth.po.RolePO;
import com.tdk.sae.collect.module.auth.po.UserPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class UserInfoService {

    private final UserAccountService accountService;

    private final UserRoleService userRoleService;

    private final RoleService roleService;

    private final RolePermissionService rolePermissionService;

    private final PermissionService permissionService;

    public UserDTO loadUserByUsername(String username) {
        // 获取用户
        UserPO userPO = accountService.getUserByEmpNo(username);
        if (null == userPO) {
            return null;
        }
        UserDTO user = BeanUtil.toBean(userPO, UserDTO.class);
        // 获取角色
        Collection<Long> roleIds = userRoleService.getRoleByUserId(user.getId());
        if (CollectionUtil.isEmpty(roleIds)) {
            return user;
        }
        List<RolePO> roles = roleService.getEnabledRoleByIds(roleIds);
        if (CollectionUtil.isEmpty(roles)) {
            return user;
        }
        Map<Long, String> roleMap = roles.parallelStream().collect(Collectors.toMap(RolePO::getId, RolePO::getRole));
        user.setRole(roleMap.values());
        user.setRoleIds(roleMap.keySet());
        // 获取权限
        Map<Long, List<Long>> permission = rolePermissionService.getPermissionIdByRoleIds(roleIds);
        if (CollectionUtil.isEmpty(permission)) {
            return user;
        }
        Set<Long> permissionIds = permission.values().stream().flatMap(List::stream).collect(Collectors.toSet());
        List<PermissionPO> permissionPOS = permissionService.getEnabledPermissionByIds(permissionIds);
        if (CollectionUtil.isEmpty(permissionPOS)) {
            return user;
        }
        Map<Long, String> permissionMap = permissionPOS.parallelStream().collect(Collectors.toMap(PermissionPO::getId, PermissionPO::getValue));
        user.setPermission(permissionMap.values());
        user.setPermissionIds(permissionMap.keySet());
        return user;
    }

}
