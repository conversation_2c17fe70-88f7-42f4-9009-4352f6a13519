package com.tdk.sae.collect.module.ics.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class ICSLogTrayMeanDTO implements Serializable {

    private Long id;

    private Long equipId;

    private String parameter;

    private BigDecimal meanValue;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime calculatedTime;

    private Integer isDeleted;

    private Integer synced;
}
