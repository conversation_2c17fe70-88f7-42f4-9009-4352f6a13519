package com.tdk.sae.collect.module.plc.kv8000;

import java.util.stream.Stream;

public enum KVTypeFormat {

    BOOL("bool", 1),
    UINT("uint", 1),
    DINT("dint", 2),
    REAL("real", 2),
    STRING("string", -1);

    private final String typeFormat;
    private final int defaultLength;

    KVTypeFormat(String typeFormat, int defaultLength) {
        this.typeFormat = typeFormat;
        this.defaultLength = defaultLength;
    }

    @Override
    public String toString() {
        return this.typeFormat;
    }

    public int getDefaultLength() {
        return this.defaultLength;
    }

    public static KVTypeFormat getTypeFormat(String typeFormat) {
        return Stream.of(KVTypeFormat.class.getEnumConstants())
                .filter(item -> item.toString().equals(typeFormat))
                .findFirst().orElse(null);
    }

    public static class Constants {
        public static final String BOOL = "bool";
        public static final String UINT = "uint";
        public static final String DINT = "dint";
        public static final String REAL = "real";
        public static final String STRING = "string";
    }

}
