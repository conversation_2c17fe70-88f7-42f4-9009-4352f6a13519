package com.tdk.sae.collect.module.common.service.impl;

import cn.hutool.core.lang.Console;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class MQProducerService {

    private final static Logger logger = LoggerFactory.getLogger(MQProducerService.class);

    @Value("${rocketmq.producer.send-message-timeout}")
    private Integer messageTimeOut;

    // 建议正常规模项目统一用一个TOPIC
    private static final String topic = "AUTOMATION";

    private final RocketMQTemplate rocketMQTemplate;


    public <T> SendResult sendSyncPayload(String tag, T payload) {
        SendResult sendResult = rocketMQTemplate.syncSend(topic + ":" + tag, MessageBuilder.withPayload(payload).build());
        Console.log(sendResult.getSendStatus());
        // TODO: log send record
        return sendResult;
    }

}
