package com.tdk.sae.collect.module.plc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.kv8000.rw.KVDataWriter;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service("kv8000write")
public class KV8000WriteService implements IWritePlcService {

    private final Logger logger = LoggerFactory.getLogger(KV8000WriteService.class);

    @Value("${system.auto-mode}")
    private String autoMode;

    @Override
    public Boolean write(EquipmentDTO equip, KV8000ParamDTO param, String value) {
        if (!"PRODUCT".equals(autoMode)) {
            return false;
        }
        String kv8000Address = equip.getAddress();
        if (StrUtil.isEmpty(kv8000Address)) {
            return false;
        }
        if (param.getDataType().startsWith("string[")) {
            param.setDataType(KVTypeFormat.Constants.STRING);
        }
        Object valueObj = null;
        switch (param.getDataType()) {
            case KVTypeFormat.Constants.BOOL:
            case KVTypeFormat.Constants.UINT:
            case KVTypeFormat.Constants.DINT:
                try {
                    BigDecimal tmp= new BigDecimal(value).setScale(0, RoundingMode.HALF_UP);
                    valueObj = Long.parseLong(String.valueOf(tmp));
                } catch (NumberFormatException e) {
                    logger.error("Error parsing value type:" + param.getDataType() + ", address:" + param.getAddress() + ", value:" + value);
                    valueObj = new BigDecimal(value).setScale(0, RoundingMode.HALF_UP).longValue();
                }
                break;
            case KVTypeFormat.Constants.REAL:
                valueObj = new BigDecimal(value).doubleValue();
                break;
            case KVTypeFormat.Constants.STRING:
                byte[] bytes = value.getBytes(StandardCharsets.US_ASCII);
                List<Integer> strValList = new ArrayList<>();
                for (int i = 0; i < bytes.length; i+=2) {
                    int higherByte = bytes[i];
                    int paddingCount = i + 1;
                    int lowerByte = 0;
                    if (paddingCount < bytes.length) {
                        lowerByte = bytes[i + 1];
                    }
                    String hByteStr = Integer.toBinaryString(higherByte);
                    hByteStr = StringUtils.leftPad(hByteStr, 8, '0');
                    String lByteStr = Integer.toBinaryString(lowerByte);
                    lByteStr = StringUtils.leftPad(lByteStr, 8, '0');
                    strValList.add(Integer.parseInt(hByteStr + lByteStr, 2));
                }
                // 用实际长度覆盖最大长度
                param.setDataLength(strValList.size());
                valueObj = strValList.toArray();
                break;
            default:
                throw new IllegalArgumentException("Type not found!");
        }
        String[] ipPort = kv8000Address.split(":");
        String ip = ipPort[0];
        int port = Integer.parseInt(ipPort[1]);
        try (KVDataWriter writer = KVDataWriter.getInstance(ip, port)) {
            String address = param.getAddress();
            // 报警类写入只需要.之前的地址
            if (address.contains(".")) {
                address = address.substring(0, address.indexOf("."));
            }
            KVDataWriter kvDataWriter = writer.setAddress(address)
                    .setType(KVTypeFormat.getTypeFormat(param.getDataType()))
                    .setDataLength(param.getDataLength());
            // 传参是数组时, 转为数组后传入可变参数, 否则会认为是一个参数
            return (valueObj instanceof Object[]) ? kvDataWriter.write((Object[]) valueObj) : kvDataWriter.write(valueObj);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return false;
    }

}
