package com.tdk.sae.collect.module.ivs.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.CharsetDetector;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.abDim.mapper.ABLogMapper;
import com.tdk.sae.collect.module.abDim.model.dto.ABLogDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.ivs.mapper.IVSLogMapper;
import com.tdk.sae.collect.module.ivs.module.dto.IVSLogDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
public class IVSLogService extends ServiceImpl<IVSLogMapper,IVSLogPo> implements ILogFileService {

    private static final String REGEX_HEADER = "Order,Result,.*,Head,HGA,Time";
    private static final String REGEX_LOG_ROW = "\\d{4}/\\d{1,2}/\\d{1,2} .*,.*\n|.*\r\n";


    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        IVSLogPo lastLog = getOne(Wrappers.lambdaQuery(IVSLogPo.class)
                .eq(IVSLogPo::getEquipId, equipId)
                .eq(IVSLogPo::getFilePath, filePath)
                .orderByDesc(IVSLogPo::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, IVSLogDTO.class);
    }


    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        for(FileLogDTO logDTO:newLogs){
            IVSLogDTO ivsLogDTO= (IVSLogDTO) logDTO;
            try{
                save(BeanUtil.copyProperties(ivsLogDTO,IVSLogPo.class));
            }catch (Exception e){
            }
        }
        return true;
    }

    /**
     * read file to Log records
     * @param f file
     * @return Log records
     */
    public List<IVSLogDTO> digestLogFile(Long equipId, File f) {
        if(!f.getPath().contains("IVSLog")){
            return new ArrayList<>();
        }
        return digestLogFile(equipId, f, null);
    }

    /**
     * read file to Log records which logTime is greater(>) than parameter 'logTime'
     * @param f file
     * @param logTime logTime
     * @return Log records
     */
    public List<IVSLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
        if(!f.getPath().contains("IVSLog")){
            return new ArrayList<>();
        }
        Charset charset= CharsetDetector.detect(f);
        InputStream inuptStream=FileUtil.getInputStream(f);
        String fileContent= IoUtil.read(inuptStream,charset);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        return doDigest(equipId, f.getPath(), fileContent, logTime);
    }

    private String[] splitLogHeader(String fileContent) {
        Pattern pattern_t = Pattern.compile(REGEX_HEADER);
        Matcher matcher_t = pattern_t.matcher(fileContent);
        String[] headers = null;
        if(matcher_t.find()){
            String s = matcher_t.group();
            headers = s.split(",");
        }
        return headers;
    }

    private List<IVSLogDTO> doDigest(Long equipId, String filePath, String fileContent, LocalDateTime logTime) {
        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);
        List<IVSLogDTO> logDTOList = new ArrayList<>();
        while (matcher.find()) {
            try {
                IVSLogDTO logDTO = new IVSLogDTO();
                logDTO.setFilePath(filePath);
                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
                if(rawData.contains("PadResult,OK")){
                    continue;
                }
                logDTO.setRawData(rawData);
                logDTO.setEquipId(equipId);
                logDTO.setResultType(2);
                String[] columns = rawData.split(",");
                if(rawData.contains("检测结果") || columns.length==12){
                    logDTO.setResultType(1);
                }
                String time=columns[0];
                time=time.replaceAll("/","-");
                logDTO.setLogTime(LocalDateTimeUtil.parse(time, "yyyy-MM-dd HH:mm:ss"));

                if (logTime != null && logDTO.getLogTime().compareTo(logTime) < 0) {
                }else{
                    logDTOList.add(logDTO);
                }
            } catch (Exception ignore) {
                System.out.println(ignore);
            }
        }
        return logDTOList;
    }
}
