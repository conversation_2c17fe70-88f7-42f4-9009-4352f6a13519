package com.tdk.sae.collect.init.utils;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import org.apache.commons.io.monitor.FileAlterationListener;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class FileMonitor implements FileAlterationListener {

    private static final Logger logger = LoggerFactory.getLogger(FileMonitor.class);

    private final EquipmentDTO equipment;

    private final ILogFileService logFileService;

    public FileMonitor(EquipmentDTO equip, ILogFileService logFileService) {
        super();
        this.equipment = equip;
        this.logFileService = logFileService;
    }

    @Override
    public void onStart(FileAlterationObserver fileAlterationObserver) {}
    @Override
    public void onDirectoryCreate(File file) {}
    @Override
    public void onDirectoryChange(File file) {}
    @Override
    public void onDirectoryDelete(File file) {}

    @Override
    public void onFileCreate(File file) {
        try {
            ThreadUtil.safeSleep(500);
            Long equipId = Long.parseLong(equipment.getId());
            List<? extends FileLogDTO> newLogs = logFileService.digestLogFile(equipId, file);
            if (newLogs.size() > 0) {
                logFileService.handleNewLogs(newLogs);
            }
        } catch (Exception e) {
            logger.error("NewFile monitor duplicated record error! file:{}, error:{}", file.getPath(), e.getMessage());
        }
    }

    @Override
    public void onFileChange(File file) {
        try {
            Long equipId = Long.parseLong(equipment.getId());
            FileLogDTO latestLog = logFileService.getLastLog(equipId, file.getPath());
            LocalDateTime latestLogTime = null;
            if (!ObjectUtil.isEmpty(latestLog)) {
                latestLogTime = latestLog.getLogTime();
            }
            ThreadUtil.safeSleep(500);
            List<? extends FileLogDTO> addedLogs = logFileService.digestLogFile(equipId, file, latestLogTime);
            if (addedLogs.size() > 0) {
                logFileService.handleNewLogs(addedLogs);
            }
        } catch (Exception e) {
            logger.error("ChangeFile monitor duplicated record error! file:{}, error:{}", file.getPath(), e.getMessage());
        }
    }

    @Override
    public void onFileDelete(File file) {}
    @Override
    public void onStop(FileAlterationObserver fileAlterationObserver) {}
    public static boolean judgeFileDateIsInRange(String path){
        return judgeFileDateIsInRange(30,path);
    }
    public static boolean judgeFileDateIsInRange(int daysAgao,String path){
        LocalDateTime now=LocalDateTime.now();
        LocalDateTime lastMonth=null;
        lastMonth=(daysAgao!=30)?now.minusDays(daysAgao):now.minusMonths(1);
        List<String> strList=new ArrayList<>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyyMM\\dd");
        //获取从今天开始往前算一个月的时间列表
        for(LocalDateTime date=lastMonth;date.isBefore(now)||date.isEqual(now);date=date.plusDays(1)){
            String s=date.format(dateTimeFormatter);
            strList.add(s);
        }
        return strList.stream().anyMatch(s -> path.contains(s));
    }
}
