package com.tdk.sae.collect.module.abDim.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustRulesApplyDTO;
import com.tdk.sae.collect.module.abDim.model.params.ManuelParam;
import com.tdk.sae.collect.module.abDim.model.params.SpecUpdateParam;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogDetailPO;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustRulesApplyPO;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustRulesPO;
import com.tdk.sae.collect.module.abDim.service.*;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentStatusPO;
import com.tdk.sae.collect.module.equipment.service.EquipmentStatusService;
import com.tdk.sae.collect.module.abDim.adjust.rules.ABSpecAndControlDefineRule;
import com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamFormulaPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.impl.*;
import com.tdk.sae.collect.pubsub.listener.adjust.AdjustEventListener;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@RestController
@RequestMapping("/client/ab")
public class ABAutoAdjustController {

    private final EquipmentStatusService equipmentStatusService;

    private final ABAdjustRulesApplyService icsAdjustRulesApplyService;

    private final ABAdjustLogDetailService icsAdjustLogDetailService;

    private final KV8000ParamService kv8000ParamService;

    private final KV8000DataService kv8000DataService;

    private final KV8000ParamFormulaService kv8000ParamFormulaService;

    @Resource(name = "${system.write-type}")
    private IWritePlcService kv8000WriteService;

    private final ABLogTrayMeanService icsLogTrayMeanService;

    @Resource(name = "${system.read-type}")
    private IReadPlcService kv8000ReadService;
    private final SystemProperties systemProperties;

    private final ABAdjustRulesService rulesService;

    private final ABAdjustRulesApplyService rulesApplyService;

    private final ABAutoAdjustService icsAutoAdjustService;

    private final ABLogTrayMeanService abLogTrayMeanService;
    public final KV8000ReadService readService;

    public final ScadaReadService readService2;
    @GetMapping("/getFlag")
    public Response<Boolean> getFlag(){
        return Response.ok(ABAutoAdjustService.flag);
    }

    @PostMapping("/writeFlag")
    public Response<Boolean> writeFlag(){
        ABAutoAdjustService.flag = !ABAutoAdjustService.flag;
        return Response.ok();
    }

    @GetMapping("/info")
    public Response<Map<String, EquipmentDTO>> getICSInfo() {
        // 目前本地客户端有且仅有两台ICS设备, 分别为L和R
        Map<String, EquipmentDTO> res = new HashMap<>(2);
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (CollectionUtil.isNotEmpty(icsLogEquips)) {
            Map<String, EquipmentDTO> result = new HashMap<>(2);
            if (icsLogEquips.get(0).getClientCode().contains("-L-")) {
                result.put("ics_l", icsLogEquips.get(0));
                if (icsLogEquips.size() > 1)
                    result.put("ics_r", icsLogEquips.get(1));
            } else {
                result.put("ics_l", icsLogEquips.get(1));
                if (icsLogEquips.size() > 1)
                    result.put("ics_r", icsLogEquips.get(0));
            }
            return Response.ok(result);
        }
        res.put("ics_l", null);
        res.put("ics_r", null);
        return Response.ok(res);

//        return Response.error("No AB Info Found!");
    }

    @GetMapping("/status")
    public Response<Map<String, Integer>> getEquipStatus() {
        List<EquipmentDTO> equips = new ArrayList<>();
        equips.addAll(SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.IPC.getName()));
        equips.addAll(SystemInfoService.getEquipmentMap().get(systemProperties.getType()));
        equips.addAll(SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName()));

        if (CollectionUtil.isNotEmpty(equips)) {
            Map<String, Integer> result = new HashMap<>(4);
            List<EquipmentStatusPO> equipmentStatusPOList = equipmentStatusService.getLatestStatus();
            Map<Long, Integer> eIdStatus = equipmentStatusPOList.stream().collect(Collectors.toMap(EquipmentStatusPO::getEquipId, EquipmentStatusPO::getEquipStatus));
            equips.forEach(e -> {
                Long eId = Long.parseLong(e.getId());
                switch (e.getEquipType()) {
                    case EquipmentTypeEnum.Constants.IPC:
                        result.put("clientStatus", eIdStatus.get(eId));
                        break;
                    case EquipmentTypeEnum.Constants.KEYENCE_KV8000:
                        result.put("kv8000Status", eIdStatus.get(eId));
                        break;
                    case EquipmentTypeEnum.Constants.SCADA:
                        result.put("scadaStatus", eIdStatus.get(eId));
                        break;
                    case EquipmentTypeEnum.Constants.AB_LOG:
                        if (e.getClientCode().contains("-L-")) {
                            result.put("icsLLogStatus", eIdStatus.get(eId));
                        } else if(e.getClientCode().contains("-R-")) {
                            result.put("icsRLogStatus", eIdStatus.get(eId));
                        }
                        break;
                }
            });
            return Response.ok(result);
        }
        return Response.error("No Status Info Found!");
    }

    @GetMapping("/rules")
    public Response<List<ABAdjustRulesApplyDTO>> getApplyRules(String equipId, String type) {
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (StrUtil.isEmpty(equipId)) {
            equipId = icsLogEquips.get(0).getId();
        }
        List<ABAdjustRulesApplyPO> rulesApply = icsAdjustRulesApplyService.getRulesApplyByType(Long.parseLong(equipId), Integer.parseInt(type));
        List<ABAdjustRulesApplyDTO> reList = BeanUtil.copyToList(rulesApply, ABAdjustRulesApplyDTO.class);
        return Response.ok(reList);
    }

    @PostMapping("/rules/toggle")
    public Response<String> toggleRule(@RequestBody ABAdjustRulesApplyDTO rulesApplyDTO) {
        ABAdjustRulesApplyPO po = BeanUtil.toBean(rulesApplyDTO, ABAdjustRulesApplyPO.class);
        icsAdjustRulesApplyService.lambdaUpdate()
                .set(ABAdjustRulesApplyPO::getEnabled, po.getEnabled())
                .set(ABAdjustRulesApplyPO::getUpdatedAt, LocalDateTime.now())
                .eq(ABAdjustRulesApplyPO::getRuleId, po.getRuleId()).update();
        return Response.ok("ok", "ok");
    }

    @PostMapping("/rules/switch")
    public Response<String> switchRule(@RequestBody ABAdjustRulesApplyDTO rulesApplyDTO) {
        ABAdjustRulesApplyPO po = BeanUtil.toBean(rulesApplyDTO, ABAdjustRulesApplyPO.class);
        boolean re = icsAdjustRulesApplyService.lambdaUpdate()
                .set(ABAdjustRulesApplyPO::getEnabled, po.getEnabled())
                .set(ABAdjustRulesApplyPO::getUpdatedAt, LocalDateTime.now())
                .eq(ABAdjustRulesApplyPO::getId, po.getId()).update();
        if (re) {
            icsAutoAdjustService.clearCache(rulesApplyDTO.getEquipId().toString());
        }
        return Response.ok("ok", "ok");
    }

    @GetMapping("/history")
    public Response<List<Map<String, String>>> getHistory(String selected) {
        if (StrUtil.isEmpty(selected)) {
            return Response.ok(new ArrayList<>());
        }
        String[] selectedDates = selected.split(",");
        List<LocalDateTime> selectedDateTimes = Arrays.stream(selectedDates)
                .map(d -> LocalDateTimeUtil.parse(d, "yyyy-MM-dd"))
                .collect(Collectors.toList());
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        Set<String> eIds = icsLogEquips.stream().map(EquipmentDTO::getId).collect(Collectors.toSet());

        List<Map<String, String>> historyList = new ArrayList<>();
        List<ABAdjustLogDetailPO> logDetails = icsAdjustLogDetailService.getAdjustLogDetailsAscByDate(eIds, selectedDateTimes);
        logDetails.forEach(d -> {
            Map<String, String> history = new HashMap<>();
            history.put("content", d.getLogMsg());
            history.put("timestamp", LocalDateTimeUtil.format(d.getWriteTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
            historyList.add(history);
        });
        return Response.ok(historyList);
    }

    @GetMapping("/history/latest")
    public Response<List<Map<String, String>>> getLatestHistory(String lastTime) {
        if (StrUtil.isEmpty(lastTime)) {
            return Response.ok(new ArrayList<>());
        }
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        Set<String> eIds = icsLogEquips.stream().map(EquipmentDTO::getId).collect(Collectors.toSet());

        LocalDateTime lastTimeVal = LocalDateTimeUtil.parse(lastTime, "yyyy-MM-dd HH:mm:ss.SSS");

        List<Map<String, String>> historyList = new ArrayList<>();
        List<ABAdjustLogDetailPO> logDetails = icsAdjustLogDetailService.getAdjustLogDetailsAscByTime(eIds, lastTimeVal);
        logDetails.forEach(d -> {
            Map<String, String> history = new HashMap<>();
            history.put("content", d.getLogMsg());
            history.put("timestamp", LocalDateTimeUtil.format(d.getWriteTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
            historyList.add(history);
        });
        return Response.ok(historyList);
    }

//    @GetMapping("/manuel")
//    public Response<Map<String, String>> getManuelReadingData(String equipId) {
//        List<String> parameterList = new ArrayList<>(Arrays.asList("R_A1", "R_B1"));
//        List<KV8000DataDTO> data = kv8000DataService.getLatestParamDateByEquipIdAndParam(equipId, parameterList);
//        Map<String, String> result = new HashMap<>();
//        data.forEach(d -> {
//            switch (d.getAddressRepresent()) {
//                case "R_A1":
//                    result.put("ra", d.getReadData());
//                    break;
//                case "R_B1":
//                    result.put("rb", d.getReadData());
//                    break;
//            }
//        });
//        // todo 只读取abdim的
//        List<KV8000ParamPO> kvParams = kv8000ParamService.lambdaQuery()
//                .eq(KV8000ParamPO::getEquipId, equipId).and(
//                        w -> w.likeRight(KV8000ParamPO::getAddressRepresent, "W_")
//                ).list();
//        List<Long> writeParams = kvParams.stream().map(KV8000ParamPO::getId).collect(Collectors.toList());
//        List<KV8000ParamFormulaPO> formulaPOS = kv8000ParamFormulaService.lambdaQuery()
//                .in(KV8000ParamFormulaPO::getParamId, writeParams).list();
//        formulaPOS.forEach(f -> {
//            if (f.getDescription().equals("UP")){
//                switch (f.getAddressRepresent()) {
//                    case "W_A1":
//                        result.put("gtFormula1", f.getFormula());
//                        break;
//                    case "W_B1":
//                        result.put("gtFormula2", f.getFormula());
//                        break;
//                }
//            }else if (f.getDescription().equals("DN")){
//                switch (f.getAddressRepresent()) {
//                    case "W_A1":
//                        result.put("gtFormula3", f.getFormula());
//                        break;
//                    case "W_B1":
//                        result.put("gtFormula4", f.getFormula());
//                        break;
//                }
//            }
//
//        });
//        return Response.ok(result);
//    }

    @GetMapping("/manuel")
    public Response<Map<String, String>> getManuelReadingData(String equipId) {
        String equipType=systemProperties.getType();
        List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        List<KV8000DataDTO> cacheList=new ArrayList<>();
        if(equipType.contains("KV")){
            cacheList=readService.getDataCacheByKvId(kv8000s.get(0).getId());
        }else{
            cacheList=readService2.getDataCacheByKvId(kv8000s.get(0).getId());
        }
        List<String> parameterList = new ArrayList<>(Arrays.asList("R_A1", "R_B1"));
        List<KV8000ParamPO> special_params=new ArrayList<>();
        List<KV8000ParamPO> paramCache=kv8000ParamService.getCacheByEquipId(equipId);
        if(!CollectionUtil.isEmpty(paramCache)){
            for(String p:parameterList){
                KV8000ParamPO k=paramCache.stream().filter(pp->pp.getAddressRepresent().equals(p)).findFirst().orElse(null);
                if(k!=null){
                    special_params.add(k);
                }
            }
        }else{
            special_params=kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getEquipId, equipId)
                    .in(KV8000ParamPO::getAddressRepresent,parameterList).list();
        }
        List<Map<String,Object>> dataMap=new ArrayList<>();
        for(KV8000ParamPO param:special_params){
            Map<String,Object> m=new HashMap<>();
            KV8000DataPO temp=null;
            if(!CollectionUtil.isEmpty(cacheList)){
                List<KV8000DataDTO> tmpData=cacheList.stream().filter(c->{return c.getParamId().toString().equals(param.getId().toString());}).collect(Collectors.toList());;
                if(CollectionUtil.isNotEmpty(tmpData)){
                    KV8000DataDTO k=tmpData.get(0);
                    temp=BeanUtil.copyProperties(k,KV8000DataPO.class);
                }else{
                    temp=kv8000DataService.lambdaQuery().eq(KV8000DataPO::getParamId,param.getId())
                            .orderByDesc(KV8000DataPO::getReadTime).last("limit 1").one();
                }
            }else{
                temp=kv8000DataService.lambdaQuery().eq(KV8000DataPO::getParamId,param.getId())
                        .orderByDesc(KV8000DataPO::getReadTime).last("limit 1").one();
            }
            if(temp==null){
                temp=new KV8000DataPO();
                temp.setReadData("");
            }
            m.put("read_data",temp.getReadData());
            m.put("address",param.getAddress());
            m.put("address_represent",param.getAddressRepresent());
            dataMap.add(m);
        }


//        List<KV8000DataDTO> data = kv8000DataService.getLatestParamDateByEquipIdAndParam(equipId, parameterList);
        Map<String, String> result = new HashMap<>();
        dataMap.forEach(d -> {
            switch (d.get("address_represent").toString()) {
                case "R_A1":
                    result.put("ra",d.get("read_data").toString());
                    break;
                case "R_B1":
                    result.put("rb", d.get("read_data").toString());
                    break;
            }
        });
        // todo 只读取abdim的
        List<KV8000ParamPO> kvParams = kv8000ParamService.lambdaQuery()
                .eq(KV8000ParamPO::getEquipId, equipId).and(
                        w -> w.likeRight(KV8000ParamPO::getAddressRepresent, "W_")
                ).list();
        List<Long> writeParams = kvParams.stream().map(KV8000ParamPO::getId).collect(Collectors.toList());
        List<KV8000ParamFormulaPO> formulaPOS = kv8000ParamFormulaService.lambdaQuery()
                .in(KV8000ParamFormulaPO::getParamId, writeParams).list();
        formulaPOS.forEach(f -> {
            if (f.getDescription().equals("UP")){
                switch (f.getAddressRepresent()) {
                    case "W_A1":
                        result.put("gtFormula1", f.getFormula());
                        break;
                    case "W_B1":
                        result.put("gtFormula2", f.getFormula());
                        break;
                }
            }else if (f.getDescription().equals("DN")){
                switch (f.getAddressRepresent()) {
                    case "W_A1":
                        result.put("gtFormula3", f.getFormula());
                        break;
                    case "W_B1":
                        result.put("gtFormula4", f.getFormula());
                        break;
                }
            }

        });
        return Response.ok(result);
    }

    @PostMapping("/manuel/write")
    public Response<String> writePlcValue(@RequestBody ManuelParam param) {
        String addressRepresent = "";
        switch (param.getParameter()) {
            case "ra":
                addressRepresent = "W_A1";
                break;
            case "rb":
                addressRepresent = "W_B1";
                break;
        }
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        String code = systemProperties.getClientCode();
        if(code.equals("4D-GPM2")){
            boolean re=false;
            for(int i=1;i<=10;i++){
                String finalAddressRepresent = addressRepresent;
                if(i>1){
                    finalAddressRepresent=finalAddressRepresent.replace("1",String.valueOf(i));
                }
                String writeAddressRepresent=finalAddressRepresent;
                KV8000ParamPO paramPO = kv8000ParamService.lambdaQuery()
                        .eq(KV8000ParamPO::getEquipId, param.getEquipId()).and(c -> c.eq(KV8000ParamPO::getAddressRepresent, writeAddressRepresent))
                        .last("limit 1").one();
                re = kv8000WriteService.write(icsLogEquips.get(0), BeanUtil.copyProperties(paramPO, KV8000ParamDTO.class), param.getValue());
            }
            return Response.ok("ok", re ? "success" : "failed");
        }else{
            String finalAddressRepresent = addressRepresent;
            KV8000ParamPO paramPO = kv8000ParamService.lambdaQuery()
                    .eq(KV8000ParamPO::getEquipId, param.getEquipId()).and(c -> c.eq(KV8000ParamPO::getAddressRepresent, finalAddressRepresent))
                    .last("limit 1").one();
            boolean re = kv8000WriteService.write(icsLogEquips.get(0), BeanUtil.copyProperties(paramPO, KV8000ParamDTO.class), param.getValue());
            return Response.ok("ok", re ? "success" : "failed");
        }


    }

    @PostMapping("/manuel/update/formula")
    public Response<String> updateFormula(@RequestBody ManuelParam param) {
        String addressRepresent = "UNDEFINED";
        switch (param.getParameter()) {
            case "gtFormula1":
            case "gtFormula3":
                addressRepresent = "W_A1";
                break;
            case "gtFormula2":
            case "gtFormula4":
                addressRepresent = "W_B1";
                break;
        }

        String finalAddressRepresent = addressRepresent;
        KV8000ParamPO paramPO = kv8000ParamService.lambdaQuery()
                .eq(KV8000ParamPO::getEquipId, param.getEquipId()).and(c -> c.eq(KV8000ParamPO::getAddressRepresent, finalAddressRepresent))
                .last("limit 1").one();
        boolean re = kv8000ParamFormulaService.lambdaUpdate()
                .set(KV8000ParamFormulaPO::getFormula, param.getFormula()).eq(KV8000ParamFormulaPO::getParamId, paramPO.getId()).eq(KV8000ParamFormulaPO::getDescription,param.getHead()).update();
        return Response.ok("ok", re ? "success" : "failed");
    }

    @GetMapping("/data")
    public Response<Map<String, Object>> getChartData(String equipId, String parameter, String selected) {
        if (StrUtil.isEmpty(equipId) || StrUtil.isEmpty(parameter)) {
            return Response.ok(new HashMap<>());
        }
        if (StrUtil.isEmpty(selected)) {
            selected = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
        }
        String[] selectedDates = selected.split(",");
        List<LocalDateTime> selectedDateTimes = Arrays.stream(selectedDates)
                .map(d -> LocalDateTimeUtil.parse(d, "yyyy-MM-dd"))
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();

        Map<String, Object> meanChartData = icsLogTrayMeanService.buildMeanChart(equipId, parameter, selectedDateTimes);
        result.put("xAxisData", meanChartData.get("xAxisData"));
        result.put("series0Data", meanChartData.get("meanData"));

        @SuppressWarnings("unchecked")
        Map<String, Object> plcChartData = kv8000DataService.buildPlcChart(equipId, parameter, selectedDateTimes, (List<LocalDateTime>) meanChartData.get("xAxisTimes"));
        result.put("series1Data", plcChartData.get("plcData"));
        result.put("scatterData", meanChartData.get("adjustData"));
        result.put("specs", meanChartData.get("specs"));

        ///初始化获取数据时，lasttime取x轴最后一个数据，若数据为空，则为当前时间
        List<String> xAxisData= (List<String>) meanChartData.get("xAxisData");
        String last=LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss.SSS");
        if(xAxisData.size()>0){
            last=xAxisData.get(xAxisData.size()-1);
        }
        AdjustEventListener.lastTimeMap.get(equipId).put(parameter,last);

        return Response.ok(result);
    }

    @GetMapping("/data/latest")
    public Response<Map<String, Object>> getLatestChartData(String equipId, String parameter, String lastTime, String selected) {
        if (StrUtil.isEmpty(equipId) || StrUtil.isEmpty(parameter)) {
            return Response.ok(new HashMap<>());
        }
        LocalDateTime lastTimeVal = LocalDateTime.parse(lastTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        if (StrUtil.isEmpty(selected)) {
            selected = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
        }
        String[] selectedDates = selected.split(",");
        List<LocalDateTime> selectedDateTimes = Arrays.stream(selectedDates)
                .map(d -> LocalDateTimeUtil.parse(d, "yyyy-MM-dd"))
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();

        Map<String, Object> meanChartData = icsLogTrayMeanService.buildMeanChart(equipId, parameter, lastTimeVal, selectedDateTimes);
        result.put("xAxisData", meanChartData.get("xAxisData"));
        result.put("series0Data", meanChartData.get("meanData"));

        @SuppressWarnings("unchecked")
        Map<String, Object> plcChartData = kv8000DataService.buildPlcChart(equipId, parameter, lastTimeVal, (List<LocalDateTime>) meanChartData.get("xAxisTimes"));
        result.put("series1Data", plcChartData.get("plcData"));
        result.put("scatterData", meanChartData.get("adjustData"));

        return Response.ok(result);
    }

    @GetMapping("/read/{addressRepresent}")
    public Response<KV8000DataDTO> readLatestPlcValue(@PathVariable String addressRepresent) {
        KV8000ParamPO param = kv8000ParamService.lambdaQuery().eq(KV8000ParamPO::getAddressRepresent, addressRepresent).last("limit 1").one();
        if (param == null) {
            return Response.error("Param not found!");
        }
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        KV8000DataPO kvData = kv8000ReadService.readParam(icsLogEquips.get(0), param);
        return Response.ok(BeanUtil.copyProperties(kvData, KV8000DataDTO.class));
    }

    @GetMapping("/spec")
    public Response<Map<String, String>> getSpecRule() {
        ABAdjustRulesPO rule = rulesService.lambdaQuery().eq(ABAdjustRulesPO::getRuleType, 5).last("limit 1").one();
        if (rule == null) {
            return Response.error("Rule not found!");
        }
        ABAdjustRulesApplyPO ruleApply = rulesApplyService.lambdaQuery().eq(ABAdjustRulesApplyPO::getRuleId, rule.getId()).last("limit 1").one();
        if (ruleApply == null) {
            return Response.error("Rule not apply!");
        }
        String ruleStr = ruleApply.getRule();

        Pattern pattern = Pattern.compile(ABSpecAndControlDefineRule.REGEX);
        Matcher matcher = pattern.matcher(ruleStr);
        if (!matcher.find()) {
            return Response.error("Spec value not found!");
        }
        String specLimitStr = matcher.group(1);
        String controlLimitStr = matcher.group(2);
        Map<String, String> result = new HashMap<>();
        result.put("spec", specLimitStr);
        result.put("control", controlLimitStr);
        return Response.ok(result);
    }

    @PostMapping("/spec/update")
    public Response<String> updateSpecRule(@RequestBody SpecUpdateParam specUpdateParam) {
        ABAdjustRulesPO rule = rulesService.lambdaQuery().eq(ABAdjustRulesPO::getRuleType, 5).last("limit 1").one();
        if (rule == null) {
            return Response.error("Rule not found!");
        }
        String specRule = rule.getRule();
        Pattern pattern = Pattern.compile(ABSpecAndControlDefineRule.REGEX);
        Matcher matcher = pattern.matcher(specRule);
        if (!matcher.find()) {
            return Response.error("Malformed rule!");
        }
        String specLimitStr = matcher.group(1);
        String controlLimitStr = matcher.group(2);
        String updateRule = specRule.replaceAll(specLimitStr, specUpdateParam.getSpec()).replaceAll(controlLimitStr, specUpdateParam.getControl());
        boolean re = icsAdjustRulesApplyService.lambdaUpdate()
                .set(ABAdjustRulesApplyPO::getRule, updateRule).eq(ABAdjustRulesApplyPO::getRuleId, rule.getId()).update();
        if (re) {
            icsAutoAdjustService.clearCache();
        }
        return Response.ok("ok", re ? "success" : "failed");
    }

    //获取ABlog分布数据
    @GetMapping("/distributeData")
    public Response<Map<String, Map<String,Object>>> distributeData(String selected) {
        if (StrUtil.isEmpty(selected)) {
            selected = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
        }
        Map<String, Map<String,Object>> result=abLogTrayMeanService.getDistributeData(selected);
        return Response.ok(result);
    }

}
