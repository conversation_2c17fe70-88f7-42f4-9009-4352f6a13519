package com.tdk.sae.collect.exceptions.handler;

import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.exceptions.HdsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 基础异常
     */
    @ExceptionHandler(HdsException.class)
    public Response<String> handlerBaseException(HdsException e) {
        logger.error(e.getMessage(), e);
        return Response.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(BindException.class)
    public Response<String> BindExceptionHandler(BindException e) {
        String message = e.getBindingResult().getAllErrors()
                .stream().map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining());
        return Response.error(ResponseCodeEnum.PARAMS_REQUIRED_IS_NULL, message);
    }

}
