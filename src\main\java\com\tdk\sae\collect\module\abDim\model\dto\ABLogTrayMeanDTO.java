package com.tdk.sae.collect.module.abDim.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class ABLogTrayMeanDTO implements Serializable {

    private Long id;

    private Long equipId;

    private String parameter;

    private BigDecimal meanValue;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime calculatedTime;

    private Integer isDeleted;

    private Integer synced;

    private String head;
}
