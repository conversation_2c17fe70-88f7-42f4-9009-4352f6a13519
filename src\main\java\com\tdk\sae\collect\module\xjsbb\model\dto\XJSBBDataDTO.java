package com.tdk.sae.collect.module.xjsbb.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XJSBBDataDTO {

    private Long id;

    private Long equipId;

    private Long paramId;

    private String readData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime readTime;

    private Integer isDeleted = 0;

    private Integer synced = 0;

    @JsonIgnore
    private String variable;

}
