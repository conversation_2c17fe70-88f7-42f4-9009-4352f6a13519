package com.tdk.sae.collect.aop.aspect;

import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tdk.sae.collect.aop.annotation.FieldClass;
import com.tdk.sae.collect.aop.annotation.FieldInfo;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class FieldInfoAspect {

    /**
     * 获取所有的属性注解，包含父类
     *
     * @param target 目标class
     * @return key, value形式的集合，key为 {@link FieldInfo#columnName()},value为 {@link FieldInfo#name()},
     */
    public static Map<String, String> getFieldInfo(Class<?> target) {
        Field[] fields = ReflectUtil.getFields(target);
        FieldClass annotation = target.getAnnotation(FieldClass.class);
        List<String> exclude = Lists.newArrayList();
        if (null != annotation) {
            exclude = Arrays.asList(annotation.exclude());
        }
        Map<String, String> resultMap = Maps.newHashMap();
        for (Field field : fields) {
            FieldInfo fieldInfo = field.getAnnotation(FieldInfo.class);
            if (null == fieldInfo) {
                continue;
            }
            if (exclude.contains(fieldInfo.columnName())) {
                continue;
            }
            resultMap.put(fieldInfo.columnName(), fieldInfo.name());
        }
        return resultMap;
    }
}
