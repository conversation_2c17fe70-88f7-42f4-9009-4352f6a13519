package com.tdk.sae.collect.module.auth.controller;

import com.tdk.sae.collect.domain.result.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * fake apis, frontend will request these, but we don't need that in local env
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dict")
public class DictController {

    @GetMapping("/cache/get")
    public Response<List<String>> getCache() {
        List<String> cache = new ArrayList<>();
        return Response.ok(cache);
    }

    /**
     * 更新缓存
     *
     * @return 是否成功
     */
    @GetMapping("/cache/update")
    public Response<String> updateCache() {
        return Response.ok("更新成功");
    }
}
