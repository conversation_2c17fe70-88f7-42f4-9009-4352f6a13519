package com.tdk.sae.collect;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogDTO;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ICSLogTests {

    private static final String REGEX_HEADER = "Time,POS,Master,.*,Result";
    private static final String REGEX_LOG_ROW = "\\d{4}-\\d{1,2}-\\d{1,2} .*,.*\n|.*\r\n";

//    public static void main(String[] args) {
//        List<ICSLogDTO> result = digestLogFile(123L, new File("C:\\Users\\<USER>\\Desktop\\ICSData\\202403\\test.txt"));
//        System.out.println(result.size());
//    }
//
//    /**
//     * read file to Log records
//     * @param f file
//     * @return Log records
//     */
//    public static List<ICSLogDTO> digestLogFile(Long equipId, File f) {
//        return digestLogFile(equipId, f, null);
//    }
//
//    /**
//     * read file to Log records which logTime is greater(>) than parameter 'logTime'
//     * @param f file
//     * @param logTime logTime
//     * @return Log records
//     */
//    public static List<ICSLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
//        String fileContent = FileUtil.readString(f, StandardCharsets.UTF_8);
//        if (StrUtil.isEmpty(fileContent)) {
//            return new ArrayList<>();
//        }
//        String[] headers = splitLogHeader(fileContent);
//        if (headers == null) {
//            return new ArrayList<>();
//        }
//        return doDigest(equipId, f.getPath(), fileContent, headers, logTime);
//    }
//
//    private static String[] splitLogHeader(String fileContent) {
//        Pattern pattern_t = Pattern.compile(REGEX_HEADER);
//        Matcher matcher_t = pattern_t.matcher(fileContent);
//        String[] headers = null;
//        if(matcher_t.find()){
//            String s = matcher_t.group();
//            headers = s.split(",");
//        }
//        return headers;
//    }
//
//    /**
//     * headers 从静态变为动态
//     */
//    private static List<ICSLogDTO> doDigest(Long equipId, String filePath, String fileContent, String[] defaultHeaders, LocalDateTime logTime) {
//        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
//        Matcher matcher = pattern.matcher(fileContent);
//
//        List<ICSLogDTO> logDTOList = new ArrayList<>();
//        while (matcher.find()) {
//            try {
//                ICSLogDTO logDTO = new ICSLogDTO();
//                logDTO.setFilePath(filePath);
//                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
//                logDTO.setRawData(rawData);
//                logDTO.setEquipId(equipId);
//
//                String[] columns = rawData.split(",");
//                if (columns.length != defaultHeaders.length) {
//                    // 切换打胶模式后, headers会变化
//                    defaultHeaders = getAdaptiveHeaders(columns.length);
//                    if (defaultHeaders == null) {
//                        throw new RuntimeException("no matched log headers found!");
//                    }
//                }
//                // update description
//                logDTO.setDescription(String.join(",", defaultHeaders));
//
//                StringBuilder logVal = new StringBuilder();
//                boolean isFilled = true;
//                for (int i = 0; i < defaultHeaders.length; i++) {
//                    String header = defaultHeaders[i];
//                    String column = columns[i].trim();
//                    if (header.equals(column)) {
//                        isFilled = false;
//                        break;
//                    }
//                    switch (header) {
//                        case "Time":
//                            column = StrUtil.padAfter(column, 23, "0");
//                            logDTO.setLogTime(LocalDateTimeUtil.parse(column, "yyyy-MM-dd HH:mm:ss:SSS"));
//                            break;
//                        case "POS":
//                            logDTO.setPos(column);
//                            break;
//                        case "Master":
//                            logDTO.setMaster(column);
//                            break;
//                        case "Result":
//                            logDTO.setLogValue(logVal.toString());
//                            logDTO.setResult(column);
//                            break;
//                        default:
//                            if (logVal.length() == 0) {
//                                logVal.append(column);
//                            } else {
//                                logVal.append(",").append(column);
//                            }
//                            break;
//                    }
//                }
//                if (logTime != null && logDTO.getLogTime().compareTo(logTime) <= 0) {
//                    continue;
//                }
//                if (isFilled) {
//                    logDTOList.add(logDTO);
//                }
//            } catch (Exception ignore) {}
//        }
//        return logDTOList;
//    }
//
//    private static final String oneDot = "Time,POS,Master,EX1,EY1,ED1,EDX1,EDY1,Result";
//    private static final String twoDot = "Time,POS,Master,EX1,EX2,EY1,EY2,ED1,ED2,EDX1,EDY1,EDX2,EDY2,Result";
//    private static final String threeDot = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,ED1,ED2,ED3,EDX1,EDY1,EDX2,EDY2,EDX3,EDY3,Result";
//
//    private static String[] getAdaptiveHeaders(int rawDataColumnLength) {
//        if (rawDataColumnLength == 9) {
//            return oneDot.split(",");
//        } else if (rawDataColumnLength == 14) {
//            return twoDot.split(",");
//        } else if (rawDataColumnLength == 19) {
//            return threeDot.split(",");
//        }
//        return null;
//    }

}
