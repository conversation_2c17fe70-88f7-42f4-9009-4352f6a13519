spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ************************************
#    username: root
#    password: 123456
#    url: **********************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: gzg@data
    url: ***********************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456