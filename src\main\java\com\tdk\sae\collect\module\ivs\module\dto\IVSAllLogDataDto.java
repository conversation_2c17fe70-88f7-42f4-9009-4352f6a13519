package com.tdk.sae.collect.module.ivs.module.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode
public class IVSAllLogDataDto {

    private LocalDateTime logTime;
    private Integer empty;
    private Integer t1;
    private Integer t2;
    private Integer t3;
    private Integer t4;
    private Integer t5;
    private Integer t6;
    private Integer t7;
    private Integer tOther;
    private Integer ok;
    private Integer direction;

    //计算比例，BigDecimal保留三位小数
    public String getT1Rate(Integer num) {
        int total = t1 + t2 + t3 + t4 + t5 + t6 + t7  + ok;
        // 使用 BigDecimal 进行精确计算
        BigDecimal numDecimal = BigDecimal.valueOf(num);
        BigDecimal totalDecimal = BigDecimal.valueOf(total);

        // 计算百分比并保留三位小数，四舍五入
        BigDecimal percentage = numDecimal
                .multiply(BigDecimal.valueOf(100)) // 计算百分比
                .divide(totalDecimal, 1, RoundingMode.HALF_UP);
        return percentage.toPlainString();
    }

    //相加
    public IVSAllLogDataDto addLog(IVSAllLogDataDto dataDto){
        this.empty += dataDto.empty;
        this.t1 += dataDto.t1;
        this.t2 += dataDto.t2;
        this.t3 += dataDto.t3;
        this.t4 += dataDto.t4;
        this.t5 += dataDto.t5;
        this.t6 += dataDto.t6;
        this.t7 += dataDto.t7;
        this.tOther += dataDto.tOther;
        this.ok += dataDto.ok;
        return this;
    }



}
