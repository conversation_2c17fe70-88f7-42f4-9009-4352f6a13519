package com.tdk.sae.collect.tasks;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.model.po.ICSPidLogPO;
import com.tdk.sae.collect.module.ics.service.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@RequiredArgsConstructor
@Component
public class ICSPidAutoAdjustTaskService {

    private static final Logger logger = LoggerFactory.getLogger(ICSPidAutoAdjustTaskService.class);

    private final ICSLogService icsLogService;
    private final ICSPidLogService icsPidLogService;
    private final ICSAdjustRulesApplyService icsAdjustRulesApplyService;

    /**
     * 每3秒执行一次pidLog计算
     */
    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void calculateICSPidLogMean() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_LOG.getName());
        if (CollectionUtil.isEmpty(icsEquips)) {
            return;
        }
        icsEquips.forEach(equip -> {
            ICSAdjustRulesApplyPO rulesApplyPO=icsAdjustRulesApplyService.lambdaQuery()
                    .eq(ICSAdjustRulesApplyPO::getEquipId,equip.getId())
                    .eq(ICSAdjustRulesApplyPO::getRule,"ICS-PID启用/禁用自动调整").one();
            if(rulesApplyPO==null){
                return;
            }
            ICSPidLogPO icsPidLogPO = icsPidLogService.lambdaQuery()
                    .eq(ICSPidLogPO::getEquipId, equip.getId())
                    .orderByDesc(ICSPidLogPO::getLogTime).last("limit 1").one();
            List<ICSLogPO> icsLogPOList;
            if (icsPidLogPO == null) {
                //第一次load数据只加载最近7天的数据
                LocalDateTime weekDaysAgo=LocalDateTime.now().minusDays(7);
                icsLogPOList = icsLogService.lambdaQuery()
                        .eq(ICSLogPO::getEquipId, equip.getId())
                        .gt(ICSLogPO::getLogTime,weekDaysAgo)
                        .orderByAsc(ICSLogPO::getLogTime).last("limit 1000").list();
            } else {
                icsLogPOList = icsLogService.lambdaQuery()
                        .eq(ICSLogPO::getEquipId, equip.getId())
                        .and(c -> c.gt(ICSLogPO::getLogTime, icsPidLogPO.getLogTime()))
                        .orderByAsc(ICSLogPO::getLogTime).last("limit 1000").list();
            }
            if(icsLogPOList.size()>0){
                icsPidLogService.calculatePidLog(equip,icsLogPOList);
            }
        });
    }

}
