package com.tdk.sae.collect.module.plc.kv8000;

/**
 * 基恩士数据格式定义，数据实际表示需要看使用者如何翻译。如：有符号32位十进制数既可以翻译成整数也可以翻译成小数
 */
public enum KVDataType {

    U_16DEC(".U"), // 无符号16位十进制数
    S_16DEC(".S"), // 有符号16位十进制数
    U_32DEC(".D"), // 无符号32位十进制数
    S_32DEC(".L"), // 有符号32位十进制数
    _16HEX(".H"); // 16位十六进制数

    private final String dataType;

    KVDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataType() {
        return this.dataType;
    }

}
