package com.tdk.sae.collect.module.auth.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VueMenuVO implements Serializable {
    /**
     * router 名称
     */
    private String name;
    /**
     * router 地址
     */
    private String path;
    /**
     * 组件地址
     */
    private String component;
    /**
     * 重定向
     */
    private String redirect;
    /**
     * 路由元数据
     */
    private MenuMetaVO meta;
    /**
     * 子集
     */
    private List<VueMenuVO> children;
}
