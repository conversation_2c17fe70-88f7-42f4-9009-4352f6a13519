<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.ics.mapper.ICSLogTrayMeanMapper">

    <select id="getChartDataInTimeRange" resultType="com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO">
        select m.mean_value, m.end_time, d.to_value
        from t_biz_ics_log_tray_mean m
        left join t_biz_ics_adjust_log_detail d on m.id = d.mean_id
        where m.equip_id = #{equipId} and m.parameter = #{param} and
        <foreach item="item" collection="times" separator="or" open="(" close=")" index="index">
            m.end_time &gt;= #{item} and m.end_time &lt; DATE_ADD(#{item}, INTERVAL 1 DAY)
        </foreach>
        order by m.end_time asc
    </select>

    <select id="getChartDataLatest" resultType="com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO">
        select m.mean_value, m.end_time, d.to_value
        from t_biz_ics_log_tray_mean m
        left join t_biz_ics_adjust_log_detail d on m.id = d.mean_id
        where m.equip_id = #{equipId} and m.parameter = #{param} and
        m.end_time &gt; #{lastTime}
        order by m.end_time asc
    </select>

    <select id="getAdjustDataInTimeRange" resultType="com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO">
        select m.mean_value, m.end_time, d.to_value
        from t_biz_ics_log_tray_mean m
        left join t_biz_ics_adjust_log_detail d on m.id = d.mean_id
        where m.equip_id = #{equipId} and m.parameter = #{param} and to_value is not null and
        <foreach item="item" collection="times" separator="or" open="(" close=")" index="index">
            m.end_time &gt;= #{item} and m.end_time &lt; DATE_ADD(#{item}, INTERVAL 1 DAY)
        </foreach>
        order by m.end_time asc
    </select>

</mapper>
