package com.tdk.sae.collect.module.auth.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TreeMenuDTO extends MenuDTO {
    /**
     * 子树
     */
    private List<TreeMenuDTO> children;
}
