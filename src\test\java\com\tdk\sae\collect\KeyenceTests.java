package com.tdk.sae.collect;

import cn.hutool.core.lang.Console;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.kv8000.rw.KVDataReader;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ReadService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000WriteService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

//@SpringBootTest
class KeyenceTests {

//	@Test
//	public void test2(@Autowired KV8000ReadService kv8000ReadService) {
//		EquipmentDTO v = new EquipmentDTO();
//		v.setAddress("************:8501");
//		v.setId("2");
//		kv8000ReadService.readParams(v);
//	}

//	public static void main(String[] args) {
//		String ip = "************";
//		int port = 8501;
//		try (KVDataReader reader = KVDataReader.getInstance(ip, port)) {
//			KVData d = reader.setAddress("LR10000")
//					.setType(KVTypeFormat.getTypeFormat("bool"))
//					.read();
//			System.out.println(d);
//		} catch (Exception e) {
//			System.out.println(e.getMessage());
//		}

//	}

//	@Test
//	public void test3(@Autowired KV8000WriteService kv8000WriteService) throws InterruptedException {
//		EquipmentDTO v = new EquipmentDTO();
//		v.setAddress("************:8501");
//		v.setId("2");
//
//		KV8000ParamDTO param = new KV8000ParamDTO();
//		param.setAddress("DM3272");
//		param.setDataType("string[10]");
//		param.setDataLength(10);
//
//		String value = "B19_KV8000_1_T"; // TODO: 测试未进行

//		Console.log(kv8000WriteService.write(v, param, value));
//		Thread.sleep(3000);
//	}

}
