DROP PROCEDURE IF EXISTS insert_ics_conf;$$

CREATE PROCEDURE insert_ics_conf()
begin
  DECLARE v_equip_id INT;
  DECLARE v_done INT DEFAULT 0;
  DECLARE v_id_start INT DEFAULT 1;
  DECLARE row_count INT;
  DECLARE equip_ids_cursor CURSOR for

    SELECT id FROM t_biz_equip WHERE equip_type = 5;

  DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = 1;
  -- 检查 collect.t_biz_ics_conf 表是否已有数据
    CREATE TABLE IF NOT EXISTS `t_biz_ics_conf` (
      `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
      `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
      `conf_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
      `conf_key` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
      `conf_value` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
      `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
      `remark` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
      PRIMARY KEY (`id`),
      KEY `idx_t_biz_ics_conf_eid` (`equip_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  SELECT COUNT(*) INTO row_count FROM collect.t_biz_ics_conf;
  IF row_count = 0 THEN

    OPEN equip_ids_cursor;

    read_loop: LOOP
    FETCH equip_ids_cursor INTO v_equip_id;
    IF v_done THEN
      LEAVE read_loop;
    END IF;
    -- 动态执行插入语句
    INSERT INTO t_biz_ics_conf (id, equip_id, conf_name, conf_key, conf_value, is_deleted, remark) VALUES
    	  (v_id_start, v_equip_id, 'ED1', 'USL', '295', 0, 'SPEC上限'),
    	  (v_id_start + 1, v_equip_id, 'ED1', 'LSL', '265', 0, 'SPEC下限'),
    	  (v_id_start + 2, v_equip_id, 'ED1', 'TARGET', '280', 0, '目标'),
    	  (v_id_start + 3, v_equip_id, 'ED1', 'UCL', '285', 0, '控制线上限'),
    	  (v_id_start + 4, v_equip_id, 'ED1', 'LCL', '275', 0, '控制线下限'),
    	  (v_id_start + 5, v_equip_id, 'ED1', 'ENABLE', '1', 0, '启用ED1调整'),
    	  (v_id_start + 6, v_equip_id, 'EX1', 'USL', '15', 0, 'SPEC上限'),
    	  (v_id_start + 7, v_equip_id, 'EX1', 'LSL', '-15', 0, 'SPEC下限'),
    	  (v_id_start + 8, v_equip_id, 'EX1', 'TARGET', '0', 0, '目标'),
    	  (v_id_start + 9, v_equip_id, 'EX1', 'UCL', '5', 0, '控制线上限'),
    	  (v_id_start + 10, v_equip_id, 'EX1', 'LCL', '-5', 0, '控制线下限'),
    	  (v_id_start + 11, v_equip_id, 'EX1', 'ENABLE', '1', 0, '启用EX1调整'),
    	  (v_id_start + 12, v_equip_id, 'EY1', 'USL', '145', 0, 'SPEC上限'),
    	  (v_id_start + 13, v_equip_id, 'EY1', 'LSL', '115', 0, 'SPEC下限'),
    	  (v_id_start + 14, v_equip_id, 'EY1', 'TARGET', '130', 0, '目标'),
    	  (v_id_start + 15, v_equip_id, 'EY1', 'UCL', '135', 0, '控制线上限'),
    	  (v_id_start + 16, v_equip_id, 'EY1', 'LCL', '125', 0, '控制线下限'),
    	  (v_id_start + 17, v_equip_id, 'EY1', 'ENABLE', '1', 0, '启用EY1调整'),
    	  (v_id_start + 18, v_equip_id, 'ED2', 'USL', '160', 0, 'SPEC上限'),
    	  (v_id_start + 19, v_equip_id, 'ED2', 'LSL', '130', 0, 'SPEC下限'),
    	  (v_id_start + 20, v_equip_id, 'ED2', 'TARGET', '145', 0, '目标'),
    	  (v_id_start + 21, v_equip_id, 'ED2', 'UCL', '150', 0, '控制线上限'),
    	  (v_id_start + 22, v_equip_id, 'ED2', 'LCL', '140', 0, '控制线下限'),
    	  (v_id_start + 23, v_equip_id, 'ED2', 'ENABLE', '1', 0, '启用ED2调整'),
    	  (v_id_start + 24, v_equip_id, 'EX2', 'USL', '15', 0, 'SPEC上限'),
    	  (v_id_start + 25, v_equip_id, 'EX2', 'LSL', '-15', 0, 'SPEC下限'),
    	  (v_id_start + 26, v_equip_id, 'EX2', 'TARGET', '0', 0, '目标'),
    	  (v_id_start + 27, v_equip_id, 'EX2', 'UCL', '5', 0, '控制线上限'),
    	  (v_id_start + 28, v_equip_id, 'EX2', 'LCL', '-5', 0, '控制线下限'),
    	  (v_id_start + 29, v_equip_id, 'EX2', 'ENABLE', '1', 0, '启用EX2调整'),
    	  (v_id_start + 30, v_equip_id, 'EY2', 'USL', '425', 0, 'SPEC上限'),
    	  (v_id_start + 31, v_equip_id, 'EY2', 'LSL', '395', 0, 'SPEC下限'),
    	  (v_id_start + 32, v_equip_id, 'EY2', 'TARGET', '410', 0, '目标'),
    	  (v_id_start + 33, v_equip_id, 'EY2', 'UCL', '415', 0, '控制线上限'),
    	  (v_id_start + 34, v_equip_id, 'EY2', 'LCL', '405', 0, '控制线下限'),
    	  (v_id_start + 35, v_equip_id, 'EY2', 'ENABLE', '1', 0, '启用EY2调整'),
    	  (v_id_start + 36, v_equip_id, 'ED1', 'UIL','282.5', 0,'内部控制线上限'),
          (v_id_start + 37, v_equip_id, 'ED2', 'UIL','147.5', 0,'内部控制线上限'),
          (v_id_start + 38, v_equip_id, 'EY1','UIL','132.5',0,'内部控制线上限'),
          (v_id_start + 39, v_equip_id, 'EY2','UIL','412.5',0,'内部控制线上限'),
          (v_id_start + 40, v_equip_id, 'EX1','UIL','2.5',0,'内部控制线下限'),
          (v_id_start + 41, v_equip_id, 'EX2','UIL','2.5',0,'内部控制线下限'),
          (v_id_start + 42, v_equip_id, 'ED1','LIL','277.5',0,'内部控制线下限'),
          (v_id_start + 43, v_equip_id, 'ED2','LIL','142.5',0,'内部控制线下限'),
          (v_id_start + 44, v_equip_id, 'EY1','LIL','127.5',0,'内部控制线下限'),
          (v_id_start + 45, v_equip_id, 'EY2','LIL','407.5',0,'内部控制线下限'),
          (v_id_start + 46, v_equip_id, 'EX1','LIL','-2.5',0,'内部控制线下限'),
          (v_id_start + 47, v_equip_id, 'EX2','LIL','-2.5',0,'内部控制线下限');

    -- 增加48，以便为下一个设备ID生成新的ID
    SET v_id_start = v_id_start + 48;

    END LOOP;

    CLOSE equip_ids_cursor;
  END IF;
END;$$


call insert_ics_conf();$$
DROP PROCEDURE IF EXISTS insert_ics_conf;$$

