package com.tdk.sae.collect.pubsub.listener.xjsbb;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBLogDTO;
import com.tdk.sae.collect.module.xjsbb.service.XJSBBDataService;
import com.tdk.sae.collect.pubsub.event.xjsbb.XJSBBLogEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.List;

@RequiredArgsConstructor
@Component
public class XJSBBLogEventListener implements ApplicationListener<XJSBBLogEvent> {

    private final XJSBBDataService xjsbbDataService;

    @Override
    public void onApplicationEvent(@Nonnull XJSBBLogEvent event) {
        List<XJSBBLogDTO> logs = event.getLogs();
        if (CollectionUtil.isNotEmpty(logs)) {
            xjsbbDataService.saveData(logs);
        }
    }

}
