package com.tdk.sae.collect.cache.redis;

import com.tdk.sae.collect.cache.KeyDefine;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public abstract class RedisCache<V> {

    protected final static String CACHE_SPLICE_COLON = ":";

    protected KeyDefine keyDefine;

    @Autowired
    @Getter
    protected RedisUtil<String, V> cache;

    protected RedisCache() {
        initKeyDefine();
    }

    /**
     * 通用的模板方法
     * @param value 值
     * @param keys 键,可多个 例: root,sub,k = 前缀:root:sub:k
     */
    public final void set(V value, Object... keys) {
        beforeSet();
        doSet(value, keys);
        afterSet();
    }

    /**
     * 让子类给keyDefine赋值
     */
    protected abstract void initKeyDefine();

    /**
     * 可选
     */
    protected void beforeSet() {}

    private void doSet(V value, Object... keys) {
        String cacheKey = getCacheKey(keyDefine, keys);
        if (keyDefine.getExpire() > 0) {
            cache.setEx(cacheKey, value, keyDefine.getExpire(), keyDefine.getTimeUnit());
        } else {
            cache.set(cacheKey, value);
        }
    }

    /**
     * 可选
     */
    protected void afterSet() {}

    /**
     * 通用的取值方法
     * @param keys 组成key的值
     * @return 缓存值
     */
    public final V get(Object... keys) {
        String cacheKey = getCacheKey(keyDefine, keys);
        return cache.get(cacheKey);
    }

    /**
     * 通用的删除方法
     * @param keys 组成key的值
     */
    public final void del(Object... keys) {
        String cacheKey = getCacheKey(keyDefine, keys);
        cache.delete(cacheKey);
    }

    public final boolean hasKey(Object... keys){
        String cacheKey = getCacheKey(keyDefine, keys);
        return cache.hasKey(cacheKey);
    }

    /**
     * 构建redis的key
     * @param kd key定义
     * @param keys 组成key的值
     * @return redis key
     */
    public String getCacheKey(KeyDefine kd, Object... keys) {
        return buildKeys(kd, keys);
    }

    public String getCacheKey(Object... keys) {
        return buildKeys(keyDefine, keys);
    }

    public  Set<String> keys(String pattern) {
        pattern = keyDefine.getFullPrefix() + "*" + pattern + "*";
        return cache.keys(pattern);
    }

    protected static String buildKeys(KeyDefine kd, Object... keys) {
        if (keys.length == 0) {
            return kd.getFullPrefix();
        }
        return kd.getFullPrefix() + CACHE_SPLICE_COLON + concatKeys(keys);
    }

    protected static String concatKeys(Object... keys) {
        return Arrays.stream(keys).map(String::valueOf).collect(Collectors.joining(CACHE_SPLICE_COLON));
    }



}
