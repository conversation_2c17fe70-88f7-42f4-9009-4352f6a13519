package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.mapper.ICSTrendLogMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSTrendLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@RequiredArgsConstructor
@Service
public class ICSTrendLogService extends ServiceImpl<ICSTrendLogMapper, ICSTrendLog> {



    public Map<String, Object> trendData(String startDate, String endDate, String barcode) {
        // 如果 selected 为空，则使用当前日期
        if (StrUtil.isEmpty(endDate)) {
            endDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
        }
        LocalDateTime today =  LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
        if(StrUtil.isEmpty(startDate)){
            startDate = LocalDateTimeUtil.format(today.minusDays(1), "yyyy-MM-dd HH:mm:ss");
        }
        LocalDateTime nextDay= LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");
        // 获取设备数据并根据 equipCode 区分左右
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_TREND.getName());
        Set<Long> leftEquipIds = new HashSet<>();
        Set<Long> rightEquipIds = new HashSet<>();

        // 区分设备是左设备还是右设备
        icsEquips.forEach(equip -> {
            if (equip.getEquipCode().contains("L")) {
                leftEquipIds.add(Long.valueOf(equip.getId()));
            } else if (equip.getEquipCode().contains("R")) {
                rightEquipIds.add(Long.valueOf(equip.getId()));
            }
        });

        // 查询符合条件的数据
        List<ICSTrendLog> TFTrendLogs = this.lambdaQuery()
                .eq(barcode != null && !barcode.isEmpty(), ICSTrendLog::getBarcode, barcode)
                .eq(ICSTrendLog::getDescription, "TF")
                .ge(ICSTrendLog::getCreatedAt, nextDay)
                .le(ICSTrendLog::getCreatedAt, today)
                .orderByAsc(ICSTrendLog::getCreatedAt)
                .list();
        // 查询符合条件的数据
        List<ICSTrendLog> N2TrendLogs = this.lambdaQuery()
                .eq(barcode != null && !barcode.isEmpty(), ICSTrendLog::getBarcode, barcode)
                .eq(ICSTrendLog::getDescription, "N2")
                .ge(ICSTrendLog::getCreatedAt, nextDay)
                .le(ICSTrendLog::getCreatedAt, today)
                .orderByAsc(ICSTrendLog::getCreatedAt)
                .list();

        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();
        Map<String, List<String>> temperatureDataL = initializeTemperatureData();
        Map<String, List<String>> flowDataL = initializeFlowData();
        Map<String, List<String>> n2PurityDataL = initializeN2PurityData();
        Map<String, List<String>> temperatureDataR = initializeTemperatureData();
        Map<String, List<String>> flowDataR = initializeFlowData();
        Map<String, List<String>> n2PurityDataR = initializeN2PurityData();

        // 单独存储 xAxis 数据
        List<String> leftXAxisData = new ArrayList<>();
        List<String> rightXAxisData = new ArrayList<>();
        // 单独存储 xN2Axis 数据
        List<String> leftN2XAxisData = new ArrayList<>();
        List<String> rightN2XAxisData = new ArrayList<>();

        // 遍历查询结果，分别处理左设备和右设备的数据
        TFTrendLogs.forEach(log -> {
            // 填充左设备和右设备数据
            if (leftEquipIds.contains(log.getEquipId())) {
                leftXAxisData.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
                fillTFData(log, temperatureDataL, flowDataL);
            } else if (rightEquipIds.contains(log.getEquipId())) {
                rightXAxisData.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
                fillTFData(log, temperatureDataR, flowDataR);
            }
        });

        // 遍历查询结果，分别处理左设备和右设备的数据
        N2TrendLogs.forEach(log -> {
            // 填充左设备和右设备数据
            if (leftEquipIds.contains(log.getEquipId())) {
                leftN2XAxisData.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
                fillN2Data(log, n2PurityDataL);
            } else if (rightEquipIds.contains(log.getEquipId())) {
                rightN2XAxisData.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
                fillN2Data(log, n2PurityDataR);
            }
        });

        // 将数据组织成需要的格式
        resultData.put("ICS-L-T", formatDataForDevice(temperatureDataL, leftXAxisData));
        resultData.put("ICS-L-F", formatDataForDevice(flowDataL, leftXAxisData));
        resultData.put("ICS-L-N2Purity", formatDataForDevice(n2PurityDataL, leftN2XAxisData));

        resultData.put("ICS-R-T", formatDataForDevice(temperatureDataR, rightXAxisData));
        resultData.put("ICS-R-F", formatDataForDevice(flowDataR, rightXAxisData));
        resultData.put("ICS-R-N2Purity", formatDataForDevice(n2PurityDataR, rightN2XAxisData));

        return resultData;
    }

    // 初始化温度数据
    private Map<String, List<String>> initializeTemperatureData() {
        Map<String, List<String>> temperatureData = new LinkedHashMap<>();
        temperatureData.put("T1", new ArrayList<>());
        temperatureData.put("T2", new ArrayList<>());
        temperatureData.put("T3", new ArrayList<>());
        temperatureData.put("T4", new ArrayList<>());
        temperatureData.put("T5", new ArrayList<>());
        return temperatureData;
    }

    // 初始化流量数据
    private Map<String, List<String>> initializeFlowData() {
        Map<String, List<String>> flowData = new LinkedHashMap<>();
        flowData.put("F1", new ArrayList<>());
        flowData.put("F2", new ArrayList<>());
        flowData.put("F3", new ArrayList<>());
        flowData.put("F4", new ArrayList<>());
        flowData.put("F5", new ArrayList<>());
        return flowData;
    }

    // 初始化氮气纯度数据
    private Map<String, List<String>> initializeN2PurityData() {
        Map<String, List<String>> n2PurityData = new LinkedHashMap<>();
        n2PurityData.put("N2Purity", new ArrayList<>());
        return n2PurityData;
    }

    // 填充左设备数据
    private void fillTFData(ICSTrendLog log, Map<String, List<String>> temperatureData,
                                    Map<String, List<String>> flowData) {
        temperatureData.get("T1").add(log.getTemperature1());
        temperatureData.get("T2").add(log.getTemperature2());
        temperatureData.get("T3").add(log.getTemperature3());
        temperatureData.get("T4").add(log.getTemperature4());
        temperatureData.get("T5").add(log.getTemperature5());

        flowData.get("F1").add(log.getFlow1());
        flowData.get("F2").add(log.getFlow2());
        flowData.get("F3").add(log.getFlow3());
        flowData.get("F4").add(log.getFlow4());
        flowData.get("F5").add(log.getFlow5());

    }

    // 填充右设备数据
    private void fillN2Data(ICSTrendLog log, Map<String, List<String>> n2PurityData) {
        n2PurityData.get("N2Purity").add(log.getN2Purity());
    }

    // 格式化数据
    private Map<String, Object> formatDataForDevice(Map<String, List<String>> data, List<String> xAxisData) {
        Map<String, Object> formattedData = new LinkedHashMap<>();
        data.forEach((key, value) -> formattedData.put(key, value));
        formattedData.put("xAxis", xAxisData); // 为每个设备单独添加 xAxis
        return formattedData;
    }



}
