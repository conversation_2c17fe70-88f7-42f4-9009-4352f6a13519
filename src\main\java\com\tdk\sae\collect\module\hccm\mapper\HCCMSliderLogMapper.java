package com.tdk.sae.collect.module.hccm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.hccm.module.po.HCCMResultPO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMSliderPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface HCCMSliderLogMapper extends BaseMapper<HCCMSliderPO> {

    @Delete("DELETE FROM t_biz_hccm_slider_log WHERE id =#{id}")
    int deleteLastData(@Param("id") Long id);

    public List<HCCMSliderPO> getHCCMSliderData(@Param("equipId")String eId, @Param("times") List<LocalDateTime> selectedDateTimes);


}
