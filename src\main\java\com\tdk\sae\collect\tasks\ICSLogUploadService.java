package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.model.po.*;
import com.tdk.sae.collect.module.ics.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ICSLogUploadService {

    private static final Logger logger = LoggerFactory.getLogger(ICSLogUploadService.class);

    private final ICSLogService icsLogService;
    private final ICSPidLogService icsPidLogService;



    private final ICSLogTrayMeanService icsLogTrayMeanService;

    private final MQProducerService mqProducerService;

    private final ICSTrendLogService icsTrendLogService;

    private final ICSHotLaserService icsHotLaserService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<ICSLogPO> logs = icsLogService.list(Wrappers.lambdaQuery(ICSLogPO.class)
                .eq(ICSLogPO::getSynced, 0)
                .orderByAsc(ICSLogPO::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ICS_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/25 * * * * ?")
    private synchronized void uploadUnSyncLogTrayMeans() {
        if (!enableUpload) {
            return;
        }
        List<ICSLogTrayMeanPO> logs = icsLogTrayMeanService.list(Wrappers.lambdaQuery(ICSLogTrayMeanPO.class)
                .eq(ICSLogTrayMeanPO::getSynced, 0)
                .orderByAsc(ICSLogTrayMeanPO::getEndTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ICS_LOG_TRAY_MEAN", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsLogTrayMeanService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/10 * * * * ?")
    private synchronized void uploadUnSyncTrendLogs() {
        if (!enableUpload) {
            return;
        }
        List<ICSTrendLog> logs = icsTrendLogService.list(Wrappers.lambdaQuery(ICSTrendLog.class)
                .eq(ICSTrendLog::getSynced, 0)
                .orderByAsc(ICSTrendLog::getCreatedAt).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ICS_TREND_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsTrendLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/30 * * * * ?")
    private synchronized void uploadUnSyncICSHotLaser() {
        if (!enableUpload) {
            return;
        }
        List<ICSHotLaserLog> logs = icsHotLaserService.list(Wrappers.lambdaQuery(ICSHotLaserLog.class)
                .eq(ICSHotLaserLog::getSynced, 0)
                .orderByAsc(ICSHotLaserLog::getCreatedAt).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ICS_HOT_LASER_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsHotLaserService.saveOrUpdateBatch(logs);
            }
        }
    }
    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void uploadUnSyncPidLogs() {
        if (!enableUpload) {
            return;
        }
        List<ICSPidLogPO> logs = icsPidLogService.list(Wrappers.lambdaQuery(ICSPidLogPO.class)
                .eq(ICSPidLogPO::getSynced, 0)
                .orderByAsc(ICSPidLogPO::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ICS_PID_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsPidLogService.saveOrUpdateBatch(logs);
            }
        }
    }



}
