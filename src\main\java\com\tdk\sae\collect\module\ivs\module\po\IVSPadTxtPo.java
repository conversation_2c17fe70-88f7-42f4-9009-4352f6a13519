package com.tdk.sae.collect.module.ivs.module.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ivs_pad_txt")
public class IVSPadTxtPo extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "check_id")
    private Long checkId;

    @TableField(value = "collect_time")
    private LocalDateTime collectTime;

    @TableField(value = "result")
    private String result;

    @TableField(value = "pos")
    private Integer pos;

    @TableField(value = "direction")
    private String direction;

    @TableField(value = "synced")
    private Integer synced;
}
