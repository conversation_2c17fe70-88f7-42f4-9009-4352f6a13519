package com.tdk.sae.collect.module.auth.cache;

import com.tdk.sae.collect.cache.KeyDefine;

import java.util.List;
import java.util.concurrent.TimeUnit;

public class MenuKeyDefine extends KeyDefine {

    @Override
    public String getName() {
        return "菜单缓存";
    }

    @Override
    public String getPrefix() {
        return "menu";
    }

    @Override
    public long getExpire() {
        return 0;
    }

    @Override
    public TimeUnit getTimeUnit() {
        return null;
    }

    @Override
    public Class<?> getClazz() {
        return List.class;
    }

}
