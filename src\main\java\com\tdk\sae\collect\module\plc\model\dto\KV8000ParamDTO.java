package com.tdk.sae.collect.module.plc.model.dto;

import com.tdk.sae.collect.domain.dto.DomainDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KV8000ParamDTO extends DomainDTO {

    private Long kvId;

    private Long equipId;

    private String variable;

    private String dataType;

    private Integer dataLength;

    private String address;

    private String addressRepresent;

    private String variableName;

    private String commCycle;

}
