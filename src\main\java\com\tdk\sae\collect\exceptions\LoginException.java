package com.tdk.sae.collect.exceptions;

import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import com.tdk.sae.collect.domain.enums.ValueEnum;
import lombok.NonNull;

public class LoginException extends HdsException {

    public LoginException() {}

    public LoginException(@NonNull ResponseCodeEnum status, String message) {
        super(message);
        super.code = status;
        super.data = message;
    }

    public LoginException(@NonNull String code, String message) {
        super(message);
        super.code = ValueEnum.valueToEnum(ResponseCodeEnum.class, code);
        super.data = message;
    }

    public LoginException(@NonNull ResponseCodeEnum status, String message, Throwable cause) {
        super(message, cause);
        super.code = status;
        super.data = message;
    }

    public LoginException(@NonNull String code, String message, Throwable cause) {
        super(message, cause);
        super.code = ValueEnum.valueToEnum(ResponseCodeEnum.class, code);
        super.data = message;
    }

}