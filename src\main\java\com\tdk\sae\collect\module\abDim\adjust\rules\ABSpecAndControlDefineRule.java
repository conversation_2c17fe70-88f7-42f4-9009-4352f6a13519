package com.tdk.sae.collect.module.abDim.adjust.rules;

import com.tdk.sae.collect.module.abDim.model.ABSpecInfo;
import com.tdk.sae.collect.module.abDim.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.abDim.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Component("ABSpecAndControlDefineRule")
public class ABSpecAndControlDefineRule extends AbstractRulesHandler {

    public static final String REGEX = "SPEC线为（Target\\+/-([\\s\\S]*?)）um, 控制线为（Target\\+/-([\\s\\S]*?)）um";

    private final ABSpecInfo specInfo;


    @Override
    public void checkKV8000ParamCache() {}

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("SpecAndControlDefineRule");
        if (isDisabled()) {
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
            ABAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
            detail.setApplyId(getRulePopulate().getApplyId());
            detail.setLogMsg("Auto adjust stopped. Cause: SpecAndControlDefineRule is disabled");
            detail.setLogLevel(0);
            return advice;
        }
        String rule = getRulePopulate().getRule();
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(rule);
        if (!matcher.find()) {
            // TODO: log detail
//            throw new IllegalArgumentException("SpecAndControlDefineRule didn't find spec/control limit!");
            return doNextRule(advice);
        }
        String specLimitStr = matcher.group(1);
        String controlLimitStr = matcher.group(2);
        BigDecimal specLimit = new BigDecimal(specLimitStr);
        BigDecimal controlLimit = new BigDecimal(controlLimitStr);

        // 设定ED,EX,EY的自动调整spec & control触发上下限, 若上次有值, 直接覆盖
        Map<String, Map<String, BigDecimal>> tmpSpecMap = specInfo.getSpecInDbFirst(getEquipment().getId());
        tmpSpecMap.forEach((param, value) -> {
            BigDecimal target = value.get("TARGET");
            value.put("ADJUST_UPPER_SPEC_LIMIT", target.add(specLimit));
            value.put("ADJUST_LOWER_SPEC_LIMIT", target.subtract(specLimit));
            value.put("ADJUST_UPPER_CONTROL_LIMIT", target.add(controlLimit));
            value.put("ADJUST_LOWER_CONTROL_LIMIT", target.subtract(controlLimit));
        });

        return doNextRule(advice);
    }

}
