package com.tdk.sae.collect.module.abDim.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_ab_adjust_rules")
public class ABAdjustRulesPO extends DomainPO {

    @TableField(value = "rule")
    private String rule;

    @TableField(value = "rule_type")
    private Integer ruleType;

    @TableField(value = "rule_weight")
    private Integer ruleWeight;

}
