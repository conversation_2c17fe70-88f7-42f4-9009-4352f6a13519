package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.service.ICSLogService;
import com.tdk.sae.collect.module.ics.service.ICSLogTrayMeanService;
import com.tdk.sae.collect.module.ivs.module.po.IVSAllLogPo;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import com.tdk.sae.collect.module.ivs.module.po.IVSPadTxtPo;
import com.tdk.sae.collect.module.ivs.module.po.IVSTxtPo;
import com.tdk.sae.collect.module.ivs.service.IVSAllLogService;
import com.tdk.sae.collect.module.ivs.service.IVSLogService;
import com.tdk.sae.collect.module.ivs.service.IVSPadTxtService;
import com.tdk.sae.collect.module.ivs.service.IVSTxtService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class IVSLogUploadService {

    private static final Logger logger = LoggerFactory.getLogger(IVSLogUploadService.class);

    private final IVSLogService ivsLogService;

    private final IVSPadTxtService ivsPadTxtService;

    private final IVSTxtService ivsTxtService;

    private final IVSAllLogService ivsAllLogService;

    private final MQProducerService mqProducerService;


    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<IVSLogPo> logs = ivsLogService.list(Wrappers.lambdaQuery(IVSLogPo.class)
                .eq(IVSLogPo::getSynced, 0)
                .orderByAsc(IVSLogPo::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("IVS_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                ivsLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/25 * * * * ?")
    private synchronized void uploadUnSyncLogPads() {
        if (!enableUpload) {
            return;
        }
        List<IVSPadTxtPo> logs = ivsPadTxtService.list(Wrappers.lambdaQuery(IVSPadTxtPo.class)
                .eq(IVSPadTxtPo::getSynced, 0)
                .orderByAsc(IVSPadTxtPo::getCollectTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("IVS_LOG_PAD", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                ivsPadTxtService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/25 * * * * ?")
    private synchronized void uploadUnSyncPadTxt1() {
        if (!enableUpload) {
            return;
        }
        List<IVSTxtPo> logs = ivsTxtService.list(Wrappers.lambdaQuery(IVSTxtPo.class)
                .eq(IVSTxtPo::getSynced, 0)
                .orderByAsc(IVSTxtPo::getCollectTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("IVS_LOG_TXT", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                ivsTxtService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void uploadUnSyncIvsAllLogs() {
        if (!enableUpload) {
            return;
        }
        List<IVSAllLogPo> logs = ivsAllLogService.list(Wrappers.lambdaQuery(IVSAllLogPo.class)
                .eq(IVSAllLogPo::getSynced, 0)
                .orderByAsc(IVSAllLogPo::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("IVS_ALL_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                ivsAllLogService.saveOrUpdateBatch(logs);
            }
        }
    }

}
