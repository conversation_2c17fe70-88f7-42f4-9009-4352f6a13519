package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.config.SnowFlakeIdGenerator;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.adjust.RuleHandleProcessor;
import com.tdk.sae.collect.module.ics.adjust.utils.ReachFullDropHalfList;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogTrayMeanDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RequiredArgsConstructor
@Service
public class ICSAutoAdjustService {

    private final SnowFlakeIdGenerator snowFlakeIdGenerator;

    private final RuleHandleProcessor ruleHandleProcessor;

    private final ICSAdjustLogService icsAdjustLogService;

    private final ICSLogTrayMeanService icsLogTrayMeanService;

    @Resource(name = "${system.write-type}")
    private  IWritePlcService writePlcService;
    private final SystemProperties systemProperties;

    private final ICSAdjustLogDetailService icsAdjustLogDetailService;


    // 取大于此时间的mean值分析
    private static final LocalDateTime ADJUST_START_TIME = LocalDateTime.now();

    private static final Map<String, LocalDateTime> EQUIP_ADJUST_TIME = new HashMap<>(8);


    // mean值分析数据
    public static final Map<String, Map<String, ReachFullDropHalfList<ICSLogTrayMeanDTO>>> DATA_CACHE = new ConcurrentHashMap<>(2);
    // continue 调节数据
    public static final Map<String, Map<String, Integer>> CONTINUE_CACHE = new ConcurrentHashMap<>(2);

    private static final int ParameterCacheCapacity = 100;

    public void startAutoAdjusting(EquipmentDTO equip) {
//        TimeInterval interval = DateUtil.timer();
        AutoAdjustAdvice advice = initAutoAdjustAdvice(equip.getId());

        AbstractRulesHandler rulesHandler = ruleHandleProcessor.populateRules(equip);
        if (rulesHandler == null) {
            ICSAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
            detail.setLogLevel(0);
            detail.setLogMsg("Can't perform adjust. Cause: no rules specified.");
        } else {
            maintainCache(equip.getId(), advice);
            advice = rulesHandler.doHandler(advice);
        }

        handleAutoAdjustAdvice(advice);
//        Console.log("一台ICS一次自动调整消耗时间: {}ms", interval.interval());
    }

    private AutoAdjustAdvice initAutoAdjustAdvice(String equipId) {
        EQUIP_ADJUST_TIME.putIfAbsent(equipId, ADJUST_START_TIME);

        AutoAdjustAdvice advice = new AutoAdjustAdvice().initLog(snowFlakeIdGenerator.nextId());
        advice.setLastStartTime(EQUIP_ADJUST_TIME.get(equipId));
        // 更新调整开始时间
        EQUIP_ADJUST_TIME.put(equipId, LocalDateTime.now());
        return advice;
    }

    private void maintainCache(String equipId, AutoAdjustAdvice advice) {
        Map<String, ReachFullDropHalfList<ICSLogTrayMeanDTO>> cacheDataMap = DATA_CACHE.computeIfAbsent(equipId, k -> new HashMap<>(8));
        Map<String,Integer> continueDataMap=CONTINUE_CACHE.computeIfAbsent(equipId, k -> new HashMap<>(8));
        //initContinue
        continueDataMap.computeIfAbsent("ED1",kk->0);
        continueDataMap.computeIfAbsent("ED2",kk->0);
        continueDataMap.computeIfAbsent("ED3",kk->0);
        continueDataMap.computeIfAbsent("EX1",kk->0);
        continueDataMap.computeIfAbsent("EX2",kk->0);
        continueDataMap.computeIfAbsent("EX3",kk->0);
        continueDataMap.computeIfAbsent("EY1",kk->0);
        continueDataMap.computeIfAbsent("EY2",kk->0);
        continueDataMap.computeIfAbsent("EY3",kk->0);

        List<ICSLogTrayMeanPO> dataList = icsLogTrayMeanService.lambdaQuery()
                .eq(ICSLogTrayMeanPO::getEquipId, equipId)
                .and(m -> m.ge(ICSLogTrayMeanPO::getCalculatedTime, advice.getLastStartTime()))
                .orderByAsc(ICSLogTrayMeanPO::getCalculatedTime).list();

        if (CollectionUtil.isEmpty(dataList)) {
           return;
        }
        dataList.forEach(meanData -> {
            ICSLogTrayMeanDTO data = BeanUtil.copyProperties(meanData, ICSLogTrayMeanDTO.class);
            ReachFullDropHalfList<ICSLogTrayMeanDTO> parameterDataList = cacheDataMap.get(meanData.getParameter());
            if (parameterDataList == null) {
                // 缓存capacity个数据点, 达到capacity时清空前一半的数据点. 规则一般统计不会连续超过一半, 所以当达到capacity时可安全删除前一半, 防止过多重复计算&消耗内存
                // 最坏情况, 一直没有触发规则, 数据累加一直重复计算50+个点数据
                parameterDataList = new ReachFullDropHalfList<>(ParameterCacheCapacity, new ArrayList<>(ParameterCacheCapacity));
                cacheDataMap.put(meanData.getParameter(), parameterDataList);
            }
            List<ICSLogTrayMeanDTO> cacheList = parameterDataList.getList();
            if (!CollectionUtil.isEmpty(cacheList)) {
                ICSLogTrayMeanDTO lastData = cacheList.get(cacheList.size() - 1);
                if (Objects.equals(lastData.getId(), data.getId())) {
                    return;
                }
            }else {
                ICSAdjustLogDetailPO adjust = icsAdjustLogDetailService.lambdaQuery().eq(ICSAdjustLogDetailPO::getMeanId, data.getId()).one();
                if (adjust != null){
                    return;
                }
            }
            parameterDataList.add(data);
        });
    }

    public void clearCache(String equipId) {
        DATA_CACHE.remove(equipId);
    }

    public void clearCache() {
        DATA_CACHE.clear();
    }

    private void handleAutoAdjustAdvice(AutoAdjustAdvice advice) {
        List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        EquipmentDTO kv8000 = kv8000s.get(0);
        Map<String, List<AutoAdjustAdvice.AutoAdjustRuleAdvice>> ruleAdvices = advice.getRuleAdvices();
        //todo 获取logdetail ,判断当前mena的ed1和ed2是否已经有调节记录，若有则通过
        if (CollectionUtil.isNotEmpty(ruleAdvices)) {
            ruleAdvices.forEach((ruleName, ruleAdviceList) -> {
                if (CollectionUtil.isEmpty(ruleAdviceList)) {
                    return;
                }
                for (AutoAdjustAdvice.AutoAdjustRuleAdvice autoAdjustRuleAdvice : ruleAdviceList) {
                    writePlcService.write(kv8000,
                            autoAdjustRuleAdvice.getKv8000Param(),
                            autoAdjustRuleAdvice.getNewValueStr());
                }
            });
        }
        icsAdjustLogService.saveLogs(advice.getAdjustLog());
    }

}
