package com.tdk.sae.collect.module.ics.model.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ics_trend_log")
public class ICSTrendLog extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "barcode")
    private String barcode;

    @TableField(value = "temperature1")
    private String Temperature1;

    @TableField(value = "temperature2")
    private String Temperature2;

    @TableField(value = "temperature3")
    private String Temperature3;

    @TableField(value = "temperature4")
    private String Temperature4;

    @TableField(value = "temperature5")
    private String Temperature5;

    @TableField(value = "flow1")
    private String flow1;

    @TableField(value = "flow2")
    private String flow2;

    @TableField(value = "flow3")
    private String flow3;

    @TableField(value = "flow4")
    private String flow4;

    @TableField(value = "flow5")
    private String flow5;

    @TableField(value = "N2_purity")
    private String N2Purity;

    @TableField(value = "synced")
    private Integer synced;

}
