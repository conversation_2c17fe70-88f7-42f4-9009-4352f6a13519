<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.hccm.mapper.HCCMSliderLogMapper">

    <select id="getHCCMSliderData" resultType="com.tdk.sae.collect.module.hccm.module.po.HCCMSliderPO">
        select * from t_biz_hccm_slider_log m
        where m.equip_id = #{equipId} and
        <foreach item="item" collection="times" separator="or" open="(" close=")" index="index">
            m.log_time &gt;= #{item} and m.log_time &lt; DATE_ADD(#{item}, INTERVAL 1 DAY)
        </foreach>
        order by m.id asc
    </select>
</mapper>
