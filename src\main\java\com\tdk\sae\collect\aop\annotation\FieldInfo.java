package com.tdk.sae.collect.aop.annotation;

import com.tdk.sae.collect.aop.aspect.FieldInfoAspect;

import java.lang.annotation.*;

/**
 * 属性描述
 * @see FieldClass
 * @see FieldInfoAspect
 */
@Documented
@Target(value = ElementType.FIELD)
@Retention(value = RetentionPolicy.RUNTIME)
public @interface FieldInfo {
    /**
     * 属性名称
     *
     * @return 属性名称
     */
    String name() default "";

    /**
     * 表字段
     *
     * @return 表字段
     */
    String columnName() default "";
}