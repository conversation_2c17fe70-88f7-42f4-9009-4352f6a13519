package com.tdk.sae.collect.module.auth.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import com.tdk.sae.collect.exceptions.LoginException;
import com.tdk.sae.collect.module.auth.cache.LoggedInUserCache;
import com.tdk.sae.collect.module.auth.cache.MenuCache;
import com.tdk.sae.collect.module.auth.dto.LoggedInUser;
import com.tdk.sae.collect.module.auth.dto.MenuDTO;
import com.tdk.sae.collect.module.auth.dto.TreeMenuDTO;
import com.tdk.sae.collect.module.auth.mapper.MenuMapper;
import com.tdk.sae.collect.module.auth.mapper.PermissionMapper;
import com.tdk.sae.collect.module.auth.po.MenuPO;
import com.tdk.sae.collect.module.auth.po.PermissionPO;
import com.tdk.sae.collect.module.auth.vo.MenuMetaVO;
import com.tdk.sae.collect.module.auth.vo.MenuPermissionVO;
import com.tdk.sae.collect.module.auth.vo.VueMenuVO;
import com.tdk.sae.collect.pubsub.event.MenuEvent;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class MenuService extends ServiceImpl<MenuMapper, MenuPO> {

    private final PermissionMapper permissionMapper;

    private final ApplicationEventPublisher eventPublisher;

    private final MenuCache menuCache;

    private final LoggedInUserCache loggedInUserCache;

    @Transactional
    public boolean updateById(MenuPO menuPO) {
        Assert.notNull(menuPO, "修改信息不为空");
        Assert.notNull(menuPO.getId(), "id为空");
        Integer isEnabled = menuPO.getIsEnabled();
        boolean updateResult = super.updateById(menuPO);
        // 更新被禁用/启用的子集
        List<MenuDTO> children = getChildrenByParenId(menuPO.getId());
        if (!CollectionUtil.isEmpty(children)) {
            Set<Long> childrenIds = children.stream().map(MenuDTO::getId).collect(Collectors.toSet());
            UpdateWrapper<MenuPO> update = Wrappers.update();
            update.set(MenuPO.IS_ENABLED, isEnabled);
            update.in(MenuPO.ID, childrenIds);
            super.update(update);
        }

        return updateResult;
    }

    @Override
    public boolean removeById(Serializable id) {
        // 获取子集
        Set<Long> ids = Sets.newHashSet((Long) id);
        List<MenuDTO> children = this.getChildrenByParenId((Long) id);
        if (!CollectionUtil.isEmpty(children)) {
            Set<Long> childrenIds = children.stream().map(MenuDTO::getId).collect(Collectors.toSet());
            ids.addAll(childrenIds);
        }
        return super.removeByIds(ids);
    }

    @Nullable
    public List<MenuDTO> getChildrenByParenId(@NotNull Long id) {
        Assert.notNull(id, "id不为空");
        List<MenuPO> entities = super.list();
        List<MenuDTO> menu = BeanUtil.copyToList(entities, MenuDTO.class);
        List<MenuDTO> result = Lists.newArrayList();
        for (MenuDTO dto : menu) {
            // 第一级
            if (dto.getParentId().equals(id)) {
                result.add(dto);
                for (MenuDTO item : menu) {
                    if (dto.getId().equals(item.getParentId())) {
                        result.add(item);
                    }
                }
            }
        }
        return result;
    }

    @SneakyThrows
    public List<TreeMenuDTO> getCurrentMenu(String empNo) {
        LoggedInUser currentUser = loggedInUserCache.get(empNo);
        if (null == currentUser) {
            throw new LoginException(ResponseCodeEnum.USE_LOGIN_ERROR, "当前用户未登录,请登录后重试");
        }
        List<TreeMenuDTO> treeMenu = menuCache.get(currentUser.getId() + "");
        if (CollectionUtil.isEmpty(treeMenu)) {
            eventPublisher.publishEvent(new MenuEvent(this, currentUser.getId()));
        }
        treeMenu = menuCache.get(currentUser.getId() + "");
        if (CollectionUtil.isEmpty(treeMenu)) {
            return Lists.newArrayList();
        }
        return treeMenu;
    }

    public boolean updateCurrentMenu(String empNo) {
        LoggedInUser currentUser = loggedInUserCache.get(empNo);
        if (null == currentUser) {
            throw new LoginException(ResponseCodeEnum.USE_LOGIN_ERROR, "当前用户未登录,请登录后重试");
        }
        eventPublisher.publishEvent(new MenuEvent(this, currentUser.getId()));
        return true;
    }

    public boolean clearUserCache(Long userId) {
        menuCache.del(userId);
        return true;
    }

    public List<TreeMenuDTO> buildTree(List<TreeMenuDTO> menu) {
        List<TreeMenuDTO> trees = new ArrayList<>();
        Set<Long> ids = new HashSet<>();
        for (TreeMenuDTO treeMenu : menu) {
            if (treeMenu.getParentId() == null) {
                trees.add(treeMenu);
            }
            for (TreeMenuDTO it : menu) {
                if (treeMenu.getId().equals(it.getParentId())) {
                    if (treeMenu.getChildren() == null) {
                        treeMenu.setChildren(new ArrayList<>());
                    }
                    treeMenu.getChildren().add(it);
                    ids.add(it.getId());
                }
            }
        }
        if (trees.size() == 0) {
            trees = menu.stream().filter(s -> !ids.contains(s.getId())).collect(Collectors.toList());
        }
        return trees;
    }

    public List<VueMenuVO> buildVueMenus(List<TreeMenuDTO> treeMenu) {
        List<VueMenuVO> list = new LinkedList<>();
        treeMenu.forEach(menu -> {
            if (null != menu) {
                VueMenuVO menuVO = new VueMenuVO();
                // 组件名称
                menuVO.setName(menu.getName());
                // 地址
                menuVO.setPath(menu.getPath());
                // 判断外链
                if (menu.isExternal()) {
                    menuVO.setName(menu.getPath());
                    menuVO.setPath("/" + menu.getName());
                }
                // 判断组件
                if (StrUtil.isNotBlank(menu.getComponent())) {
                    menuVO.setComponent(menu.getComponent());
                } else if (!menu.isExternal()) {
                    menuVO.setComponent("Layout");
                }
                // meta
                MenuMetaVO metaVO = new MenuMetaVO();
                metaVO.setTitle(menu.getLabel());
                metaVO.setIcon(menu.getIcon());
                metaVO.setShowLink(menu.isShowLink());
                metaVO.setI18n(menu.isI18n());
                metaVO.setKeepAlive(menu.isCache());
                metaVO.setRank(menu.getSort());
                metaVO.setShowParent(menu.isShowParent());
                metaVO.setAuthority(menu.getAuthority());
                // iframe
                if (menu.isIframe()) {
                    metaVO.setFrameSrc(menu.getPath());
                }
                menuVO.setMeta(metaVO);
                List<TreeMenuDTO> children = menu.getChildren();
                if (CollectionUtil.isNotEmpty(children)) {
                    menuVO.setRedirect(children.get(0).getPath());
                    menuVO.setChildren(buildVueMenus(children));
                }
                list.add(menuVO);
            }
        });
        return list;
    }

    public List<MenuPO> getSuperior(Long id, List<MenuPO> entities) {
        if (null == id || id == 0) {
            return entities;
        }
        MenuPO menuPO = super.getById(id);
        entities.add(menuPO);
        return getSuperior(menuPO.getParentId(), entities);
    }

    public List<MenuPermissionVO> queryMenuPermissionTree() {
        List<PermissionPO> permissionEntities = permissionMapper.selectList(null);
        List<MenuPO> entities = super.list();
        List<MenuPermissionVO> trees = new ArrayList<>();
        for (MenuPO menuEntity : entities) {

            MenuPermissionVO menuPermission = new MenuPermissionVO();
            if (menuEntity.getParentId() == null) {
                menuPermission.setId(menuEntity.getId());
                menuPermission.setName(menuEntity.getLabel());
                menuPermission.setIsPermission(false);
                trees.add(menuPermission);
            }
            for (MenuPO entity : entities) {
                if (menuEntity.getId().equals(entity.getParentId())) {
                    if (menuPermission.getChildren() == null) {
                        menuPermission.setChildren(new ArrayList<>());
                    }
                    MenuPermissionVO menuPermissionInfo = new MenuPermissionVO();
                    menuPermissionInfo.setName(entity.getLabel());
                    menuPermissionInfo.setId(entity.getId());
                    menuPermissionInfo.setIsPermission(false);
                    menuPermission.getChildren().add(menuPermissionInfo);
                    // 权限
                    List<PermissionPO> permissionList = permissionEntities.stream().filter(e -> e.getMenuId().equals(entity.getId())).collect(Collectors.toList());
                    for (PermissionPO permissionEntity : permissionList) {
                        MenuPermissionVO permission = new MenuPermissionVO();
                        permission.setIsPermission(true);
                        permission.setId(permissionEntity.getId());
                        permission.setName(permissionEntity.getName());
                        if (menuPermissionInfo.getChildren() == null) {
                            menuPermissionInfo.setChildren(new ArrayList<>());
                        }
                        menuPermissionInfo.getChildren().add(permission);
                    }
                }
            }
        }
        return trees;
    }

}
