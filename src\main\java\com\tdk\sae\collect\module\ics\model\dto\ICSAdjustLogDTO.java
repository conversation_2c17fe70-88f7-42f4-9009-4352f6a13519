package com.tdk.sae.collect.module.ics.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode
public class ICSAdjustLogDTO implements Serializable {

    private Long id;

    private Long equipId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer adjusted;

    private Integer adjustType;

    private Long adjustBy;

    private Integer isDeleted = 0;

    private Integer synced = 0;

    @JsonIgnore
    private List<ICSAdjustLogDetailDTO> logDetails;

    @JsonIgnore
    public ICSAdjustLogDetailDTO newDetail() {
        ICSAdjustLogDetailDTO newDetail = new ICSAdjustLogDetailDTO();
        newDetail.setLogId(id);
        logDetails.add(newDetail);
        return newDetail;
    }

}
