package com.tdk.sae.collect.module.hccm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.hccm.mapper.HCCMResultLogMapper;
import com.tdk.sae.collect.module.hccm.module.HCCMTypeEnums;
import com.tdk.sae.collect.module.hccm.module.dto.HCCMResultLogDTO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMResultPO;
import com.tdk.sae.collect.module.ics.mapper.ICSLogMapper;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogDTO;
import com.tdk.sae.collect.module.ivs.module.dto.IVSAllLogDataDto;
import com.tdk.sae.collect.module.ivs.module.dto.IVSLogDTO;
import com.tdk.sae.collect.module.ivs.module.dto.IVSPadTxtDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class HCCMResultLogService extends ServiceImpl<HCCMResultLogMapper, HCCMResultPO> implements ILogFileService {

    private static final String REGEX_HEADER = "PosID,BaseholeX,.*,Time";
    private static final String REGEX_LOG_ROW = "\\d{1,2},.*\n|.*\r\n";

    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        HCCMResultPO lastLog = getOne(Wrappers.lambdaQuery(HCCMResultPO.class)
                .eq(HCCMResultPO::getEquipId, equipId)
                .eq(HCCMResultPO::getFilePath, filePath)
                .orderByDesc(HCCMResultPO::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, ICSLogDTO.class);
    }


    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        for(FileLogDTO logDTO:newLogs){
            HCCMResultLogDTO hccmResultLogDTO= (HCCMResultLogDTO) logDTO;
            try{
                save(BeanUtil.copyProperties(hccmResultLogDTO, HCCMResultPO.class));
            }catch (Exception e){
            }
        }
        return true;
    }

    /**
     * read file to Log records
     * @param f file
     * @return Log records
     */
    public List<HCCMResultLogDTO> digestLogFile(Long equipId, File f) {
        return digestLogFile(equipId, f, null);
    }

    /**
     * read file to Log records which logTime is greater(>) than parameter 'logTime'
     * @param f file
     * @param logTime logTime
     * @return Log records
     */
    public List<HCCMResultLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
        String fileContent = FileUtil.readString(f, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        String[] headers = splitLogHeader(fileContent);
        if (headers == null) {
            return new ArrayList<>();
        }
        return doDigest(equipId, f.getPath(), fileContent, headers, logTime);
    }

    private String[] splitLogHeader(String fileContent) {
        Pattern pattern_t = Pattern.compile(REGEX_HEADER);
        Matcher matcher_t = pattern_t.matcher(fileContent);
        String[] headers = null;
        if(matcher_t.find()){
            String s = matcher_t.group();
            headers = s.split(",");
        }
        return headers;
    }

    /**
     * headers 从静态变为动态
     */
    private List<HCCMResultLogDTO> doDigest(Long equipId, String filePath, String fileContent, String[] defaultHeaders, LocalDateTime logTime) {
        //todo 获取文件名的日期
//        String regex = "\\\\(\\d{5,6})\\\\GlueSize\\\\(\\d{1,2})\\_day\\.txt";
//        Pattern pattern1 = Pattern.compile(regex);
//        Matcher matcher1 = pattern1.matcher(filePath);
//        String now = "";
//        if (matcher1.find()) {
//            String y=matcher1.group(1);
//            String d=matcher1.group(2);
//            if(y.length()<6){
//                //月份补0
//                y=y.substring(0,4)+"0"+y.substring(4);
//            }
//            if(d.length()<2){
//                //日期补0
//                d="0"+d;
//            }
//            date=y.substring(0,4)+"-"+y.substring(4,6)+"-"+d;
//        }
        String now=LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd");

        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);
        List<HCCMResultLogDTO> logDTOList = new ArrayList<>();
        while (matcher.find()) {
            try {
                HCCMResultLogDTO logDTO = new HCCMResultLogDTO();
                logDTO.setFilePath(filePath);
                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
                logDTO.setRawData(rawData);
                logDTO.setEquipId(equipId);

                String[] columns = rawData.split(",");
                if (defaultHeaders == null) {
                    defaultHeaders = new String[]{};
                }
                if (columns.length != defaultHeaders.length) {
                    // 切换打胶模式后, 第一行header不变, 实际上数据会变, 此时需要适应对应数据的header
                    defaultHeaders = getAdaptiveHeaders(columns.length);
                    if (defaultHeaders == null) {
                        throw new RuntimeException("no matched log headers found! rawData:" + rawData);
                    }
                }
                // update description
                logDTO.setDescription(String.join(",", defaultHeaders));

                StringBuilder logVal = new StringBuilder();
                boolean isFilled = true;
                for (int i = 0; i < defaultHeaders.length; i++) {
                    String header = defaultHeaders[i];
                    String column = columns[i].trim();
                    if (header.equals(column)) {
                        isFilled = false;
                        break;
                    }
                    switch (header) {
                        case "Time":
                               String[] a=column.split(":");
                               Integer hour=Integer.parseInt(a[0]);
                               Integer minute=Integer.parseInt(a[1]);
                               Integer seconds=Integer.parseInt(a[2]);
                               if(hour<10){
                                   a[0]="0"+a[0];
                               }
                                if(minute<10){
                                    a[1]="0"+a[1];
                                }
                                if(seconds<10){
                                    a[2]="0"+a[2];
                                }
                                column=now+" "+a[0]+":"+a[1]+":"+a[2];
                                logDTO.setLogTime(LocalDateTimeUtil.parse(column,"yyyy-MM-dd HH:mm:ss"));
                            break;
                        case "PosID":
                            logDTO.setPos(column);
                        default:
                            if (logVal.length() == 0) {
                                logVal.append(column);
                            } else {
                                logVal.append(",").append(column);
                            }
                            break;
                    }
                }
                if (logTime != null && logDTO.getLogTime().compareTo(logTime) < 0) {
                    continue;
                }
                if (isFilled) {
                    logDTOList.add(logDTO);
                }
            } catch (Exception ignore) {}
        }
        return logDTOList;
    }

    private static final String oneDot = "Time,POS,Master,EX1,EY1,ED1,EDX1,EDY1,Result";
    private static final String twoDot = "Time,POS,Master,EX1,EX2,EY1,EY2,ED1,ED2,EDX1,EDY1,EDX2,EDY2,Result";
    private static final String threeDot = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,ED1,ED2,ED3,EDX1,EDY1,EDX2,EDY2,EDX3,EDY3,Result";

    private static final String oneDot2 = "Time,POS,Master,EX1,EY1,EW1,Result";
    private static final String twoDot2 = "Time,POS,Master,EX1,EX2,EY1,EY2,EW1,EW2,Result";
    private static final String threeDot2 = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,EW1,EW2,EW3,Result";

    private String[] getAdaptiveHeaders(int rawDataColumnLength) {
        if (rawDataColumnLength == 7) {
            return oneDot2.split(",");
        } else if (rawDataColumnLength == 10) {
            return twoDot2.split(",");
        } else if (rawDataColumnLength == 13) {
            return threeDot2.split(",");
        } else if (rawDataColumnLength == 9) {
            return oneDot.split(",");
        } else if (rawDataColumnLength == 14) {
            return twoDot.split(",");
        } else if (rawDataColumnLength == 19) {
            return threeDot.split(",");
        }
        return null;
    }
    public Map<String, Map<String,Object>> getHCCMResultData(List<LocalDateTime> select,String label) {
        Map<String, Map<String,Object>> result=new HashMap<>();
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.HCCM_RESULT.getName());
//        LocalDateTime selected = LocalDateTimeUtil.parse(time, "yyyy-MM-dd");
//        LocalDateTime tommrow= selected.plusDays(1);
        initDistribute(result,"data");
        initMeanData(result,"mean_data");

        for(EquipmentDTO e:icsEquips){
            List<HCCMResultPO> hccmResultPOList=null;
            hccmResultPOList=this.baseMapper.getHCCMResultData(e.getId(),select);
//            hccmResultPOList = this.lambdaQuery()
//                    .eq(HCCMResultPO::getEquipId,e.getId())
//                    .ge(HCCMResultPO::getLogTime,selected)
//                    .le(HCCMResultPO::getLogTime,tommrow)
//                    .orderByAsc(HCCMResultPO::getId).list();
            int oneMeanSize = 0;
            HCCMResultPO[] oneMean = new HCCMResultPO[10];
            Queue<Integer> tray = new ArrayDeque<>(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
            for(HCCMResultPO hccmResult:hccmResultPOList){
                int pos = Integer.parseInt(hccmResult.getPos().trim());
                if (Objects.equals(tray.poll(), pos)) {
                    oneMean[pos-1] = hccmResult;
                    oneMeanSize++;
                }else{
                    tray = new ArrayDeque<>(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
                    oneMean = new HCCMResultPO[10];
                    oneMeanSize = 0;
                    if (pos == 1) {
                        tray.poll();
                        oneMean[pos-1] = hccmResult;
                        oneMeanSize++;
                    }
                }
                if (oneMeanSize == 10) {
                    LocalDateTime tempTime=oneMean[4].getLogTime();
                    String tt = LocalDateTimeUtil.format(tempTime, "HH:mm:ss");
                    fillData(result,"data",tt,oneMean,label);
                    fillMeanData(result,"mean_data",tt,oneMean,label);
                    tray = new ArrayDeque<>(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
                    oneMean = new HCCMResultPO[10];
                    oneMeanSize = 0;
                }
            }

        }

        return result;
    }
    public void initMeanData(Map<String, Map<String,Object>> result,String key){
        Map<String,Object> temp=new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<BigDecimal> mean = new ArrayList<>();
        temp.put("xAxis",xAxisData);
        temp.put("mean",mean);
        result.put(key,temp);
    }
    public void fillMeanData(Map<String, Map<String,Object>> result,String Akey,String tt,HCCMResultPO[] oneMean,String label){
        List<String> timesa = (List<String>) result.get(Akey).get("xAxis");
        List<BigDecimal> mean= (List<BigDecimal>) result.get(Akey).get("mean");
        timesa.add(tt);
        BigDecimal sum=BigDecimal.ZERO;
        for(HCCMResultPO h:oneMean){
            BigDecimal value=castLogValue(h,label);
            sum=sum.add(value);
        }
        mean.add(sum.divide(new BigDecimal(10), 2, RoundingMode.HALF_UP));
    }
    public void initDistribute(Map<String, Map<String,Object>> result,String key){
        Map<String,Object> temp=new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<BigDecimal> p0 = new ArrayList<>();
        List<BigDecimal> p1 = new ArrayList<>();
        List<BigDecimal> p2 = new ArrayList<>();
        List<BigDecimal> p3 = new ArrayList<>();
        List<BigDecimal> p4 = new ArrayList<>();
        List<BigDecimal> p5 = new ArrayList<>();
        List<BigDecimal> p6 = new ArrayList<>();
        List<BigDecimal> p7 = new ArrayList<>();
        List<BigDecimal> p8 = new ArrayList<>();
        List<BigDecimal> p9 = new ArrayList<>();
        temp.put("xAxis",xAxisData);
        temp.put("p0",p0);
        temp.put("p1",p1);
        temp.put("p2",p2);
        temp.put("p3",p3);
        temp.put("p4",p4);
        temp.put("p5",p5);
        temp.put("p6",p6);
        temp.put("p7",p7);
        temp.put("p8",p8);
        temp.put("p9",p9);
        result.put(key,temp);
    }

    public void fillData(Map<String, Map<String,Object>> result,String Akey,String tt,HCCMResultPO[] oneMean,String label){
        List<String> timesa = (List<String>) result.get(Akey).get("xAxis");
        List<BigDecimal> p0adata= (List<BigDecimal>) result.get(Akey).get("p0");
        List<BigDecimal> p1adata= (List<BigDecimal>) result.get(Akey).get("p1");
        List<BigDecimal> p2adata= (List<BigDecimal>) result.get(Akey).get("p2");
        List<BigDecimal> p3adata= (List<BigDecimal>) result.get(Akey).get("p3");
        List<BigDecimal> p4adata= (List<BigDecimal>) result.get(Akey).get("p4");
        List<BigDecimal> p5adata= (List<BigDecimal>) result.get(Akey).get("p5");
        List<BigDecimal> p6adata= (List<BigDecimal>) result.get(Akey).get("p6");
        List<BigDecimal> p7adata= (List<BigDecimal>) result.get(Akey).get("p7");
        List<BigDecimal> p8adata= (List<BigDecimal>) result.get(Akey).get("p8");
        List<BigDecimal> p9adata= (List<BigDecimal>) result.get(Akey).get("p9");
        timesa.add(tt);

        HCCMResultPO p0=oneMean[0];
        HCCMResultPO p1=oneMean[1];
        HCCMResultPO p2=oneMean[2];
        HCCMResultPO p3=oneMean[3];
        HCCMResultPO p4=oneMean[4];
        HCCMResultPO p5=oneMean[5];
        HCCMResultPO p6=oneMean[6];
        HCCMResultPO p7=oneMean[7];
        HCCMResultPO p8=oneMean[8];
        HCCMResultPO p9=oneMean[9];
        BigDecimal p0a=castLogValue(p0,label);
        p0adata.add(p0a);

        BigDecimal p1a=castLogValue(p1,label);
        p1adata.add(p1a);

        BigDecimal p2a=castLogValue(p2,label);
        p2adata.add(p2a);

        BigDecimal p3a=castLogValue(p3,label);
        p3adata.add(p3a);

        BigDecimal p4a=castLogValue(p4,label);
        p4adata.add(p4a);

        BigDecimal p5a=castLogValue(p5,label);
        p5adata.add(p5a);

        BigDecimal p6a=castLogValue(p6,label);
        p6adata.add(p6a);

        BigDecimal p7a=castLogValue(p7,label);
        p7adata.add(p7a);

        BigDecimal p8a=castLogValue(p8,label);
        p8adata.add(p8a);

        BigDecimal p9a=castLogValue(p9,label);
        p9adata.add(p9a);
    }
    public BigDecimal castLogValue(HCCMResultPO temp,String label){
        Integer index=label.equals(HCCMTypeEnums.SUSP_X.getDisplayName())?3:4;
        return new BigDecimal(temp.getRawData().split(",")[index]).setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    public Map<String,Object> getXoffsetAndYoffset(List<LocalDateTime> select){
        Map<String,Object> resultMap=new HashMap<>();
        BigDecimal[] xoffset=new BigDecimal[10];
        Arrays.fill(xoffset,BigDecimal.ZERO);
        BigDecimal[] yoffset=new BigDecimal[10];
        Arrays.fill(yoffset,BigDecimal.ZERO);
        resultMap.put("xoffset",xoffset);
        resultMap.put("yoffset",yoffset);
//        LocalDateTime selected=LocalDateTimeUtil.parse(time, DatePattern.NORM_DATE_PATTERN);
//        LocalDateTime tommrow= selected.plusDays(1);
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.HCCM_RESULT.getName());
        for(EquipmentDTO e:icsEquips) {
            List<HCCMResultPO> hccmResultPOList = null;
            hccmResultPOList=this.baseMapper.getHCCMResultData(e.getId(),select);
//            hccmResultPOList = this.lambdaQuery()
//                    .eq(HCCMResultPO::getEquipId, e.getId())
//                    .ge(HCCMResultPO::getLogTime, selected)
//                    .le(HCCMResultPO::getLogTime, tommrow)
//                    .orderByAsc(HCCMResultPO::getId).list();
            //根据pos分组
            Map<String, List<HCCMResultPO>> groupedData = new HashMap<>();
            groupedData = hccmResultPOList.stream()
                    .collect(Collectors.groupingBy(dto -> dto.getPos(), HashMap::new, Collectors.toList()));
            calXoffsetAndYoffsetGroupData(groupedData,xoffset,5);
            calXoffsetAndYoffsetGroupData(groupedData,yoffset,6);
        }

        return resultMap;
    }

    public void calXoffsetAndYoffsetGroupData(Map<String, List<HCCMResultPO>> groupedData,BigDecimal[] offset,Integer dataIndex){
        groupedData.forEach((posStr, groupList) -> {
            try {
                int pos = Integer.parseInt(posStr); // 将pos转换为整数
                int index = pos - 1; // 转换为数组索引
                // 提取每个rawdata的第六个值并转换为BigDecimal
                List<BigDecimal> Values = groupList.stream()
                        .map(po -> {
                            String[] parts = po.getRawData().split(",");
                            return new BigDecimal(parts[dataIndex].trim());
                        })
                        .collect(Collectors.toList());

                // 计算平均值
                if (!Values.isEmpty()) {
                    BigDecimal sum = Values.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal average = sum.divide(
                            new BigDecimal(Values.size()),
                            2, // 保留两位小数
                            RoundingMode.HALF_UP // 四舍五入
                    );
                    offset[index] = average;
                }
            } catch (Exception ee) {
            }
        });
    }
}
