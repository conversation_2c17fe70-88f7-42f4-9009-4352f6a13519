package com.tdk.sae.collect;

import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

//@SpringBootTest
public class MQTest {

//    @Test
//    public void t1(@Autowired MQProducerService mqProducerService) throws InterruptedException {
//        List<BigDecimal> iList = new ArrayList<>();
//        iList.add(new BigDecimal("1314.1315"));
//        iList.add(new BigDecimal("520.521"));
//        mqProducerService.sendAsyncPayload("restag", iList);
//        Thread.sleep(30000);
//    }

}
