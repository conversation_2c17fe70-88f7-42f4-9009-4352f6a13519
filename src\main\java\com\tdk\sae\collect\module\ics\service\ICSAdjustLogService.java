package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ics.mapper.ICSAdjustLogMapper;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ICSAdjustLogService extends ServiceImpl<ICSAdjustLogMapper, ICSAdjustLogPO> {

    private final ICSAdjustLogDetailService icsAdjustLogDetailService;

    @Transactional
    public void saveLogs(ICSAdjustLogDTO adjustLog) {
        ICSAdjustLogPO icsAdjustLogPO = BeanUtil.copyProperties(adjustLog, ICSAdjustLogPO.class, "logDetails");
        // TODO: adjustType is null problem need to be fix
        if (icsAdjustLogPO.getAdjustType() == null || icsAdjustLogPO.getAdjustType() > 3) {
            return;
        }
        if (icsAdjustLogPO.getEndTime() == null) {
            icsAdjustLogPO.setEndTime(LocalDateTime.now());
        }
        List<ICSAdjustLogDetailDTO> logDetails = adjustLog.getLogDetails();
        if (logDetails.size() > 0) {
            icsAdjustLogDetailService.saveDetails(logDetails);
        }
        this.save(icsAdjustLogPO);
    }

}
