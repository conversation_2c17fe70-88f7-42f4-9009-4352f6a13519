package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.hccm.module.po.HCCMResultPO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMSliderPO;
import com.tdk.sae.collect.module.hccm.service.HCCMResultLogService;
import com.tdk.sae.collect.module.hccm.service.HCCMSliderLogService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class HCCMUploadService {

    private static final Logger logger = LoggerFactory.getLogger(HCCMUploadService.class);

    private final HCCMResultLogService hccmResultLogService;
    private final HCCMSliderLogService hccmSliderLogService;
    private final MQProducerService mqProducerService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void uploadUnSyncHCCMResultLogs() {
        if (!enableUpload) {
            return;
        }
        List<HCCMResultPO> logs = hccmResultLogService.list(Wrappers.lambdaQuery(HCCMResultPO.class)
                .eq(HCCMResultPO::getSynced, 0)
                .orderByAsc(HCCMResultPO::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("HCCM_Result_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                hccmResultLogService.saveOrUpdateBatch(logs);
            }
        }
    }


    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<HCCMSliderPO> logs = hccmSliderLogService.list(Wrappers.lambdaQuery(HCCMSliderPO.class)
                .eq(HCCMSliderPO::getSynced, 0)
                .orderByAsc(HCCMSliderPO::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("HCCM_Slider_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                hccmSliderLogService.saveOrUpdateBatch(logs);
            }
        }
    }
}
