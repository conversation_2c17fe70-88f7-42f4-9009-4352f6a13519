package com.tdk.sae.collect.module.abDim.model;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.abDim.mapper.ABConfMapper;
import com.tdk.sae.collect.module.abDim.model.po.ABConfPO;
import com.tdk.sae.collect.module.ics.model.po.ICSConfPO;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ABSpecInfo extends ServiceImpl<ABConfMapper, ABConfPO> {

    private static final String PROJECT_CODE = SpringUtil.getProperty("system.projectCode");


    private static final Map<String, Map<String, BigDecimal>> btsMap = new HashMap<>(16);
    private static final Map<String, Map<String, BigDecimal>> tsdMap = new HashMap<>(16);
    static {

        Map<String, BigDecimal> tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(300));
        tmp.put("TARGET", new BigDecimal(108.5));
        tmp.put("LSL", new BigDecimal(230));
        btsMap.put("A1", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(185));
        tmp.put("TARGET", new BigDecimal(0));
        tmp.put("LSL", new BigDecimal(115));
        btsMap.put("B1", tmp);


        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(350));
        tmp.put("LSL", new BigDecimal(250));
        tsdMap.put("A1", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(400));
        tmp.put("LSL", new BigDecimal(300));
        tsdMap.put("B1", tmp);
    }

    public static Map<String, Map<String, BigDecimal>> getSpec() {
        if (PROJECT_CODE.startsWith("BTS")) {
            return btsMap;
        }
        if (PROJECT_CODE.startsWith("TSD")) {
            return tsdMap;
        }
        return new HashMap<>(2);
    }
    public Map<String, Map<String, BigDecimal>> getSpecInDbFirst(String equipId) {
        List<ABConfPO> confList = this.lambdaQuery().eq(ABConfPO::getEquipId, equipId).list();
        if (CollectionUtil.isEmpty(confList)) {
            return getSpec();
        }
        BigDecimal specLimit = new BigDecimal(10);
        BigDecimal controlLimit = new BigDecimal(2);
        Map<String, Map<String, BigDecimal>> result = new HashMap<>(8);
        confList.forEach(conf -> {
            Map<String, BigDecimal> confMap = result.get(conf.getConfName());
            if (CollectionUtil.isEmpty(confMap)) {
                confMap = new HashMap<>(4);
            }
            // TARGET
            if ("USL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_UPPER_SPEC_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("LSL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_LOWER_SPEC_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("UCL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_UPPER_CONTROL_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("LCL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_LOWER_CONTROL_LIMIT", new BigDecimal(conf.getConfValue()));
            } else {
                // TARGET, ENABLE...
                confMap.put(conf.getConfKey(), new BigDecimal(conf.getConfValue()));
            }
            result.put(conf.getConfName(), confMap);
        });
        return result;
    }
}
