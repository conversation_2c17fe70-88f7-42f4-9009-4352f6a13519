package com.tdk.sae.collect.module.plc.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_kv8000_param")
public class KV8000ParamPO extends DomainPO {

    @TableField(value = "kv_id")
    private Long kvId;

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "variable")
    private String variable;

    @TableField(value = "data_type")
    private String dataType;

    @TableField(value = "data_length")
    private Integer dataLength;

    @TableField(value = "address")
    private String address;

    @TableField(value = "address_represent")
    private String addressRepresent;

    @TableField(value = "variable_name")
    private String variableName;

    @TableField(value = "comm_cycle")
    private String commCycle;

}
