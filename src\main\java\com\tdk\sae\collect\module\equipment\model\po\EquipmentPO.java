package com.tdk.sae.collect.module.equipment.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@Accessors(chain = true)
@TableName("t_biz_equip")
public class EquipmentPO extends DomainPO {

    private static final long serialVersionUID = 543072914893552276L;

    @TableField("equip_name")
    private String equipName;

    @TableField("equip_code")
    private String equipCode;

    @TableField("group_id")
    private Long groupId;

    @TableField("client_code")
    private String clientCode;

    @TableField("address")
    private String address;

    @TableField("equip_type")
    private Integer equipType;

    @TableField("file_save_day")
    private Integer fileSaveDay;

    @TableField("file_user_domain")
    private String fileUserDomain;

    @TableField("file_user_account")
    private String fileUserAccount;

    @TableField("file_user_pwd")
    private String fileUserPwd;

    @TableField("file_updated_time")
    private LocalDateTime fileUpdatedTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EquipmentPO that = (EquipmentPO) o;
        return Objects.equals(equipCode, that.equipCode) && Objects.equals(groupId, that.groupId) && Objects.equals(equipType, that.equipType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(equipCode, groupId, equipType);
    }

}
