package com.tdk.sae.collect.util;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * Date util
 *
 */
@UtilityClass
public class DateUtils {

    private static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

    public final String yM = "yyyy-MM";

    public final String yMd = "yyyy-MM-dd";

    public final String hms = "HH:mm:ss";

    public final String yMdHms = "yyyy-MM-dd HH:mm:ss";

    public String getYMDByDate(Date date){
        if(date==null) return null;
        SimpleDateFormat yMdSim = new SimpleDateFormat(yMd);
        return yMdSim.format(date);
    }

    public String getHmsByDate(Date date){
        if(date==null) return null;
        SimpleDateFormat hmsSim = new SimpleDateFormat(hms);
        return hmsSim.format(date);
    }

    public Date getDateByYMD(String dateStr){
        if(StringUtils.isBlank(dateStr)) return null;
        try {
            SimpleDateFormat yMdSim = new SimpleDateFormat(yMd);
            return yMdSim.parse(dateStr);
        } catch (ParseException e) {
            logger.error(dateStr + "--日期格式化错误",e);
            return null;
        }
    }

    public Date getDateEnd(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH,1);
        calendar.add(Calendar.SECOND,-1);
        return calendar.getTime();
    }

    public String getYMDHMDByDate(Date date){
        if(date==null) return null;
        SimpleDateFormat yMdHmsSim = new SimpleDateFormat(yMdHms);
        return yMdHmsSim.format(date);
    }

    public Date getDateByYMDHMS(String dateStr){
        if(StringUtils.isBlank(dateStr)) return null;
        try {
            SimpleDateFormat yMdHmsSim = new SimpleDateFormat(yMdHms);
            return yMdHmsSim.parse(dateStr);
        } catch (Exception e) {
            logger.error(dateStr + "--日期格式化错误",e);
            return null;
        }
    }

    public Date getDate(String dateStr,String pattern){
        if(StringUtils.isBlank(dateStr) || StringUtils.isBlank(pattern)){
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try {
            return simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            logger.error(dateStr + "--日期格式化错误",e);
            return null;
        }
    }

    public String getDateStr(Date date,String pattern){
        if(date ==null || StringUtils.isBlank(pattern)){
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }

    public String getTodayStrYMD(){
        SimpleDateFormat yMdSim = new SimpleDateFormat(yMd);
        return yMdSim.format(new Date());
    }

    public Date getTodayYMD(){
        try {
            SimpleDateFormat yMdSim = new SimpleDateFormat(yMd);
            return yMdSim.parse(getTodayStrYMD());
        } catch (ParseException e) {
            logger.error("--日期格式化错误",e);
            return null;
        }
    }

    public Date getDateYMD(Date date){
        try {
            SimpleDateFormat yMdSim = new SimpleDateFormat(yMd);
            return yMdSim.parse(yMdSim.format(date));
        } catch (ParseException e) {
            logger.error("--日期格式化错误",e);
            return null;
        }
    }

    /**
     * 大于等于date[0],小于date[1]
     * @return
     */
    public Date[] getTodayStartAndEnd(){
        Date start = getTodayYMD();
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.DAY_OF_MONTH,1);
        Date end = cal.getTime();
        return new Date[]{start,end};
    }

    /**
     * 大于等于date[0],小于date[1]
     * @return
     */
    public Date[] getDateStartAndEnd(Date date){
        Date start = getDateYMD(date);
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.DAY_OF_MONTH,1);
        Date end = cal.getTime();
        return new Date[]{start,end};
    }

    public String getThisMonthStr(){
        SimpleDateFormat yMSim = new SimpleDateFormat(yM);
        return yMSim.format(new Date());
    }

    public String getMonthByDate(Date date){
        SimpleDateFormat yMSim = new SimpleDateFormat(yM);
        return yMSim.format(date);
    }

    /**
     * 大于等于date[0],小于date[1]
     * @return
     */
    public Date[] getMonthStartAndEnd(Date date){
        String monthStr = getMonthByDate(date);
        try {
            SimpleDateFormat yMSim = new SimpleDateFormat(yM);
            Date start = yMSim.parse(monthStr);
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            cal.add(Calendar.MONTH,1);
            Date end = cal.getTime();
            return new Date[]{start,end};
        } catch (ParseException e) {
            logger.error(monthStr + "--日期格式化错误",e);
            return null;
        }
    }

    /**
     * 大于等于date[0],小于date[1]
     * dateStr格式yyyy-MM
     * @return
     */
    public Date[] getMonthStartAndEnd(String dateStr){
        try {
            SimpleDateFormat yMSim = new SimpleDateFormat(yM);
            Date start = yMSim.parse(dateStr);
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            cal.add(Calendar.MONTH,1);
            Date end = cal.getTime();
            return new Date[]{start,end};
        } catch (ParseException e) {
            logger.error(dateStr + "--日期格式化错误",e);
            return null;
        }
    }

    /**
     * create date
     *
     * @return {@link  Date}
     */
    public Date now() {
        return new Date();
    }

    /**
     * 时间追加
     *
     * @param date     时间 不为null
     * @param time     时间 不能小于1
     * @param timeUnit 时间类型 不为空
     * @return 时间完后时间
     */
    public Date add(Date date, long time, TimeUnit timeUnit) {
        if (null == date || time < 0 || timeUnit == null) {
            return null;
        }
        Date result;

        int timeIntValue;

        if (time > Integer.MAX_VALUE) {
            timeIntValue = Integer.MAX_VALUE;
        } else {
            timeIntValue = Long.valueOf(time).intValue();
        }

        // Calc the expiry time
        switch (timeUnit) {
            case DAYS:
                result = addDays(date, timeIntValue);
                break;
            case HOURS:
                result = addHours(date, timeIntValue);
                break;
            case MINUTES:
                result = addMinutes(date, timeIntValue);
                break;
            case SECONDS:
                result = addSeconds(date, timeIntValue);
                break;
            case MILLISECONDS:
                result = addMilliseconds(date, timeIntValue);
                break;
            default:
                result = date;
        }
        return result;
    }

    /**
     * 追加年数
     *
     * @param date   日期，不为空
     * @param amount 追加的年数
     * @return 以追加的新日期
     */
    public Date addYears(final Date date, final int amount) {
        return add(date, Calendar.YEAR, amount);
    }

    /**
     * 追加月数
     *
     * @param date   日期，不为空
     * @param amount 追加的周数
     * @return 以追加的新日期
     */
    public Date addMonths(final Date date, final int amount) {
        return add(date, Calendar.MONTH, amount);
    }

    /**
     * 追加周数
     *
     * @param date   日期，不为空
     * @param amount 追加的周数
     * @return 以追加的新日期
     */
    public Date addWeeks(final Date date, final int amount) {
        return add(date, Calendar.WEEK_OF_YEAR, amount);
    }

    /**
     * 追加天数
     *
     * @param date   日期，不为空
     * @param amount 追加的天数
     * @return 以追加的新日期
     */
    public Date addDays(final Date date, final int amount) {
        return add(date, Calendar.DAY_OF_MONTH, amount);
    }

    /**
     * 追加小时
     *
     * @param date   日期，不为空
     * @param amount 追加的分钟数
     * @return 以追加的新日期
     */
    public Date addHours(final Date date, final int amount) {
        return add(date, Calendar.HOUR_OF_DAY, amount);
    }

    /**
     * 追加分钟数
     *
     * @param date   日期，不为空
     * @param amount 追加的分钟数
     * @return 以追加的新日期
     */
    public Date addMinutes(final Date date, final int amount) {
        return add(date, Calendar.MINUTE, amount);

    }

    /**
     * 追加秒数
     *
     * @param date   日期，不为空
     * @param amount 追加的秒数
     * @return 以追加的新日期
     */
    public Date addSeconds(final Date date, final int amount) {
        return add(date, Calendar.SECOND, amount);
    }

    /**
     * 追加毫秒数，原对象不变
     *
     * @param date   日期不为空
     * @param amount 追加的毫秒数
     * @return 以追加的新日期
     */
    public Date addMilliseconds(final Date date, final int amount) {
        return add(date, Calendar.MILLISECOND, amount);
    }


    private static Date add(Date date, int calendarField, int amount) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }
}
