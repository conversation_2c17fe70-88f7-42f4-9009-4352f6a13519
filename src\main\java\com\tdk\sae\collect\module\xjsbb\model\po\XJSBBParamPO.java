package com.tdk.sae.collect.module.xjsbb.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_xjsbb_param")
public class XJSBBParamPO extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "variable")
    private String variable;

    @TableField(value = "variable_name")
    private String variableName;

    @TableField(value = "data_type")
    private String dataType;

    @TableField(value = "data_unit")
    private String dataUnit;

}
