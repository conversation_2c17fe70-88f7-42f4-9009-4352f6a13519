package com.tdk.sae.collect.module.auth.controller;

import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.module.auth.dto.LoggedInUser;
import com.tdk.sae.collect.module.auth.param.LoginParams;
import com.tdk.sae.collect.module.auth.service.UserLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
public class LoginController {

    private final UserLoginService userLoginService;

    @PostMapping(path = "/login", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<LoggedInUser> loginBy<PERSON>son(@Validated @RequestBody LoginParams loginParams) {
        return doLogin(loginParams);
    }

    private Response<LoggedInUser> doLogin(LoginParams loginParams) {
        LoggedInUser user = userLoginService.login(loginParams);
        return Response.ok(user);
    }

}
