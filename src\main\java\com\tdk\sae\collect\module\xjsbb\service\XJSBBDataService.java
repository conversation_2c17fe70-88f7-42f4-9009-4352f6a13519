package com.tdk.sae.collect.module.xjsbb.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.config.SnowFlakeIdGenerator;
import com.tdk.sae.collect.module.xjsbb.mapper.XJSBBDataMapper;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBDataDTO;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBLogDTO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBDataPO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBParamPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class XJSBBDataService extends ServiceImpl<XJSBBDataMapper, XJSBBDataPO> {

    // 最新参数缓存
    private static final Map<String, String> VAR_VALUE_LOOK_UP = new HashMap<>(128);

    private final SnowFlakeIdGenerator snowFlakeIdGenerator;

    private final XJSBBParamService xjsbbParamService;

    private final XJSBBDataMapper xjsbbDataMapper;

    /**
     * invoked at app start
     */
    public void loadLookupMap() {
        List<XJSBBDataDTO> latestData = xjsbbDataMapper.getLatestData();
        latestData.forEach(d -> {
            String key = d.getVariable();
            String value = d.getReadData();
            if (key != null) {
                VAR_VALUE_LOOK_UP.put(key, value);
            }
        });
    }

    /**
     * 对比数据库中的最新值,如果有变化则保存更新
     * @param logs 新增日志
     */
    public void saveData(List<XJSBBLogDTO> logs) {
        if (logs == null) {
            return;
        }
        List<XJSBBDataPO> newDataList = new ArrayList<>();
        logs.forEach(log -> {
            String var = log.getVarName();
            String lastValue = VAR_VALUE_LOOK_UP.get(var);
            if (!log.getVarValue().equals(lastValue)) {
                XJSBBDataPO newData = new XJSBBDataPO();
                newData.setId(snowFlakeIdGenerator.nextId());
                newData.setEquipId(log.getEquipId());
                XJSBBParamPO param = xjsbbParamService.getOne(Wrappers.lambdaQuery(XJSBBParamPO.class)
                        .eq(XJSBBParamPO::getVariable, var).eq(XJSBBParamPO::getEquipId, log.getEquipId()).last("limit 1"));
                if (param == null) {
                    return;
                }
                newData.setParamId(param.getId());
                newData.setReadData(log.getVarValue());
                newData.setReadTime(log.getLogTime());

                newDataList.add(newData);
                VAR_VALUE_LOOK_UP.put(var, log.getVarValue());
            }
        });
        if (newDataList.size() > 0) {
            saveBatch(newDataList);
        }
    }


}
