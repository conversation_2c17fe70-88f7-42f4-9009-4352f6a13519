package com.tdk.sae.collect.module.auth.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.dto.DomainDTO;
import com.tdk.sae.collect.domain.dto.EntityConverter;
import com.tdk.sae.collect.module.auth.po.UserPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Collection;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class UserDTO extends DomainDTO implements EntityConverter<UserPO> {

    private static final long serialVersionUID = 1L;

    private Long deptId;

    private String empNo;

    private String password;

    private String engName;

    private String zhName;

    private String zhNameSimple;

    private String telType;

    private String title;

    private String email;

    private String phone;

    private String extension;

    private String location;

    private String description;

    private Integer isEnabled;

    /**
     * 角色id
     */
    private Collection<Long> roleIds;

    public void setRoleIds(Collection<Long> roleIds) {
        if (CollectionUtil.isEmpty(roleIds)) {
            return;
        }
        this.roleIds = new ArrayList<>(roleIds);
    }

    /**
     * 权限id
     */
    private Collection<Long> permissionIds;

    public void setPermissionIds(Collection<Long> permissionIds) {
        if (CollectionUtil.isEmpty(permissionIds)) {
            return;
        }
        this.permissionIds = new ArrayList<>(permissionIds);
    }

    /**
     * 用户角色(标识符)
     */
    private Collection<String> role;

    public void setRole(Collection<String> role) {
        if (CollectionUtil.isEmpty(role)) {
            return;
        }
        this.role = new ArrayList<>(role);
    }

    /**
     * 用户权限(标识符)
     */
    private Collection<String> permission;

    public void setPermission(Collection<String> permission) {
        if (CollectionUtil.isEmpty(permission)) {
            return;
        }
        this.permission = new ArrayList<>(permission);
    }
}