package com.tdk.sae.collect.pubsub.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.tdk.sae.collect.module.auth.cache.MenuCache;
import com.tdk.sae.collect.module.auth.dto.TreeMenuDTO;
import com.tdk.sae.collect.module.auth.dto.UserDTO;
import com.tdk.sae.collect.module.auth.po.MenuPO;
import com.tdk.sae.collect.module.auth.po.PermissionPO;
import com.tdk.sae.collect.module.auth.po.UserPO;
import com.tdk.sae.collect.module.auth.service.MenuService;
import com.tdk.sae.collect.module.auth.service.PermissionService;
import com.tdk.sae.collect.module.auth.service.UserAccountService;
import com.tdk.sae.collect.module.auth.service.UserInfoService;
import com.tdk.sae.collect.pubsub.event.MenuEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class MenuEventListener implements ApplicationListener<MenuEvent> {
    private final UserAccountService userAccountService;
    private final UserInfoService userInfoService;
    private final PermissionService permissionService;
    private final MenuService menuService;
    private final MenuCache menuCache;

    @Override
    public void onApplicationEvent(@Nonnull MenuEvent event) {
        Long userId = event.getUserId();
        if (null == userId) {
            return;
        }
        UserDTO user = findUserById(userId);
        if (null == user) {
            return;
        }
        List<TreeMenuDTO> menu = findMenuByUser(user);
        if (CollectionUtil.isEmpty(menu)) {
            return;
        }
        menuCache.set(menu, userId + "");
    }

    private List<TreeMenuDTO> findMenuByUser(UserDTO user) {
        if (null == user) {
            return null;
        }
        Collection<Long> permissionIds = user.getPermissionIds();
        if (CollectionUtil.isEmpty(permissionIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PermissionPO> queryWrapper = Wrappers.lambdaQuery(PermissionPO.class)
                .in(PermissionPO::getId, permissionIds)
                .select(PermissionPO::getMenuId, PermissionPO::getValue);
        // 权限
        List<PermissionPO> permissionEntities = permissionService.list(queryWrapper);
        if (CollectionUtil.isEmpty(permissionEntities)) {
            return Lists.newArrayList();
        }
        Map<Long, List<String>> permissionMapping = permissionEntities.stream().collect(Collectors.groupingBy(PermissionPO::getMenuId,
                Collectors.mapping(PermissionPO::getValue, Collectors.toList())));
        // 菜单
        List<Long> menuIds = permissionEntities.parallelStream().map(PermissionPO::getMenuId).collect(Collectors.toList());
        Set<MenuPO> entities = Sets.newHashSet();
        for (Long menuId : menuIds) {
            entities.addAll(menuService.getSuperior(menuId, Lists.newArrayList()));
        }
        List<MenuPO> menuEntities = entities.stream().sorted(Comparator.comparing(MenuPO::getSort)).collect(Collectors.toList());
        List<TreeMenuDTO> treeMenu = BeanUtil.copyToList(menuEntities, TreeMenuDTO.class);
        for (TreeMenuDTO menu : treeMenu) {
            menu.setAuthority(permissionMapping.get(menu.getId()));
        }
        return treeMenu;
    }

    private UserDTO findUserById(Long userId) {
        if (null == userId) {
            return null;
        }
        UserPO userPO = userAccountService.getUserByUserId(userId);
        if (null == userPO) {
            return null;
        }
        return userInfoService.loadUserByUsername(userPO.getEmpNo());
    }
}
