package com.tdk.sae.collect.module.plc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.plc.mapper.KV8000DataMapper;
import com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@RequiredArgsConstructor
@Service
public class KV8000DataService extends ServiceImpl<KV8000DataMapper, KV8000DataPO> {

    private final KV8000DataMapper kv8000DataMapper;

    public List<KV8000DataDTO> getLatestParamDataByEquipId(String equipId) {
        return kv8000DataMapper.getLatestParamDataByEquipId(equipId);
    }

    public List<KV8000DataDTO> getLatestParamDateByEquipIdAndParam(String equipId, List<String> params) {
        return kv8000DataMapper.getLatestParamDateByEquipIdAndParam(equipId, params);
    }

    public Map<String, Object> buildPlcChart(String equipId, String parameter, List<LocalDateTime> selectedDateTimes, List<LocalDateTime> xAxisTimes) {
        if (parameter.contains("ED")){
            parameter = parameter.replaceAll("ED", "GT");
        }
        List<KV8000DataPO> eachPeriodLastData = new ArrayList<>();

        String finalParameter = parameter;
        selectedDateTimes.forEach(t -> {
            KV8000DataPO data = kv8000DataMapper.beforeOne(equipId, finalParameter, t);
            if (data != null) {
                eachPeriodLastData.add(data);
            }
        });
        List<KV8000DataPO> data = kv8000DataMapper.getPlcDataInTimeRange(equipId, parameter, selectedDateTimes);
        eachPeriodLastData.addAll(data);
        eachPeriodLastData.sort(Comparator.comparing(KV8000DataPO::getReadTime));
        return doBuildPlcChart(eachPeriodLastData, xAxisTimes);
    }

    public Map<String, Object> buildPlcChart(String equipId, String parameter, LocalDateTime lastTime, List<LocalDateTime> xAxisTimes) {
        if (parameter.contains("ED")){
            parameter = parameter.replaceAll("ED", "GT");
        }
        List<KV8000DataPO> eachPeriodLastData = new ArrayList<>();

        KV8000DataPO data = kv8000DataMapper.beforeOne(equipId, parameter, lastTime);
        if (data != null) {
            eachPeriodLastData.add(data);
        }
        List<KV8000DataPO> dataList = kv8000DataMapper.getPlcDataLatest(equipId, parameter, lastTime);
        eachPeriodLastData.addAll(dataList);
        eachPeriodLastData.sort(Comparator.comparing(KV8000DataPO::getReadTime));
        return doBuildPlcChart(eachPeriodLastData, xAxisTimes);
    }

    public Map<String, Object> doBuildPlcChart(List<KV8000DataPO> plcData, List<LocalDateTime> xAxisTimes) {
        Map<String, Object> result = new HashMap<>(2);
        List<String> plcDataList = new ArrayList<>();
        LocalDateTime[] plcTimeArr = plcData.stream().map(KV8000DataPO::getReadTime).toArray(LocalDateTime[]::new);
        xAxisTimes.forEach(dPoint -> {
            int idx = findNearLatestIdx(plcTimeArr, dPoint);
            if (idx < 0) {
                plcDataList.add(null);
                return;
            }
            KV8000DataPO fillData = plcData.get(idx);
            plcDataList.add(fillData.getReadData());
        });
        result.put("plcData", plcDataList);
        return result;
    }

    public static int findNearLatestIdx(LocalDateTime[] a, LocalDateTime k){
        int l = 0;
        int r = a.length - 1;
        while(l < r){

            int mid = (l + r + 1) / 2;
            if(a[mid].compareTo(k) <= 0) {
                l = mid;
            }else{
                r= mid - 1;
            }
        }
        if(l > a.length - 1 ) return -1;
        if(a[l].compareTo(k) > 0) return -1;
        return l;
    }

}
