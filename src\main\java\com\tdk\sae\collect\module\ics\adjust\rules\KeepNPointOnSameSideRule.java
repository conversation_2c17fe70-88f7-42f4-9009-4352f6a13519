package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.lang.Console;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import org.springframework.stereotype.Component;

@Component("KeepNPointOnSameSideRule")
public class KeepNPointOnSameSideRule extends AbstractRulesHandler {
    @Override
    public void checkKV8000ParamCache() {

    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("KeepNPointOnSameSideRule");
        return doNextRule(advice);
    }
}
