package com.tdk.sae.collect.module.plc.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_kv8000_data")
public class KV8000DataPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "param_id")
    private Long paramId;

    @TableField(value = "command")
    private String command;

    @TableField(value = "raw_data")
    private String rawData;

    @TableField(value = "read_data")
    private String readData;

    @TableField(value = "read_time")
    private LocalDateTime readTime;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "synced")
    private Integer synced;

}
