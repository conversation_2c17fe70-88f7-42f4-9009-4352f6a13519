package com.tdk.sae.collect.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tdk.sae.collect.aop.annotation.FieldInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
public class DomainDTO implements Serializable {

    private static final long serialVersionUID = -3936221129683979906L;

    @FieldInfo(name = "id", columnName = "id")
    private Long id;

    // 默认非删,删除需要传值覆盖
    private Integer isDeleted = 0;

    private Long createdBy = 0L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt = LocalDateTime.now();

    private Long updatedBy = 0L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt = LocalDateTime.now();

    private String description = "";

}
