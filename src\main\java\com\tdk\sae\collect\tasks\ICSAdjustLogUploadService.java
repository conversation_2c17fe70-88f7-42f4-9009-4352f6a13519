package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogPO;
import com.tdk.sae.collect.module.ics.service.ICSAdjustLogDetailService;
import com.tdk.sae.collect.module.ics.service.ICSAdjustLogService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ICSAdjustLogUploadService {

    private final ICSAdjustLogService icsAdjustLogService;
    private final ICSAdjustLogDetailService icsAdjustLogDetailService;

    private final MQProducerService mqProducerService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/15 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<ICSAdjustLogPO> logs = icsAdjustLogService.list(Wrappers.lambdaQuery(ICSAdjustLogPO.class)
                .eq(ICSAdjustLogPO::getSynced, 0)
                .orderByAsc(ICSAdjustLogPO::getEndTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ADJUST_ICS_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsAdjustLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/15 * * * * ?")
    private synchronized void uploadUnSyncLogDetails() {
        if (!enableUpload) {
            return;
        }
        List<ICSAdjustLogDetailPO> logs = icsAdjustLogDetailService.list(Wrappers.lambdaQuery(ICSAdjustLogDetailPO.class)
                .eq(ICSAdjustLogDetailPO::getSynced, 0)
                .orderByAsc(ICSAdjustLogDetailPO::getWriteTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ADJUST_ICS_DETAIL_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsAdjustLogDetailService.saveOrUpdateBatch(logs);
            }
        }
    }

}
