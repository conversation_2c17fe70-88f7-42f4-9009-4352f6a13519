package com.tdk.sae.collect.module.auth.cache;

import com.tdk.sae.collect.cache.KeyDefine;
import com.tdk.sae.collect.module.auth.param.LoginParams;

import java.util.concurrent.TimeUnit;

public class LoginParamsKeyDefine extends KeyDefine {

    @Override
    public String getName() {
        return "登录参数缓存";
    }

    @Override
    public String getPrefix() {
        return "login_param";
    }

    @Override
    public long getExpire() {
        return 30L;
    }

    @Override
    public TimeUnit getTimeUnit() {
        return TimeUnit.SECONDS;
    }

    @Override
    public Class<?> getClazz() {
        return LoginParams.class;
    }

}