package com.tdk.sae.collect.domain.enums;

import com.tdk.sae.collect.domain.enums.ValueEnum;

/**
 * 响应状态
 * S: 成功
 * A: 授权,权限相关
 * P: 参数相关
 * W: 警告
 * E: 错误
 * 数字按分类,顺序递增
 */
public enum ResponseCodeEnum implements ValueEnum<String> {
    /**
     * 请求成功
     */
    OK("S000", "请求成功"),

    /**
     * 用户登录异常
     */
    USE_LOGIN_ERROR("A002", "用户登录异常"),

    /**
     * 用户密码错误
     */
    USER_PASSWORD_ERROR("A001", "用户密码错误"),

    /**
     * 请求必填参数为空
     */
    PARAMS_REQUIRED_IS_NULL("P000", "请求必填参数为空"),

    /**
     * 访问未授权
     */
    UNAUTHORIZED("A000", "访问未授权"),

    /**
     * 系统错误
     */
    FAIL("E000", "系统执行错误"),
    NOT_FOUND("E404", "资源不存在"),
    ;

    private String code;
    private String message;

    ResponseCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String getValue() {
        return this.code;
    }
}
