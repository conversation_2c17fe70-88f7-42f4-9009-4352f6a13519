package com.tdk.sae.collect.module.abDim.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.config.SnowFlakeIdGenerator;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.abDim.adjust.ABRuleHandleProcessor;
import com.tdk.sae.collect.module.abDim.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.abDim.adjust.utils.ReachFullDropHalfList;
import com.tdk.sae.collect.module.abDim.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDetailDTO;
import com.tdk.sae.collect.module.abDim.model.dto.ABLogTrayMeanDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogDetailPO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogTrayMeanPO;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;

import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RequiredArgsConstructor
@Service
public class ABAutoAdjustService {

    private final SnowFlakeIdGenerator snowFlakeIdGenerator;

    private final ABRuleHandleProcessor ruleHandleProcessor;

    private final ABAdjustLogService icsAdjustLogService;

    private final ABLogTrayMeanService icsLogTrayMeanService;
    @Resource(name = "${system.write-type}")
    private IWritePlcService writePlcService;
    private final SystemProperties systemProperties;
    private final KV8000ParamService kv8000ParamService;

    private final ABAdjustLogDetailService abAdjustLogDetailService;
    public static boolean flag = true;

    // 取大于此时间的mean值分析
    private static final LocalDateTime ADJUST_START_TIME = LocalDateTime.now();

    private static final Map<String, LocalDateTime> EQUIP_ADJUST_TIME = new HashMap<>(8);


    // mean值分析数据
    public static final Map<String, Map<String, ReachFullDropHalfList<ABLogTrayMeanDTO>>> DATA_CACHE = new ConcurrentHashMap<>(2);
    public static final Map<String, Map<String, List<LocalDateTime>>> ADJUST_CACHE = new ConcurrentHashMap<>(2);

    private static final int ParameterCacheCapacity = 100;

    public void startAutoAdjusting(EquipmentDTO equip) {
//        TimeInterval interval = DateUtil.timer();
        AutoAdjustAdvice advice = initAutoAdjustAdvice(equip.getId());

        AbstractRulesHandler rulesHandler = ruleHandleProcessor.populateRules(equip);
        if (rulesHandler == null) {
            ABAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
            detail.setLogLevel(0);
            detail.setLogMsg("Can't perform adjust. Cause: no rules specified.");
        } else {
            maintainCache(equip.getId(), advice);
            advice = rulesHandler.doHandler(advice);
        }

        handleAutoAdjustAdvice(advice);
//        Console.log("一台ICS一次自动调整消耗时间: {}ms", interval.interval());
    }

    private AutoAdjustAdvice initAutoAdjustAdvice(String equipId) {
        EQUIP_ADJUST_TIME.putIfAbsent(equipId, ADJUST_START_TIME);

        AutoAdjustAdvice advice = new AutoAdjustAdvice().initLog(snowFlakeIdGenerator.nextId());
        advice.setLastStartTime(EQUIP_ADJUST_TIME.get(equipId));
        // 更新调整开始时间
        EQUIP_ADJUST_TIME.put(equipId, LocalDateTime.now());
        return advice;
    }

    private void maintainCache(String equipId, AutoAdjustAdvice advice) {
        Map<String, ReachFullDropHalfList<ABLogTrayMeanDTO>> cacheDataMap = DATA_CACHE.computeIfAbsent(equipId, k -> new HashMap<>(8));
        Map<String,List<LocalDateTime>> adjustDataMap=ADJUST_CACHE.computeIfAbsent(equipId, k -> new HashMap<>(8));
        adjustDataMap.computeIfAbsent("A1",kk->new ArrayList<>());
        adjustDataMap.computeIfAbsent("B1",kk->new ArrayList<>());
        List<ABLogTrayMeanPO> dataList = icsLogTrayMeanService.lambdaQuery()
                .eq(ABLogTrayMeanPO::getEquipId, equipId)
                .and(m -> m.ge(ABLogTrayMeanPO::getCalculatedTime, advice.getLastStartTime()))
                .orderByAsc(ABLogTrayMeanPO::getCalculatedTime).list();


        if (CollectionUtil.isEmpty(dataList)) {
           return;
        }
        dataList.forEach(meanData -> {
            ABLogTrayMeanDTO data = BeanUtil.copyProperties(meanData, ABLogTrayMeanDTO.class);
            ReachFullDropHalfList<ABLogTrayMeanDTO> parameterDataList = cacheDataMap.get(meanData.getParameter());
            if (parameterDataList == null) {
                // 缓存capacity个数据点, 达到capacity时清空前一半的数据点. 规则一般统计不会连续超过一半, 所以当达到capacity时可安全删除前一半, 防止过多重复计算&消耗内存
                // 最坏情况, 一直没有触发规则, 数据累加一直重复计算50+个点数据
                parameterDataList = new ReachFullDropHalfList<>(ParameterCacheCapacity, new ArrayList<>(ParameterCacheCapacity));
                cacheDataMap.put(meanData.getParameter(), parameterDataList);
            }
            List<ABLogTrayMeanDTO> cacheList = parameterDataList.getList();
            if (!CollectionUtil.isEmpty(cacheList)) {
                ABLogTrayMeanDTO lastData = cacheList.get(cacheList.size() - 1);
                if (Objects.equals(lastData.getId(), data.getId())) {
                    return;
                }
            }else {
                ABAdjustLogDetailPO adjust = abAdjustLogDetailService.lambdaQuery().eq(ABAdjustLogDetailPO::getMeanId, data.getId()).one();
                if (adjust != null){
                    return;
                }
            }
            parameterDataList.add(data);
        });
    }

    public void clearCache(String equipId) {
        DATA_CACHE.remove(equipId);
    }

    public void clearCache() {
        DATA_CACHE.clear();
    }

    private void handleAutoAdjustAdvice(AutoAdjustAdvice advice) {
        List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        EquipmentDTO kv8000 = kv8000s.get(0);
        Map<String, List<AutoAdjustAdvice.AutoAdjustRuleAdvice>> ruleAdvices = advice.getRuleAdvices();
        if (CollectionUtil.isNotEmpty(ruleAdvices)) {
            ruleAdvices.forEach((ruleName, ruleAdviceList) -> {
                if (CollectionUtil.isEmpty(ruleAdviceList)) {
                    return;
                }
                for (AutoAdjustAdvice.AutoAdjustRuleAdvice autoAdjustRuleAdvice : ruleAdviceList) {
                    String code = systemProperties.getClientCode();
                    if(code.equals("4D-GPM2") || code.equals("4D-M40")){
                        for(int i=1;i<=10;i++){
                            String addressRepresent=autoAdjustRuleAdvice.getKv8000Param().getAddressRepresent();
                            String finalAddressRepresent = addressRepresent;
                            if(i>1){
                                finalAddressRepresent=finalAddressRepresent.replace("1",String.valueOf(i));
                            }
                            String writeAddressRepresent=finalAddressRepresent;
                            KV8000ParamPO paramPO = kv8000ParamService.lambdaQuery()
                                    .eq(KV8000ParamPO::getEquipId, autoAdjustRuleAdvice.getKv8000Param().getEquipId()).and(c -> c.eq(KV8000ParamPO::getAddressRepresent, writeAddressRepresent))
                                    .last("limit 1").one();
                            KV8000ParamDTO currentParam=BeanUtil.copyProperties(paramPO,KV8000ParamDTO.class);
                            writePlcService.write(kv8000,
                                    currentParam,
                                    autoAdjustRuleAdvice.getNewValueStr());
                        }
                    }else{
                        writePlcService.write(kv8000,
                                autoAdjustRuleAdvice.getKv8000Param(),
                                autoAdjustRuleAdvice.getNewValueStr());
                    }

                }
            });
        }
        icsAdjustLogService.saveLogs(advice.getAdjustLog());
    }

}
