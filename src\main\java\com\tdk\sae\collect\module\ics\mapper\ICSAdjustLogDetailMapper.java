package com.tdk.sae.collect.module.ics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Mapper
public interface ICSAdjustLogDetailMapper extends BaseMapper<ICSAdjustLogDetailPO> {

    List<ICSAdjustLogDetailPO> getAdjustLogDetailsAscByTime(@Param("eIds") Set<String> eIds, @Param("lastTime") LocalDateTime lastTime);

    List<ICSAdjustLogDetailPO> getAdjustLogDetailsAscByDate(@Param("eIds") Set<String> eIds, @Param("times") List<LocalDateTime> selectedDateTimes);

}
