package com.tdk.sae.collect.tasks;


import com.tdk.sae.collect.module.abDim.mapper.ABConfMapper;
import com.tdk.sae.collect.module.abDim.model.po.ABConfPO;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.mapper.ICSConfMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSConfPO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ICSConfUploadService {


    private static final Logger logger = LoggerFactory.getLogger(ICSConfUploadService.class);

    private final MQProducerService mqProducerService;

    private final ICSConfMapper icsConfMapper;

    private final ABConfMapper abConfMapper;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0 0 0 * * ?")
    public synchronized void uploadUnSyncICSConf() {
        if (!enableUpload) {
            return;
        }
        List<ICSConfPO> list = icsConfMapper.selectList(null);
        if (!list.isEmpty()) {
            logger.info("同步ICS_CONF数据到服务端");
            mqProducerService.sendSyncPayload("ICS_CONF", list);
        }
    }

    @Scheduled(cron = "0 0 0 * * ?")
    public synchronized void uploadUnSyncABConf() {
        if (!enableUpload) {
            return;
        }
        List<ABConfPO> list = abConfMapper.selectList(null);
        if (!list.isEmpty()) {
            logger.info("同步AB_CONF数据到服务端");
            mqProducerService.sendSyncPayload("AB_CONF", list);
        }
    }
}
