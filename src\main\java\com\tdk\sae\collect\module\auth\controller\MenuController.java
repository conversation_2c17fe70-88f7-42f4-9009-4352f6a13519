package com.tdk.sae.collect.module.auth.controller;

import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.module.auth.dto.TreeMenuDTO;
import com.tdk.sae.collect.module.auth.service.MenuService;
import com.tdk.sae.collect.module.auth.utils.CommonUtils;
import com.tdk.sae.collect.module.auth.vo.MenuPermissionVO;
import com.tdk.sae.collect.module.auth.vo.VueMenuVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/system/menu")
public class MenuController {

    private final MenuService menuService;


    /**
     * 菜单权限树
     *
     * @return 菜单权限树
     */
    @GetMapping("/query/tree/permission")
    public Response<List<MenuPermissionVO>> queryMenuPermissionTree() {
        List<MenuPermissionVO> menuPermission = menuService.queryMenuPermissionTree();
        return Response.ok(menuPermission);
    }

    /**
     * 更新当前用户缓存
     *
     * @return 是否成功
     */
    @GetMapping("/update/current")
    public Response<String> updateCurrentMenu(HttpServletRequest request) {
        menuService.updateCurrentMenu(CommonUtils.getAccessToken(request));
        return Response.ok("更新成功");
    }

    /**
     * 获取当前用户所属菜单
     *
     * @return 菜单树
     */
    @GetMapping("/get/current/tree")
    public Response<List<TreeMenuDTO>> getCurrentMenu(HttpServletRequest request) {
        List<TreeMenuDTO> currentMenu = menuService.getCurrentMenu(CommonUtils.getAccessToken(request));
        List<TreeMenuDTO> treeMenu = menuService.buildTree(currentMenu);
        return Response.ok(treeMenu);
    }

    /**
     * 获取当前用户router
     *
     * @return vue router
     */
    @GetMapping("/get/current/router")
    public Response<List<VueMenuVO>> getCurrentRouter(HttpServletRequest request) {
        List<TreeMenuDTO> currentMenu = menuService.getCurrentMenu(CommonUtils.getAccessToken(request));
        List<TreeMenuDTO> treeMenu = menuService.buildTree(currentMenu);
        List<VueMenuVO> routerMenu = menuService.buildVueMenus(treeMenu);
        return Response.ok(routerMenu);
    }


}
