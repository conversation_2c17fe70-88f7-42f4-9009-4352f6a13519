package com.tdk.sae.collect.tasks;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Console;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.module.abDim.mapper.ABLogMapper;
import com.tdk.sae.collect.module.abDim.mapper.ABLogTrayMeanMapper;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.mapper.ICSLogMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.service.ICSLogService;
import com.tdk.sae.collect.module.ics.service.ICSLogTrayMeanService;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000DataService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
public class CleanDataService {


    private static final Logger logger = LoggerFactory.getLogger(CleanDataService.class);
    private final ICSLogService icsLogService;
    private final KV8000DataService kv8000DataService;
    private final ICSLogTrayMeanService icsLogTrayMeanService;
    private final SystemProperties systemProperties;
    private final ABLogMapper abLogMapper;
    private final ABLogTrayMeanMapper abLogTrayMeanMapper;
    private static final Integer cleanSize=100000;

    /**
     * 每天5点执行
     */
    @Scheduled(cron = "0 0 6 * * ?")
    public void cleanData() {
        TimeInterval interval = DateUtil.timer();
        LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1).minusHours(12);
        String time = getLastMonthTime();
        String laset_week_time = getLastWeekTime();
        logger.info("开始清理一个月前或一周前所有的数据，时间：{}", time);

        QueryWrapper<ICSLogPO> queryWrapper = new QueryWrapper<>();
        Long dataSize=icsLogService.getBaseMapper().selectCount(queryWrapper);

        if(dataSize>cleanSize){
            icsLogService.getBaseMapper().deleteICSLogData(time);
        }

        QueryWrapper<ICSLogTrayMeanPO> queryWrapper1 = new QueryWrapper<>();
        Long dataSize1=icsLogTrayMeanService.getBaseMapper().selectCount(queryWrapper1);

        if(dataSize1>cleanSize){
            icsLogTrayMeanService.getBaseMapper().deleteICSLogMeanData(time);
        }
        QueryWrapper<KV8000DataPO> queryWrapper2 = new QueryWrapper<>();
        Long dataSize2=kv8000DataService.getBaseMapper().selectCount(queryWrapper2);

        if(dataSize2>cleanSize){
            if(checkIsSpecialMachine()){
                kv8000DataService.getBaseMapper().deleteKV8000Data(laset_week_time);
            }else{
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String last=oneMonthAgo.format(formatter);
                kv8000DataService.getBaseMapper().deleteKV8000Data(last);
                kv8000DataService.getBaseMapper().deleteKV8000Data(time);
            }
        }
        Console.log("清理所有数据消耗时间: {}ms", interval.interval());

    }
    /**
     * 每天5点执行
     */
    @Scheduled(cron = "0/8 * * * * ?")
    public void cleanOtherData() {
        if(checkIsSpecialMachine()){
            //获取two days前的时间，删除two days前的数据
            String time = getLastTwoDayTime();
            logger.info("开始清理two days前other的数据，时间：{}", time);
            TimeInterval interval = DateUtil.timer();
            kv8000DataService.getBaseMapper().deleteKV8000OtherData(time);
            Console.log("清理other数据消耗时间: {}ms", interval.interval());
        }
    }
    /**
     * 获取一个月前的时间
     */
    private String getLastTwoDayTime() {
        LocalDateTime twoDaysAgo = LocalDateTime.now().minusDays(2);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return twoDaysAgo.format(formatter);
    }
    /**
     * 获取一个月前的时间
     */
    private String getLastMonthTime() {
        LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return oneMonthAgo.format(formatter);
    }
    /**
     * 获取一个xq前的时间
     */
    private String getLastWeekTime() {
        LocalDateTime oneMonthAgo = LocalDateTime.now().minusWeeks(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return oneMonthAgo.format(formatter);
    }
    private boolean checkIsSpecialMachine(){
        if(systemProperties.getClientCode().equals("4D-5B-B12")||
                systemProperties.getClientCode().equals("4D-6B-B15")||
                systemProperties.getClientCode().equals("4D-M25M26")||
                systemProperties.getClientCode().equals("4D-M27M28")||
                systemProperties.getClientCode().equals("4D-M29M30")){
            return true;
        }
        return false;
    }
    //删除AB数相关数据
    @Scheduled(cron = "0 0 * * * ?")
    public void delABData(){
       abLogMapper.deleteData(getLastMonthTime());
       abLogTrayMeanMapper.deleteData(getLastMonthTime());
    }

}
