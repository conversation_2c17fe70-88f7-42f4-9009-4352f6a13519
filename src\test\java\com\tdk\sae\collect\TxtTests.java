package com.tdk.sae.collect;

import cn.hutool.core.lang.Console;
import org.apache.commons.io.monitor.FileAlterationListener;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

//@SpringBootTest
class TxtTests {

	// TODO: 测试断线重连, 取消共享再恢复共享, 新增文件测试是否仍然可以正常监控目录状态
	public static void main(String[] args) throws Exception {
//		FileAlterationMonitor monitor = new FileAlterationMonitor(5000);
//		String folderStr = "\\\\192.168.3.161\\ICSData";
//		File folder = new File(folderStr);
//		// 初始化扫描完成, 开始监听新数据
//		FileAlterationObserver observer = new FileAlterationObserver(folder);
//		observer.addListener(new FileAlterationListener() {
//			@Override
//			public void onStart(FileAlterationObserver fileAlterationObserver) {
//
//			}
//
//			@Override
//			public void onDirectoryCreate(File file) {
//				Console.log("dir create");
//			}
//
//			@Override
//			public void onDirectoryChange(File file) {
//				Console.log("dir change");
//			}
//
//			@Override
//			public void onDirectoryDelete(File file) {
//				Console.log("dir del");
//			}
//
//			@Override
//			public void onFileCreate(File file) {
//				Console.log("file create");
//			}
//
//			@Override
//			public void onFileChange(File file) {
//				Console.log("file change");
//			}
//
//			@Override
//			public void onFileDelete(File file) {
//				Console.log("file del");
//			}
//
//			@Override
//			public void onStop(FileAlterationObserver fileAlterationObserver) {
//
//			}
//		});
//		monitor.addObserver(observer);
//		monitor.start();
//		Thread.sleep(600000);
	}

}
