package com.tdk.sae.collect.module.plc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface KV8000DataMapper extends BaseMapper<KV8000DataPO> {

    List<KV8000DataDTO> getLatestParamDataByEquipId(@Param("equipId") String equipId);

    List<KV8000DataDTO> getLatestParamDateByEquipIdAndParam(@Param("equipId") String equipId, @Param("params") List<String> params);

    KV8000DataPO beforeOne(@Param("equipId") String equipId, @Param("param") String parameter, @Param("time") LocalDateTime time);

    List<KV8000DataPO> getPlcDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> times);

    List<KV8000DataPO> getPlcDataLatest(@Param("equipId") String equipId, @Param("param") String parameter, @Param("lastTime") LocalDateTime lastTime);

    @Delete("DELETE FROM t_biz_kv8000_data WHERE read_time < #{time}")
    int deleteKV8000Data(@Param("time") String time);

    @Delete("DELETE FROM t_biz_kv8000_data WHERE param_id not in (select id from t_biz_kv8000_param where address_represent !='') and read_time < #{time}")
    int deleteKV8000OtherData(@Param("time") String time);
}
