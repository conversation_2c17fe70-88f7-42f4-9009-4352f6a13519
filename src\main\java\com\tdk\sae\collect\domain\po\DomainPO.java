package com.tdk.sae.collect.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class DomainPO extends BasePO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    public static final String ID = "id";

}
