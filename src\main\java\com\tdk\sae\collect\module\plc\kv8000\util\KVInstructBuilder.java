package com.tdk.sae.collect.module.plc.kv8000.util;

import com.tdk.sae.collect.module.plc.kv8000.KVDataType;

import javax.annotation.Nonnull;

public class KVInstructBuilder {

    private final StringBuilder iBuilder = new StringBuilder();
    private KVDataType type;
    private int n;

    private int checker = 0;

    public static KVInstructBuilder getInstance() {
        return new KVInstructBuilder();
    }

    /**
     * 设置按数据类型读出
     * @param type 数据类型
     * @return this
     */
    public KVInstructBuilder read(@Nonnull KVDataType type) {
        iBuilder.append("RD");
        this.type = type;
        checker = 1;
        return this;
    }

    /**
     * 设置按数据类型连续读出
     * @param n 连续读出个数
     * @param type 数据类型
     * @return this
     */
    public KVInstructBuilder read(int n, @Nonnull KVDataType type) {
        if (n <= 1) {
            return read(type);
        }
        iBuilder.append("RDS");
        this.type = type;
        this.n = n;
        checker = 1;
        return this;
    }

    /**
     * 设置按数据类型写入
     * @param type 数据类型
     * @return this
     */
    public KVInstructBuilder write(@Nonnull KVDataType type) {
        iBuilder.append("WR");
        this.type = type;
        checker = 2;
        return this;
    }

    /**
     * 设置按数据类型连续写入
     * @param n 写入个数
     * @param type 数据类型
     * @return this
     */
    public KVInstructBuilder write(int n, @Nonnull KVDataType type) {
        if (n <= 1) {
            return write(type);
        }
        iBuilder.append("WRS");
        this.type = type;
        this.n = n;
        checker = 2;
        return this;
    }

    /**
     * 设置读出/写入地址 例:DM15000
     * @param address 地址
     * @return this
     */
    public KVInstructBuilder address(@Nonnull String address) {
        if ((checker & 1) == 0 && (checker & 2) == 0) {
            throw new RuntimeException("read/write mode not set");
        }
        iBuilder.append(" ");
        iBuilder.append(address);
        iBuilder.append(type.getDataType());
        checker += 4;
        return this;
    }

    /**
     * 设置写入的值
     * @param values 单个或多个值，基于写入模式是否连续
     * @return this
     */
    public KVInstructBuilder value(@Nonnull Object...values) {
        if ((checker & 4) == 0 || (checker & 2) == 0) {
            throw new RuntimeException("not in write mode or haven't set address!");
        }
        if ((checker & 8) == 8) {
            throw new RuntimeException("value has already set!");
        }
        if (values.length == 0) {
            throw new RuntimeException("no value");
        }
        if (n == 0 && values.length > 1) {
            throw new RuntimeException("too many values, expected one value");
        }
        if (n > 0 && n != values.length) {
            throw new RuntimeException("wrong length of values, expected " + n + " values, but in fact " + values.length + " values");
        }

        if (values.length > 1) {
            iBuilder.append(" ");
            iBuilder.append(n);
        }
        for (Object value : values) {
            String iVal = null;
            String valStr = value.toString();
            switch (type) {
                case U_16DEC:
                    if (value instanceof Boolean) {
                        iVal = (Boolean) value ? "1" : "0";
                    } else {
                        int val = Integer.parseInt(valStr);
                        if (val > Short.MAX_VALUE || val < Short.MIN_VALUE) {
                            throw new RuntimeException("value out of range!");
                        }
                        String sVal = Integer.toBinaryString(val);
                        if (sVal.length() == 32) {
                            sVal = sVal.substring(16);
                        }
                        int uInt = Integer.parseInt(sVal, 2);
                        iVal = Integer.toString(uInt);
                    }
                    break;
                case U_32DEC:
                    if (value instanceof Boolean) {
                        iVal = (Boolean) value ? "1" : "0";
                    } else if (value instanceof Float || value instanceof Double) {
                        float fVal = Float.parseFloat(valStr);
                        int IntVal = Float.floatToRawIntBits(fVal);
                        String binaryStr = Integer.toBinaryString(IntVal);
                        iVal = Long.toString(Long.parseLong(binaryStr, Character.MIN_RADIX));
                    } else {
                        long lVal = Long.parseLong(valStr);
                        if (lVal > 4294967295L || lVal < Integer.MIN_VALUE) {
                            throw new RuntimeException("value out of range!");
                        }
                        if (lVal <= Integer.MAX_VALUE) {
                            iVal = Integer.toUnsignedString((int) lVal, 10);
                        } else {
                            iVal = Long.toUnsignedString(lVal, 10);
                        }
                    }
                    break;
                default:
                    // 其它的暂时不支持
                    throw new RuntimeException("unsupported type!");
            }
            iBuilder.append(" ");
            iBuilder.append(iVal);
        }
        checker += 8;
        return this;
    }

    /**
     * 生成指令
     * @return 指令
     */
    public String build() {
        String instruct = null;
        if ((checker & 1) == 1) {
            if (n > 0) {
                instruct = iBuilder.append(" ").append(n).append("\r").toString();
            } else {
                instruct = iBuilder.append("\r").toString();
            }
        } else if ((checker & 2) == 2) {
            instruct = iBuilder.append("\r").toString();
        }
        if (instruct == null) {
            throw new RuntimeException("check your build params!");
        }
        return instruct;
    }

}
