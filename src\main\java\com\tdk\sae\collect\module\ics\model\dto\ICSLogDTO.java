package com.tdk.sae.collect.module.ics.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class ICSLogDTO extends FileLogDTO {

    private Long equipId;

    private String filePath;

    private String rawData;

    private String pos;

    private String master;

    private String logValue;

    private Integer synced = 0;

    private String result;

    private Integer isDeleted = 0;

    private Long createdBy = 0L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt = LocalDateTime.now();

    private Long updatedBy = 0L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt = LocalDateTime.now();

    private String description = "";

}
