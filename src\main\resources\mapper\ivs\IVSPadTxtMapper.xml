<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.ivs.mapper.IVSPadTxtMapper">

    <select id="getPadData" resultType="com.tdk.sae.collect.module.ivs.module.dto.IVSPadTxtDTO">

        select pos,direction,count(*) as bad_count

        from t_biz_ivs_pad_txt

        where

        result!='OK'
        and ( collect_time &gt;= #{start} and collect_time &lt;= #{end} )

        group by pos,direction
    </select>

</mapper>
