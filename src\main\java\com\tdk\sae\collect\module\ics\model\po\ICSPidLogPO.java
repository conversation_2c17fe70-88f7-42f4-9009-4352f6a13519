package com.tdk.sae.collect.module.ics.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ics_pid_log")
public class ICSPidLogPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "parameter")
    private String parameter;

    @TableField(value = "log_value")
    private BigDecimal logValue;

    @TableField(value = "log_time")
    private LocalDateTime logTime;

    @TableField(value = "write_time")
    private LocalDateTime writeTime;

    @TableField(value = "log_msg")
    private String logMsg;

    @TableField(value = "from_value")
    private String fromValue;

    @TableField(value = "to_value")
    private String toValue;

    @TableField(value = "param_id")
    private Long paramId;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "synced")
    private Integer synced = 0;

}
