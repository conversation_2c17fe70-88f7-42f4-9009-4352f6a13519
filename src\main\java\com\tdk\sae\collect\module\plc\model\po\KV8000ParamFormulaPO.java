package com.tdk.sae.collect.module.plc.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_kv8000_param_formula")
public class KV8000ParamFormulaPO extends DomainPO {

    @TableField(value = "param_id")
    private Long paramId;

    @TableField(value = "address_represent")
    private String addressRepresent;

    @TableField(value = "formula")
    private String formula;

}
