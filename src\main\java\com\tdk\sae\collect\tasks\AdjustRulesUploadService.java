package com.tdk.sae.collect.tasks;


import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import com.tdk.sae.collect.module.ics.service.ICSAdjustRulesApplyService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class AdjustRulesUploadService {

    private static final Logger logger = LoggerFactory.getLogger(AdjustRulesUploadService.class);

    private final MQProducerService mqProducerService;

    private final ICSAdjustRulesApplyService icsAdjustRulesApplyService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0 0 0 * * ?")
    public synchronized void uploadUnSyncEquipStatus() {
        if (!enableUpload) {
            return;
        }
        List<ICSAdjustRulesApplyPO> list = icsAdjustRulesApplyService.list();
        if (!list.isEmpty()) {
            logger.info("同步ADJUST_RULES_APPLY数据到服务端");
            mqProducerService.sendSyncPayload("ADJUST_ICS_ADJUST_RULES_APPLY", list);
        }
    }
}
