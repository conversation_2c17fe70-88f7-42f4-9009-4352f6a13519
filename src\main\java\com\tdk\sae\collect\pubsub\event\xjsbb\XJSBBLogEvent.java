package com.tdk.sae.collect.pubsub.event.xjsbb;

import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBLogDTO;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class XJSBBLogEvent extends ApplicationEvent {

    @Getter
    private final List<XJSBBLogDTO> logs;

    @SuppressWarnings("unchecked")
    public XJSBBLogEvent(Object source, List<? extends FileLogDTO> newLogs) {
        super(source);
        this.logs = (List<XJSBBLogDTO>) newLogs;
    }

}
