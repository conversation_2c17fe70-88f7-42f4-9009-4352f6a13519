package com.tdk.sae.collect.module.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.auth.mapper.RoleMapper;
import com.tdk.sae.collect.module.auth.po.RolePO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
public class RoleService extends ServiceImpl<RoleMapper, RolePO> {

    private final RoleMapper roleMapper;

    public List<RolePO> getEnabledRoleByIds(@Nonnull Collection<Long> roleIds) {
        Assert.notNull(roleIds, "角色id不为空");
        LambdaQueryWrapper<RolePO> queryWrapper = Wrappers
                .lambdaQuery(RolePO.class)
                .in(RolePO::getId, roleIds)
                .select(RolePO::getId, RolePO::getRole);
        return roleMapper.selectList(queryWrapper);
    }

}
