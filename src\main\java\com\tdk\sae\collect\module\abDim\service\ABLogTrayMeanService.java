package com.tdk.sae.collect.module.abDim.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.abDim.mapper.ABLogTrayMeanMapper;
import com.tdk.sae.collect.module.abDim.model.ABSpecInfo;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustChartDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogTrayMeanPO;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.pubsub.event.adjust.AdjustEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@RequiredArgsConstructor
@Service
public class ABLogTrayMeanService extends ServiceImpl<ABLogTrayMeanMapper, ABLogTrayMeanPO> {

    private final ABLogTrayMeanMapper icsLogTrayMeanMapper;

    private final ApplicationEventPublisher eventPublisher;

    private static final String[] PARAMETER = new String[]{"A1","B1"};

    private static LocalDateTime recordTime=null;
    private static String head = null;
    private final ABLogService abLogService;
    private final ABSpecInfo specInfo;

    public List<ABAdjustChartDTO> getChartDataInTimeRange(String equipId, String parameter, List<LocalDateTime> selectedDateTimes) {
        return icsLogTrayMeanMapper.getChartDataInTimeRange(equipId, parameter, selectedDateTimes);
    }

    public List<ABAdjustChartDTO> getChartDataLatest(String equipId, String parameter, LocalDateTime lastTime) {
        return icsLogTrayMeanMapper.getChartDataLatest(equipId, parameter, lastTime);
    }

    public Map<String, Object> buildMeanChart(String equipId, String parameter, List<LocalDateTime> selectedDateTimes) {
        List<ABAdjustChartDTO> chartDTOList = getChartDataInTimeRange(equipId, parameter, selectedDateTimes);
        return doBuildMeanChart(equipId,parameter, chartDTOList);
    }

    // 为什么要重新查询调整参数：调整参数过程中可能会有延迟，导致前端如果请求了数据但是调整参数还未更新的话就会丢失掉这次调整的显示。所以现在改为每次都按选中的日期获取所有调整参数
    public Map<String, Object> buildMeanChart(String equipId, String parameter, LocalDateTime lastTime, List<LocalDateTime> selectedDateTimes) {
        List<ABAdjustChartDTO> chartDTOList = getChartDataLatest(equipId, parameter, lastTime);
        Map<String, Object> result = doBuildMeanChart(equipId,parameter, chartDTOList);
        // override adjust points
        List<Object[]> adjustData = new ArrayList<>();
        List<ABAdjustChartDTO> adjustChartDTOList = icsLogTrayMeanMapper.getAdjustDataInTimeRange(equipId, parameter, selectedDateTimes);
        if (adjustChartDTOList.size() > 0) {
            adjustChartDTOList.forEach(point -> {
                Object[] tmp = new Object[2];
                tmp[0] = LocalDateTimeUtil.format(point.getEndTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                tmp[1] = point.getToValue();
                adjustData.add(tmp);
            });
            result.put("adjustData", adjustData);
        }
        return result;
    }

    public Map<String, Object> doBuildMeanChart(String equipId,String parameter, List<ABAdjustChartDTO> chartDTOList) {
        Map<String, Object> result = new HashMap<>(6);
        List<String> xAxisData = new ArrayList<>();
        List<LocalDateTime> xAxisTimes = new ArrayList<>();
        List<BigDecimal> meanData = new ArrayList<>();
        List<Object[]> adjustData = new ArrayList<>();
        chartDTOList.forEach(point -> {
            xAxisTimes.add(point.getEndTime());
            String time = LocalDateTimeUtil.format(point.getEndTime(), "yyyy-MM-dd HH:mm:ss.SSS");
            xAxisData.add(time);
            meanData.add(point.getMeanValue());
            if (StrUtil.isNotEmpty(point.getToValue())) {
                Object[] tmp = new Object[2];
                tmp[0] = time;
                tmp[1] = point.getToValue();
                adjustData.add(tmp);
            }
        });
        Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(equipId);

        BigDecimal mus = specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
        BigDecimal mls = specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
        BigDecimal muc = specMap.get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
        BigDecimal mlc = specMap.get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
        BigDecimal target = specMap.get(parameter).get("TARGET");
        Map<String, String> spec = new HashMap<>();
        spec.put("mus", mus.toPlainString());
        spec.put("mls", mls.toPlainString());
        spec.put("target", target.toPlainString());
        spec.put("muc", muc.toPlainString());
        spec.put("mlc", mlc.toPlainString());
        result.put("specs", spec);

        result.put("xAxisTimes", xAxisTimes);
        result.put("xAxisData", xAxisData);
        result.put("meanData", meanData);
        result.put("adjustData", adjustData);
        return result;
    }

    public boolean checkMaster(ABLogPO log){
        String[] des=log.getDescription().split(",");
        int resultIndex=ArrayUtil.indexOf(des,"Result");
        int HgaMasterIndex=ArrayUtil.indexOf(des,"HGA");
        String[] values = log.getRawData().split(",");
        Integer result=Integer.parseInt(values[resultIndex].trim());
        String HgaMaster=values[HgaMasterIndex].trim();
        if(result==410 && HgaMaster.equals("N")){
            return true;
        }else{
            return false;
        }
    }
    public boolean checkMasterByPos(ABLogPO log){
        String[] des=log.getDescription().split(",");
        int resultIndex=ArrayUtil.indexOf(des,"Result");
        int HgaMasterIndex=ArrayUtil.indexOf(des,"HGA");
        int vHoleIndex=ArrayUtil.indexOf(des,"VHole_X(um)");
        String[] values = log.getRawData().split(",");
        Integer result=Integer.parseInt(values[resultIndex].trim());
        String HgaMaster=values[HgaMasterIndex].trim();
        String vHole=values[vHoleIndex].trim();
        if((result==410 && HgaMaster.equals("N")) || (result==411 && HgaMaster.equals("N") && !vHole.equals("0"))){
            return true;
        }else{
            return false;
        }
    }


    public String getHead(ABLogPO log){
        String[] des=log.getDescription().split(",");
        int headIndex=ArrayUtil.indexOf(des,"Head");
        String[] values = log.getRawData().split(",");
        return values[headIndex].trim();
    }

    @Transactional
    public void calculateABLogTrayMean(EquipmentDTO equip, List<ABLogPO> icsLogPOList){
        List<ABLogTrayMeanPO> calculatedMeans = new ArrayList<>();
        ABLogPO[] oneMean = new ABLogPO[2];
        int oneMeanSize = 0;
        for (ABLogPO icsLogPO : icsLogPOList) {
            boolean master=checkMaster(icsLogPO);
            String headNow = getHead(icsLogPO);
            if(master){
                if(recordTime==null){
                    oneMean[0] = icsLogPO;
                    oneMeanSize++;
                    recordTime=icsLogPO.getLogTime();
                    head= headNow;
                }else{
                    //比较时间
                    LocalDateTime secondTime=icsLogPO.getLogTime();
                    long millisDiff = ChronoUnit.MILLIS.between(recordTime, secondTime);
                    boolean isWithinThreshold = millisDiff <= 5000;
                    if(isWithinThreshold && head.equals(headNow)){
                        oneMean[1] = icsLogPO;
                        oneMeanSize++;
                        //开始计算mean
                        LocalDateTime now = LocalDateTime.now();
                        Long eId = Long.parseLong(equip.getId());
                        List<ABLogTrayMeanPO> means = new ArrayList<>();
                        ABLogTrayMeanPO a1Mean = new ABLogTrayMeanPO();
                        a1Mean.setEquipId(eId);
                        a1Mean.setParameter("A1");
                        a1Mean.setCalculatedTime(now);
                        means.add(a1Mean);
                        ABLogTrayMeanPO b1Mean = new ABLogTrayMeanPO();
                        b1Mean.setEquipId(eId);
                        b1Mean.setParameter("B1");
                        b1Mean.setCalculatedTime(now);
                        means.add(b1Mean);
                        BigDecimal A1 = new BigDecimal(0);
                        BigDecimal B1 = new BigDecimal(0);
                        for (int i = 0; i < oneMean.length; i++) {
                            ABLogPO[] finalOneMean = oneMean;
                            int finalI = i;
                            if (i == 0) {
                                means.forEach(m -> m.setStartTime(finalOneMean[finalI].getLogTime()));
                            }
                            if (i == 1) {
                                means.forEach(m -> m.setEndTime(finalOneMean[finalI].getLogTime()));
                            }
                            ABLogPO log = oneMean[i];
                            String[] columns = log.getDescription().split(",");
                            String[] values = log.getRawData().split(",");
                            for (int i1 = 0; i1 < columns.length; i1++) {
                                String tmp=values[i1].trim();
                                switch (columns[i1]) {
                                    case "A_Dim(um)":
                                        A1 = A1.add(new BigDecimal(tmp));
                                        break;
                                    case "B_Dim(um)":
                                        B1 = B1.add(new BigDecimal(tmp));
                                        break;
                                }
                            }
                        }
                        a1Mean.setMeanValue(A1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                        b1Mean.setMeanValue(B1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                        a1Mean.setHead(headNow);
                        b1Mean.setHead(headNow);
                        calculatedMeans.addAll(means);
                        oneMean = new ABLogPO[2];
                        oneMeanSize = 0;
                        recordTime=null;
                        head=null;
                    }else{
                        oneMean[0] = icsLogPO;
                        oneMeanSize=1;
                        recordTime=icsLogPO.getLogTime();
                        head= headNow;
                    }
                }
            }else{
                oneMean = new ABLogPO[2];
                oneMeanSize = 0;
                recordTime=null;
                head=null;
            }
        }
        if (calculatedMeans.size() > 0) {
            this.saveBatch(calculatedMeans);
            //mean值变化，推送数据到前端实现图表自动刷新
            System.out.println("推送数据");
            for (String address : PARAMETER) {
                eventPublisher.publishEvent(new AdjustEvent(this,address,equip.getId()));
            }
        }
        recordTime=null;
        head=null;
    }

    public Map<String, Map<String,Object>> getDistributeData(String time){
        Map<String, Map<String,Object>> result=new HashMap<>();
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        LocalDateTime selected = LocalDateTimeUtil.parse(time, "yyyy-MM-dd");
        LocalDateTime tommrow= selected.plusDays(1);
        initDistribute(result,"AB-L-A");
        initDistribute(result,"AB-L-B");
        initDistribute(result,"AB-R-A");
        initDistribute(result,"AB-R-B");
        for(EquipmentDTO e:icsEquips){
            LocalDateTime record=null;
            List<ABLogPO> abLogPOList=null;
            long id=Long.parseLong(e.getId());
            abLogPOList = abLogService.lambdaQuery()
                    .eq(ABLogPO::getEquipId,id)
                    .ge(ABLogPO::getLogTime,selected)
                    .le(ABLogPO::getLogTime,tommrow)
                    .orderByAsc(ABLogPO::getId).list();
            int oneMeanSize = 0;
            ABLogPO[] oneMean = new ABLogPO[10];
            Queue<Integer> tray = new ArrayDeque<>(Arrays.asList(0,1,2,3,4,5,6,7,8,9));
            for(ABLogPO abLogPO:abLogPOList){
                boolean master=checkMasterByPos(abLogPO);
                if(!master){
                    tray = new ArrayDeque<>(Arrays.asList(0,1,2,3,4,5,6,7,8,9));
                    oneMean = new ABLogPO[10];
                    oneMeanSize = 0;
                    record=null;
                    continue;
                }
                int pos = Integer.parseInt(abLogPO.getPos().trim());
                if (Objects.equals(tray.poll(), pos)) {
                    oneMean[pos] = abLogPO;
                    oneMeanSize++;
                    if(record==null){
                        record=abLogPO.getLogTime();
                    }else{
                        LocalDateTime secondTime=abLogPO.getLogTime();
                        long millisDiff = ChronoUnit.MILLIS.between(record, secondTime);
                        boolean isWithinThreshold = millisDiff <= 6000;
                        if(!isWithinThreshold){
                            tray = new ArrayDeque<>(Arrays.asList(0,1,2,3,4,5,6,7,8,9));
                            oneMean = new ABLogPO[10];
                            oneMeanSize = 0;
                        }
                        record=null;
                    }
                } else {
                    tray = new ArrayDeque<>(Arrays.asList(0,1,2,3,4,5,6,7,8,9));
                    oneMean = new ABLogPO[10];
                    oneMeanSize = 0;
                    record=null;
                    if (pos == 0) {
                        tray.poll();
                        oneMean[pos] = abLogPO;
                        oneMeanSize++;
                        record=abLogPO.getLogTime();
                    }
                }
                if (oneMeanSize == 10) {
                    if(e.getEquipCode().equals("AB-L-LOG")){
                        LocalDateTime tempTime=oneMean[4].getLogTime();
                        String tt = LocalDateTimeUtil.format(tempTime, "HH:mm:ss");
                        fillData(result,"AB-L-A","AB-L-B",tt,oneMean);
                    }else{
                        LocalDateTime tempTime=oneMean[4].getLogTime();
                        String tt = LocalDateTimeUtil.format(tempTime, "HH:mm:ss");
                        fillData(result,"AB-R-A","AB-R-B",tt,oneMean);
                    }
                    tray = new ArrayDeque<>(Arrays.asList(0,1,2,3,4,5,6,7,8,9));
                    oneMean = new ABLogPO[10];
                    oneMeanSize = 0;
                    record=null;
                }
            }
        }
        return result;
    }
    public void initDistribute(Map<String, Map<String,Object>> result,String key){
        Map<String,Object> temp=new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<BigDecimal> p0 = new ArrayList<>();
        List<BigDecimal> p1 = new ArrayList<>();
        List<BigDecimal> p2 = new ArrayList<>();
        List<BigDecimal> p3 = new ArrayList<>();
        List<BigDecimal> p4 = new ArrayList<>();
        List<BigDecimal> p5 = new ArrayList<>();
        List<BigDecimal> p6 = new ArrayList<>();
        List<BigDecimal> p7 = new ArrayList<>();
        List<BigDecimal> p8 = new ArrayList<>();
        List<BigDecimal> p9 = new ArrayList<>();
        temp.put("xAxis",xAxisData);
        temp.put("p0",p0);
        temp.put("p1",p1);
        temp.put("p2",p2);
        temp.put("p3",p3);
        temp.put("p4",p4);
        temp.put("p5",p5);
        temp.put("p6",p6);
        temp.put("p7",p7);
        temp.put("p8",p8);
        temp.put("p9",p9);
        result.put(key,temp);
    }
    public void fillData(Map<String, Map<String,Object>> result,String Akey,String Bkey,String tt,ABLogPO[] oneMean){
        List<String> timesa = (List<String>) result.get(Akey).get("xAxis");
        List<String> timesb = (List<String>) result.get(Bkey).get("xAxis");
        List<BigDecimal> p0adata= (List<BigDecimal>) result.get(Akey).get("p0");
        List<BigDecimal> p0bdata= (List<BigDecimal>) result.get(Bkey).get("p0");
        List<BigDecimal> p1adata= (List<BigDecimal>) result.get(Akey).get("p1");
        List<BigDecimal> p1bdata= (List<BigDecimal>) result.get(Bkey).get("p1");
        List<BigDecimal> p2adata= (List<BigDecimal>) result.get(Akey).get("p2");
        List<BigDecimal> p2bdata= (List<BigDecimal>) result.get(Bkey).get("p2");
        List<BigDecimal> p3adata= (List<BigDecimal>) result.get(Akey).get("p3");
        List<BigDecimal> p3bdata= (List<BigDecimal>) result.get(Bkey).get("p3");
        List<BigDecimal> p4adata= (List<BigDecimal>) result.get(Akey).get("p4");
        List<BigDecimal> p4bdata= (List<BigDecimal>) result.get(Bkey).get("p4");
        List<BigDecimal> p5adata= (List<BigDecimal>) result.get(Akey).get("p5");
        List<BigDecimal> p5bdata= (List<BigDecimal>) result.get(Bkey).get("p5");
        List<BigDecimal> p6adata= (List<BigDecimal>) result.get(Akey).get("p6");
        List<BigDecimal> p6bdata= (List<BigDecimal>) result.get(Bkey).get("p6");
        List<BigDecimal> p7adata= (List<BigDecimal>) result.get(Akey).get("p7");
        List<BigDecimal> p7bdata= (List<BigDecimal>) result.get(Bkey).get("p7");
        List<BigDecimal> p8adata= (List<BigDecimal>) result.get(Akey).get("p8");
        List<BigDecimal> p8bdata= (List<BigDecimal>) result.get(Bkey).get("p8");
        List<BigDecimal> p9adata= (List<BigDecimal>) result.get(Akey).get("p9");
        List<BigDecimal> p9bdata= (List<BigDecimal>) result.get(Bkey).get("p9");
        timesa.add(tt);
        timesb.add(tt);

        ABLogPO p0=oneMean[0];
        ABLogPO p1=oneMean[1];
        ABLogPO p2=oneMean[2];
        ABLogPO p3=oneMean[3];
        ABLogPO p4=oneMean[4];
        ABLogPO p5=oneMean[5];
        ABLogPO p6=oneMean[6];
        ABLogPO p7=oneMean[7];
        ABLogPO p8=oneMean[8];
        ABLogPO p9=oneMean[9];
        BigDecimal p0a=castLogValue(p0,0);
        BigDecimal p0b=castLogValue(p0,1);
        p0adata.add(p0a);
        p0bdata.add(p0b);

        BigDecimal p1a=castLogValue(p1,0);
        BigDecimal p1b=castLogValue(p1,1);
        p1adata.add(p1a);
        p1bdata.add(p1b);

        BigDecimal p2a=castLogValue(p2,0);
        BigDecimal p2b=castLogValue(p2,1);
        p2adata.add(p2a);
        p2bdata.add(p2b);

        BigDecimal p3a=castLogValue(p3,0);
        BigDecimal p3b=castLogValue(p3,1);
        p3adata.add(p3a);
        p3bdata.add(p3b);

        BigDecimal p4a=castLogValue(p4,0);
        BigDecimal p4b=castLogValue(p4,1);
        p4adata.add(p4a);
        p4bdata.add(p4b);

        BigDecimal p5a=castLogValue(p5,0);
        BigDecimal p5b=castLogValue(p5,1);
        p5adata.add(p5a);
        p5bdata.add(p5b);

        BigDecimal p6a=castLogValue(p6,0);
        BigDecimal p6b=castLogValue(p6,1);
        p6adata.add(p6a);
        p6bdata.add(p6b);

        BigDecimal p7a=castLogValue(p7,0);
        BigDecimal p7b=castLogValue(p7,1);
        p7adata.add(p7a);
        p7bdata.add(p7b);

        BigDecimal p8a=castLogValue(p8,0);
        BigDecimal p8b=castLogValue(p8,1);
        p8adata.add(p8a);
        p8bdata.add(p8b);

        BigDecimal p9a=castLogValue(p9,0);
        BigDecimal p9b=castLogValue(p9,1);
        p9adata.add(p9a);
        p9bdata.add(p9b);
    }
    public BigDecimal castLogValue(ABLogPO temp,Integer index){
        return new BigDecimal(temp.getLogValue().split(",")[index]);
    }
}
