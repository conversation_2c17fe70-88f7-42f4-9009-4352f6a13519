package com.tdk.sae.collect.init.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.init.utils.FileMonitor;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBLogDTO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBLogPO;
import com.tdk.sae.collect.module.xjsbb.service.XJSBBDataService;
import com.tdk.sae.collect.module.xjsbb.service.XJSBBLogService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@DependsOn("systemInfoService")

public class MonitorXJSBBLog {

    private static final Logger logger = LoggerFactory.getLogger(MonitorXJSBBLog.class);

    public static final Map<String, FileAlterationMonitor> MONITORS = new ConcurrentHashMap<>();

    private final XJSBBLogService xjsbbLogService;

    private final XJSBBDataService xjsbbDataService;


    @PostConstruct
    public void init() {
        if (CollectionUtil.isEmpty(SystemInfoService.getEquipmentMap())) {
            return;
        }
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.XJSBB_LOG.getName());
        if (!CollectionUtil.isEmpty(icsEquips)) {
            xjsbbDataService.loadLookupMap();
            icsEquips.forEach(e -> {
                try {
                    start(e);
                } catch (Exception ex) {
                    logger.error("init MonitorXJSBBLog failed! error:{}----------{}", ex.getMessage(), e);
                }
            });
        }
    }

    public void start(EquipmentDTO equip) throws Exception {
        Long equipId = Long.parseLong(equip.getId());
        if (StrUtil.isEmpty(equip.getAddress())) {
            return;
        }
        String[] folderStrArr = equip.getAddress().split(",");
        Collection<File> logFiles = new ArrayList<>();
        List<File> folders = new ArrayList<>();
        for (String folderStr : folderStrArr) {
            File folder = new File(folderStr);
            if (ObjectUtil.isNull(folder) || !folder.exists()) {
                logger.error("Folder not exist! address: {}", folderStr);
                return;
            }
            folders.add(folder);
            // 获取所有txt后缀文件
            logFiles.addAll(FileUtils.listFiles(folder, new String[]{"txt"}, true));
        }

        // 按filePath group by取得所有已读取过记录的文件
        LambdaQueryWrapper<XJSBBLogPO> queryWrapper = Wrappers.lambdaQuery(XJSBBLogPO.class)
                .select(XJSBBLogPO::getFilePath)
                .eq(XJSBBLogPO::getEquipId, equipId)
                .groupBy(XJSBBLogPO::getFilePath);
        List<XJSBBLogPO> xjsbbLogs = xjsbbLogService.list(queryWrapper);

        XJSBBLogPO lastLog = null;
        if (!xjsbbLogs.isEmpty()) {
            lastLog = xjsbbLogService.getOne(Wrappers.lambdaQuery(XJSBBLogPO.class)
                    .eq(XJSBBLogPO::getEquipId, equipId)
                    .orderByDesc(XJSBBLogPO::getLogTime).last("limit 1"));
            final String excludeFile = lastLog.getFilePath();
            Set<String> ignoreFiles = xjsbbLogs.stream()
                    .map(XJSBBLogPO::getFilePath)
                    .filter(filePath -> !filePath.equals(excludeFile)).collect(Collectors.toSet());
            logFiles = logFiles.stream().filter(f -> !ignoreFiles.contains(f.getPath())).collect(Collectors.toSet());
        }
        List<XJSBBLogDTO> xjsbbLogDTOList = new ArrayList<>();

        XJSBBLogPO finalLastLog = lastLog;
        logFiles.forEach(f -> {
            // 数据库最新的记录与文件匹配到时, 只考虑大于数据库logTime的数据, 提前去重
            if (finalLastLog != null && f.getPath().equals(finalLastLog.getFilePath())) {
                xjsbbLogDTOList.addAll(xjsbbLogService.digestLogFile(equipId, f, finalLastLog.getLogTime()));
            } else {
                xjsbbLogDTOList.addAll(xjsbbLogService.digestLogFile(equipId, f));
            }
        });
        if (!xjsbbLogDTOList.isEmpty()) {
            xjsbbLogService.handleNewLogs(xjsbbLogDTOList);
        }

        // 监听频率为1s
        long interval = 1000;
        FileAlterationMonitor monitor = new FileAlterationMonitor(interval);
        for (File folder : folders) {
            // 初始化扫描完成, 开始监听新数据
            FileAlterationObserver observer = new FileAlterationObserver(folder);
            observer.addListener(new FileMonitor(equip, xjsbbLogService));
            monitor.addObserver(observer);
        }
        monitor.start();

        MONITORS.put(equip.getId(), monitor);
    }

}
