package com.tdk.sae.collect.module.xjsbb.model.dto;

import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class XJSBBLogDTO extends FileLogDTO {

    private Long equipId;

    private String filePath;

    private String rawData;

    private String varName;

    private String varValue;

    private Integer isDeleted = 0;

    private Integer synced = 0;

}
