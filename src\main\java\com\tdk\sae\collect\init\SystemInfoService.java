package com.tdk.sae.collect.init;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentPO;
import com.tdk.sae.collect.module.equipment.service.EquipmentService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Component
public class SystemInfoService {

    private static final Logger logger = LoggerFactory.getLogger(SystemInfoService.class);

    private final SystemProperties systemProperties;

    private final EquipmentService equipmentService;

    public static final InfoStore STORE = new InfoStore();

    @PostConstruct
    public void init() {
        HttpRequest request = HttpUtil.createGet(systemProperties.getServer().getEndpoint() + "/equip/client/info/" + systemProperties.getDatacenterId());
        try {
            HttpResponse response = request.execute();
            JSONObject result = JSONObject.parseObject(response.body());
            List<EquipmentDTO> equipList = JSONObject.parseArray(result.get("data").toString(), EquipmentDTO.class);
            if (!equipList.isEmpty()) {
                equipmentService.saveOrUpdateBatch(BeanUtil.copyToList(equipList, EquipmentPO.class));
            } else {
                List<EquipmentPO> poList = equipmentService.list();
                if (!poList.isEmpty()) {
                    equipList = BeanUtil.copyToList(poList, EquipmentDTO.class);
                }
            }
            STORE.setEquipmentMap(equipList);
        } catch (IORuntimeException ioRuntimeException) {
            logger.error("Can't not connect to HDS Server! /equip/client/info fetch failed! Use local data instead.");
            List<EquipmentPO> poList = equipmentService.list();
            if (!poList.isEmpty()) {
                STORE.setEquipmentMap(BeanUtil.copyToList(poList, EquipmentDTO.class));
            }
        }
    }

    public static Map<String, List<EquipmentDTO>> getEquipmentMap() {
        return STORE.getEquipmentMap();
    }

    private static class InfoStore {

        private Map<String, List<EquipmentDTO>> equipmentMap;

        private Map<String, List<EquipmentDTO>> getEquipmentMap() {
            return equipmentMap;
        }

        private void setEquipmentMap(List<EquipmentDTO> equipmentList) {
            if (CollectionUtil.isEmpty(equipmentList)) {
                return;
            }
            Map<String, List<EquipmentDTO>> map = new HashMap<>();
            equipmentList.forEach(equip -> {
                String key = "";
                switch (equip.getEquipType()) {
                    case EquipmentTypeEnum.Constants.IPC:
                        key = EquipmentTypeEnum.IPC.getName();
                        break;
                    case EquipmentTypeEnum.Constants.IVS_IMAGE:
                        key = EquipmentTypeEnum.IVS_IMAGE.getName();
                        break;
                    case EquipmentTypeEnum.Constants.IVS_LOG:
                        key = EquipmentTypeEnum.IVS_LOG.getName();
                        break;
                    case EquipmentTypeEnum.Constants.KEYENCE_KV8000:
                        key = EquipmentTypeEnum.KEYENCE_KV8000.getName();
                        break;
                    case EquipmentTypeEnum.Constants.ICS_LOG:
                        key = EquipmentTypeEnum.ICS_LOG.getName();
                        break;
                    case EquipmentTypeEnum.Constants.ICS_IMAGE:
                        key = EquipmentTypeEnum.ICS_IMAGE.getName();
                        break;
                    case EquipmentTypeEnum.Constants.OCR_ICS:
                        key = EquipmentTypeEnum.OCR_ICS.getName();
                        break;
                    case EquipmentTypeEnum.Constants.XJSBB_LOG:
                        key = EquipmentTypeEnum.XJSBB_LOG.getName();
                        break;
                    case EquipmentTypeEnum.Constants.AB_LOG:
                        key = EquipmentTypeEnum.AB_LOG.getName();
                        break;
                    case EquipmentTypeEnum.Constants.SCADA:
                        key = EquipmentTypeEnum.SCADA.getName();
                        break;
                    case EquipmentTypeEnum.Constants.ICS_TREND:
                        key = EquipmentTypeEnum.ICS_TREND.getName();
                        break;
                    case EquipmentTypeEnum.Constants.HCCM_RESULT:
                        key = EquipmentTypeEnum.HCCM_RESULT.getName();
                        break;
                    case EquipmentTypeEnum.Constants.HCCM_SLIDER:
                        key = EquipmentTypeEnum.HCCM_SLIDER.getName();
                        break;
                }
                List<EquipmentDTO> list = map.computeIfAbsent(key, k -> new ArrayList<>());
                list.add(equip);
            });
            equipmentMap = map;
        }
    }


}
