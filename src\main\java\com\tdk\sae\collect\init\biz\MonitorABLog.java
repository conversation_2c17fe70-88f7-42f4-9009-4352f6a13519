package com.tdk.sae.collect.init.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.init.utils.FileMonitor;
import com.tdk.sae.collect.module.abDim.model.dto.ABLogDTO;
import com.tdk.sae.collect.module.abDim.service.ABLogService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import com.tdk.sae.collect.module.abDim.model.po.*;

@RequiredArgsConstructor
@Component
@DependsOn("systemInfoService")
public class MonitorABLog {

    private static final Logger logger = LoggerFactory.getLogger(MonitorABLog.class);

    private final ABLogService icsLogService;

    public static final Map<String, FileAlterationMonitor> MONITORS = new ConcurrentHashMap<>();


    /**
     * 根据设备开始监听指定目录，扫描所有该目录下的txt文件，并保存符合ICS_LOG规则的新日志记录
     * @param equip 存在ICS_LOG的设备信息, 需要设备ID, 文件目录地址等必要信息
     */
    public void start(EquipmentDTO equip) throws Exception {
        Long equipId = Long.parseLong(equip.getId());
        if (StrUtil.isEmpty(equip.getAddress())) {
            return;
        }
        String[] folderStrArr = equip.getAddress().split(",");
        Collection<File> logFiles = new ArrayList<>();
        List<File> folders = new ArrayList<>();
        for (String folderStr : folderStrArr) {
            File folder = new File(folderStr);
            if (ObjectUtil.isNull(folder) || !folder.exists()) {
                logger.error("Folder not exist! address: {}", folderStr);
                return;
            }
            folders.add(folder);
            // 获取所有txt后缀文件
            logFiles.addAll(FileUtils.listFiles(folder, new String[]{"txt"}, true));
        }
        fileBefore30daysFile(logFiles);

        // 按filePath group by取得所有已读取过记录的文件
        LambdaQueryWrapper<ABLogPO> queryWrapper = Wrappers.lambdaQuery(ABLogPO.class)
                .select(ABLogPO::getFilePath)
                .eq(ABLogPO::getEquipId, equipId)
                .groupBy(ABLogPO::getFilePath);
        List<ABLogPO> icsLogs = icsLogService.list(queryWrapper);

        // 旧记录忽略，只检查数据库最近一条记录的文件内容是否有更新
        ABLogPO latestLog = null;
        if (icsLogs.size() > 0) {
            latestLog = icsLogService.getOne(Wrappers.lambdaQuery(ABLogPO.class)
                    .eq(ABLogPO::getEquipId, equipId)
                    .orderByDesc(ABLogPO::getLogTime).last("limit 1"));
            final String excludeFile = latestLog.getFilePath();
            Set<String> ignoreFiles = icsLogs.stream()
                    .map(ABLogPO::getFilePath)
                    .filter(filePath -> !filePath.equals(excludeFile)).collect(Collectors.toSet());
            logFiles = logFiles.stream().filter(f -> !ignoreFiles.contains(f.getPath())).collect(Collectors.toSet());
        }
        List<ABLogDTO> icsLogDTOList = new ArrayList<>();

        ABLogPO finalLatestLog = latestLog;
        logFiles.forEach(f -> {
            // 数据库最新的记录与文件匹配到时, 只考虑大于数据库logTime的数据, 提前去重
            if (finalLatestLog != null && f.getPath().equals(finalLatestLog.getFilePath())) {
                icsLogDTOList.addAll(icsLogService.digestLogFile(equipId, f, finalLatestLog.getLogTime()));
            } else {
                icsLogDTOList.addAll(icsLogService.digestLogFile(equipId, f));
            }
        });
        if (icsLogDTOList.size() > 0) {
            icsLogService.saveBatch(BeanUtil.copyToList(icsLogDTOList, ABLogPO.class), icsLogDTOList.size());
        }

        // 监听频率为1s
        long interval = 1000;
        FileAlterationMonitor monitor = new FileAlterationMonitor(interval);
        for (File folder : folders) {
            // 初始化扫描完成, 开始监听新数据
            FileAlterationObserver observer = new FileAlterationObserver(folder);
            observer.addListener(new FileMonitor(equip, icsLogService));
            monitor.addObserver(observer);
        }
        monitor.start();

        MONITORS.put(equip.getId(), monitor);
    }

    /**
     * ICS_LOG监听入口，按设备逐个启动监听
     */
    @PostConstruct
    public void init() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (!CollectionUtil.isEmpty(icsEquips)) {
            icsEquips.forEach(e -> {
                try {
                    start(e);
                } catch (Exception ex) {
                    logger.error("init MonitorICSLog failed! error:{}", ex.getMessage());
                }
            });
        }
    }

    private void fileBefore30daysFile(Collection<File> logFiles){
        LocalDateTime now=LocalDateTime.now();
        LocalDateTime lastMonth=now.minusMonths(1);
        List<String> strList=new ArrayList<>();
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyyMM\\dd");
        //获取从今天开始往前算一个月的时间列表
        for(LocalDateTime date=lastMonth;date.isBefore(now)||date.isEqual(now);date=date.plusDays(1)){
            String s=date.format(dateTimeFormatter);
            strList.add(s);
        }
        Iterator<File> iterator = logFiles.iterator();
        while (iterator.hasNext()) {
            File element = iterator.next();
            String path=element.getPath();
            //只读取最近一个月的文件数据
            boolean flag = strList.stream().anyMatch(s -> path.contains(s));
            if(!flag){
                iterator.remove();
            }
        }
    }

}
