package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.init.utils.FileMonitor;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.ics.mapper.ICSLogMapper;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
public class ICSLogService extends ServiceImpl<ICSLogMapper, ICSLogPO> implements ILogFileService {

    private static final String REGEX_HEADER = "Time,POS,Master,.*,Result";
    private static final String REGEX_LOG_ROW = "\\d{4}-\\d{1,2}-\\d{1,2} .*,.*\n|.*\r\n";
    private static final List<String> strList=new ArrayList<>();

    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        ICSLogPO lastLog = getOne(Wrappers.lambdaQuery(ICSLogPO.class)
                .eq(ICSLogPO::getEquipId, equipId)
                .eq(ICSLogPO::getFilePath, filePath)
                .orderByDesc(ICSLogPO::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, ICSLogDTO.class);
    }


    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        return saveBatch(BeanUtil.copyToList(newLogs, ICSLogPO.class), newLogs.size());
    }

    /**
     * read file to Log records
     * @param f file
     * @return Log records
     */
    public List<ICSLogDTO> digestLogFile(Long equipId, File f) {
        return digestLogFile(equipId, f, null);
    }

    /**
     * read file to Log records which logTime is greater(>) than parameter 'logTime'
     * @param f file
     * @param logTime logTime
     * @return Log records
     */
    public List<ICSLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
        boolean flag= FileMonitor.judgeFileDateIsInRange(f.getPath());
        if(!flag){
            Console.log("file:{},标志：{}", f.getPath(),flag);
            return new ArrayList<>();
        }
        String fileContent = FileUtil.readString(f, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        String[] headers = splitLogHeader(fileContent);
        if (headers == null) {
            return new ArrayList<>();
        }
        return doDigest(equipId, f.getPath(), fileContent, headers, logTime);
    }

    private String[] splitLogHeader(String fileContent) {
        Pattern pattern_t = Pattern.compile(REGEX_HEADER);
        Matcher matcher_t = pattern_t.matcher(fileContent);
        String[] headers = null;
        if(matcher_t.find()){
            String s = matcher_t.group();
            headers = s.split(",");
        }
        return headers;
    }

    /**
     * headers 从静态变为动态
     */
    private List<ICSLogDTO> doDigest(Long equipId, String filePath, String fileContent, String[] defaultHeaders, LocalDateTime logTime) {
        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);

        List<ICSLogDTO> logDTOList = new ArrayList<>();
        while (matcher.find()) {
            try {
                ICSLogDTO logDTO = new ICSLogDTO();
                logDTO.setFilePath(filePath);
                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
                logDTO.setRawData(rawData);
                logDTO.setEquipId(equipId);

                String[] columns = rawData.split(",");
                if (defaultHeaders == null) {
                    defaultHeaders = new String[]{};
                }
                if (columns.length != defaultHeaders.length) {
                    // 切换打胶模式后, 第一行header不变, 实际上数据会变, 此时需要适应对应数据的header
                    defaultHeaders = getAdaptiveHeaders(columns.length);
                    if (defaultHeaders == null) {
                        throw new RuntimeException("no matched log headers found! rawData:" + rawData);
                    }
                }
                // update description
                logDTO.setDescription(String.join(",", defaultHeaders));

                StringBuilder logVal = new StringBuilder();
                boolean isFilled = true;
                for (int i = 0; i < defaultHeaders.length; i++) {
                    String header = defaultHeaders[i];
                    String column = columns[i].trim();
                    if (header.equals(column)) {
                        isFilled = false;
                        break;
                    }
                    switch (header) {
                        case "Time":
                            column = StrUtil.padAfter(column, 23, "0");
                            logDTO.setLogTime(LocalDateTimeUtil.parse(column, "yyyy-MM-dd HH:mm:ss:SSS"));
                            break;
                        case "POS":
                            logDTO.setPos(column);
                            break;
                        case "Master":
                            logDTO.setMaster(column);
                            break;
                        case "Result":
                            logDTO.setLogValue(logVal.toString());
                            logDTO.setResult(column);
                            break;
                        default:
                            if (logVal.length() == 0) {
                                logVal.append(column);
                            } else {
                                logVal.append(",").append(column);
                            }
                            break;
                    }
                }
                if (logTime != null && logDTO.getLogTime().compareTo(logTime) <= 0) {
                    continue;
                }
                if (isFilled) {
                    logDTOList.add(logDTO);
                }
            } catch (Exception ignore) {}
        }
        return logDTOList;
    }

    private static final String oneDot = "Time,POS,Master,EX1,EY1,ED1,EDX1,EDY1,Result";
    private static final String twoDot = "Time,POS,Master,EX1,EX2,EY1,EY2,ED1,ED2,EDX1,EDY1,EDX2,EDY2,Result";
    private static final String threeDot = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,ED1,ED2,ED3,EDX1,EDY1,EDX2,EDY2,EDX3,EDY3,Result";

    private static final String oneDot2 = "Time,POS,Master,EX1,EY1,EW1,Result";
    private static final String twoDot2 = "Time,POS,Master,EX1,EX2,EY1,EY2,EW1,EW2,Result";
    private static final String threeDot2 = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,EW1,EW2,EW3,Result";

    private String[] getAdaptiveHeaders(int rawDataColumnLength) {
        if (rawDataColumnLength == 7) {
            return oneDot2.split(",");
        } else if (rawDataColumnLength == 10) {
            return twoDot2.split(",");
        } else if (rawDataColumnLength == 13) {
            return threeDot2.split(",");
        } else if (rawDataColumnLength == 9) {
            return oneDot.split(",");
        } else if (rawDataColumnLength == 14) {
            return twoDot.split(",");
        } else if (rawDataColumnLength == 19) {
            return threeDot.split(",");
        }
        return null;
    }
}
