package com.tdk.sae.collect.module.ics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ICSLogTrayMeanMapper extends BaseMapper<ICSLogTrayMeanPO> {

    List<ICSAdjustChartDTO> getChartDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> selectedDateTimes);

    List<ICSAdjustChartDTO> getChartDataLatest(@Param("equipId") String equipId, @Param("param") String parameter, @Param("lastTime") LocalDateTime lastTime);

    List<ICSAdjustChartDTO> getAdjustDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> selectedDateTimes);

    @Delete("DELETE FROM t_biz_ics_log_tray_mean WHERE start_time < #{time}")
    int deleteICSLogMeanData(@Param("time") String time);
}
