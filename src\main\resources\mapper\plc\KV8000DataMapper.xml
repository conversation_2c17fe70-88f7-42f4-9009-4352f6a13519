<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.plc.mapper.KV8000DataMapper">

    <select id="getLatestParamDataByEquipId" resultType="com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO">
        select t1.*, t3.address from t_biz_kv8000_data t1
        inner join
        (select param_id, max(read_time) as read_time
        from t_biz_kv8000_data
        where param_id in (select id from t_biz_kv8000_param where kv_id = #{equipId}) group by param_id) t2 on t1.param_id = t2.param_id and t1.read_time = t2.read_time
        left join t_biz_kv8000_param t3 on t1.param_id = t3.id
    </select>

    <select id="getLatestParamDateByEquipIdAndParam" resultType="com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO">
        select t1.*, t3.address, t3.address_represent from t_biz_kv8000_data t1
        inner join
        (select param_id, max(read_time) as read_time
        from t_biz_kv8000_data
        where param_id in (select id from t_biz_kv8000_param where equip_id = #{equipId}) group by param_id) t2 on t1.param_id = t2.param_id and t1.read_time = t2.read_time
        inner join t_biz_kv8000_param t3 on t1.param_id = t3.id and t3.address_represent in
        <foreach item="item" collection="params" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>

    <select id="beforeOne" resultType="com.tdk.sae.collect.module.plc.model.po.KV8000DataPO">
        select
            d.*
        from
            t_biz_kv8000_data d
        left join t_biz_kv8000_param p on d.param_id = p.id
        where
            p.equip_id = #{equipId}
            and p.description = #{param}
            and d.read_time &lt; #{time}
        order by d.read_time desc
        limit 1
    </select>

    <select id="getPlcDataInTimeRange" resultType="com.tdk.sae.collect.module.plc.model.po.KV8000DataPO">
        select
            d.*
        from t_biz_kv8000_data d
        left join t_biz_kv8000_param p on d.param_id = p.id
        where
            p.equip_id = #{equipId}
            and p.description = #{param}
            and
            <foreach item="item" collection="times" separator="or" open="(" close=")" index="index">
                d.read_time &gt;= #{item} and d.read_time &lt; DATE_ADD(#{item}, INTERVAL 1 DAY)
            </foreach>
    </select>

    <select id="getPlcDataLatest" resultType="com.tdk.sae.collect.module.plc.model.po.KV8000DataPO">
        select
            d.*
        from t_biz_kv8000_data d
        left join t_biz_kv8000_param p on d.param_id = p.id
        where
            p.equip_id = #{equipId}
            and p.description = #{param}
            and d.read_time &gt; #{lastTime}
    </select>

</mapper>
