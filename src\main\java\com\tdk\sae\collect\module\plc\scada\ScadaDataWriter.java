package com.tdk.sae.collect.module.plc.scada;


import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class ScadaDataWriter extends ScadaConnection {

    private final Logger logger = LoggerFactory.getLogger(ScadaDataWriter.class);

    private String address;

    private String dataType;

    private String scale;

    public ScadaDataWriter(String ip, Integer port) {
        super(ip, port);
    }

    public ScadaDataWriter setDataPoint(String address) {
        this.address = address;
        return this;
    }

    public ScadaDataWriter setDataType(String dataType) {
        this.dataType = dataType;
        return this;
    }

    public ScadaDataWriter setScale(String scale) {
        this.scale = scale;
        return this;
    }

    public boolean write(Object value) {
        // 浮点数需要按scale转成int写入
        if (dataType.equals(KVTypeFormat.Constants.REAL)) {
            BigDecimal wData = BigDecimal.valueOf((double) value);
            int wValue = wData.divide(new BigDecimal(scale), RoundingMode.HALF_UP).intValue();
            logger.warn("Writing value to Scada, address: " + address + ", value: " + wValue);
            dbComm.SetData(initial, address, wValue);
            return true;
        } else if (dataType.equals(KVTypeFormat.Constants.DINT)) {
            BigDecimal wData = BigDecimal.valueOf((long) value);
            int wValue = wData.intValue();
            logger.warn("Writing value to Scada, address: " + address + ", value: " + wValue);
            dbComm.SetData(initial, address, wValue);
            return true;
        }
        return false;
    }

}
