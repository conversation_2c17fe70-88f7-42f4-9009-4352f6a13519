package com.tdk.sae.collect.module.ics.service;

import lombok.Getter;

@Getter
public enum SpecialEquipmentEnum {

    TRAY_B28("4D-B28", Type.TRAY),
    THREE_GLUE_GPM2("4D-GPM2", Type.THREE_GLUE),
    THREE_GLUE_CCM6("4D-CCM6", Type.THREE_GLUE),
    THREE_GLUE_CCM6_2("4D-CCM6-2", Type.THREE_GLUE)

    ;
    private String name;
    private Type type;

    SpecialEquipmentEnum(String name, Type type) {
        this.name = name;
        this.type = type;
    }

    //特殊种类
    @Getter
    enum Type{
        //log顺序
        TRAY,
        //三点胶
        THREE_GLUE,
    }
}
