package com.tdk.sae.collect.module.abDim.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustChartDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ABLogTrayMeanMapper extends BaseMapper<ABLogTrayMeanPO> {

    List<ABAdjustChartDTO> getChartDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> selectedDateTimes);

    List<ABAdjustChartDTO> getChartDataLatest(@Param("equipId") String equipId, @Param("param") String parameter, @Param("lastTime") LocalDateTime lastTime);

    List<ABAdjustChartDTO> getAdjustDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> selectedDateTimes);
    @Delete("DELETE FROM t_biz_ab_log_tray_mean WHERE end_time < #{time} order by end_time asc limit 10000")
    int deleteData(@Param("time") String time);


}
