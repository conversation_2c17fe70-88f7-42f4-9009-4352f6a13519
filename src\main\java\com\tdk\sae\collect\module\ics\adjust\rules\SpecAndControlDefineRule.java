package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.lang.Console;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.adjust.ICSAdjustRulesPopulate;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Component("SpecAndControlDefineRule")
public class SpecAndControlDefineRule extends AbstractRulesHandler {

    public static final String REGEX = "范围线为（Target\\+/-([\\s\\S]*?)）um, 自动调整触发线为（Target\\+/-([\\s\\S]*?)）um";

    private final SpecInfo specInfo;

    @Override
    public void checkKV8000ParamCache() {}

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("SpecAndControlDefineRule");
        if (isDisabled()) {
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
            ICSAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
            detail.setApplyId(getRulePopulate().getApplyId());
            detail.setLogMsg("Auto adjust stopped. Cause: SpecAndControlDefineRule is disabled");
            detail.setLogLevel(0);
            return advice;
        }
        String rule = getRulePopulate().getRule();
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(rule);
        if (!matcher.find()) {
            // TODO: log detail
//            throw new IllegalArgumentException("SpecAndControlDefineRule didn't find spec/control limit!");
            return doNextRule(advice);
        }
        String specLimitStr = matcher.group(1);
        String controlLimitStr = matcher.group(2);
        BigDecimal specLimit = new BigDecimal(specLimitStr);
        BigDecimal controlLimit = new BigDecimal(controlLimitStr);
//        BigDecimal innerControlLimit=controlLimit.divide(new BigDecimal(2), 2, RoundingMode.HALF_UP);


        // 设定ED,EX,EY的自动调整spec & control触发上下限, 若上次有值, 直接覆盖
        Map<String, Map<String, BigDecimal>> tmpSpecMap = specInfo.getSpecInDbFirst(getEquipment().getId());;
        tmpSpecMap.forEach((param, value) -> {
            BigDecimal target = value.get("TARGET");
            value.put("ADJUST_UPPER_SPEC_LIMIT", target.add(specLimit));
            value.put("ADJUST_LOWER_SPEC_LIMIT", target.subtract(specLimit));
            value.put("ADJUST_UPPER_CONTROL_LIMIT", target.add(controlLimit));
            value.put("ADJUST_LOWER_CONTROL_LIMIT", target.subtract(controlLimit));
//            value.put("ADJUST_INNER_UPPER_CONTROL_LIMIT",target.add(innerControlLimit));
//            value.put("ADJUST_INNER_LOWER_CONTROL_LIMIT",target.subtract(innerControlLimit));
            value.put("ENABLE", new BigDecimal(1));
        });

        return doNextRule(advice);
    }

}
