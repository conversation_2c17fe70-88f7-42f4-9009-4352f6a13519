package com.tdk.sae.collect.module.hccm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.hccm.mapper.HCCMResultLogMapper;
import com.tdk.sae.collect.module.hccm.mapper.HCCMSliderLogMapper;
import com.tdk.sae.collect.module.hccm.module.HCCMTypeEnums;
import com.tdk.sae.collect.module.hccm.module.dto.HCCMResultLogDTO;
import com.tdk.sae.collect.module.hccm.module.dto.HCCMSliderLogDTO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMSliderPO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMSliderPO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMSliderPO;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
public class HCCMSliderLogService extends ServiceImpl<HCCMSliderLogMapper, HCCMSliderPO> implements ILogFileService {

    private static final String REGEX_HEADER = "PosID,OutLineAngle,.*,Time";
    private static final String REGEX_LOG_ROW = "\\d{1,2},.*\n|.*\r\n";
    public static String lastPos="-1";
    public static LocalDateTime lastSliderTime=null;
    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        HCCMSliderPO lastLog = getOne(Wrappers.lambdaQuery(HCCMSliderPO.class)
                .eq(HCCMSliderPO::getEquipId, equipId)
                .eq(HCCMSliderPO::getFilePath, filePath)
                .orderByDesc(HCCMSliderPO::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, ICSLogDTO.class);
    }


    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        if(lastSliderTime!=null){
            HCCMSliderPO firstSlider=BeanUtil.copyProperties(newLogs.get(0), HCCMSliderPO.class);
            long millisDiff = ChronoUnit.SECONDS.between(lastSliderTime,firstSlider.getLogTime());
            if(millisDiff<5 && lastPos.equals(firstSlider.getPos())){
                //5s内视为重复，去掉数据库最后一条
                HCCMSliderPO slider=this.lambdaQuery().orderByDesc(HCCMSliderPO::getLogTime).last("limit 1").one();
                this.getBaseMapper().deleteLastData(slider.getId());
            }
        }
        for(int i=0;i<newLogs.size();i++){
            HCCMSliderLogDTO hccmResultLogDTO= (HCCMSliderLogDTO) newLogs.get(i);
            try{
                HCCMSliderPO po=BeanUtil.copyProperties(hccmResultLogDTO, HCCMSliderPO.class);
                save(po);
                if(i==newLogs.size()-1){
                    HCCMSliderLogService.lastSliderTime=po.getLogTime();
                    HCCMSliderLogService.lastPos=po.getPos();
                }
            }catch (Exception e){
            }

        }
        return true;
    }

    /**
     * read file to Log records
     * @param f file
     * @return Log records
     */
    public List<HCCMSliderLogDTO> digestLogFile(Long equipId, File f) {
        return digestLogFile(equipId, f, null);
    }

    /**
     * read file to Log records which logTime is greater(>) than parameter 'logTime'
     * @param f file
     * @param logTime logTime
     * @return Log records
     */
    public List<HCCMSliderLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
        String fileContent = FileUtil.readString(f, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        String[] headers = splitLogHeader(fileContent);
        if (headers == null) {
            return new ArrayList<>();
        }
        return doDigest(equipId, f.getPath(), fileContent, headers, logTime);
    }

    private String[] splitLogHeader(String fileContent) {
        Pattern pattern_t = Pattern.compile(REGEX_HEADER);
        Matcher matcher_t = pattern_t.matcher(fileContent);
        String[] headers = null;
        if(matcher_t.find()){
            String s = matcher_t.group();
            headers = s.split(",");
        }
        return headers;
    }

    /**
     * headers 从静态变为动态
     */
    private List<HCCMSliderLogDTO> doDigest(Long equipId, String filePath, String fileContent, String[] defaultHeaders, LocalDateTime logTime) {
        //todo 获取文件名的日期
//        String regex = "\\\\(\\d{5,6})\\\\GlueSize\\\\(\\d{1,2})\\_day\\.txt";
//        Pattern pattern1 = Pattern.compile(regex);
//        Matcher matcher1 = pattern1.matcher(filePath);
//        String now = "";
//        if (matcher1.find()) {
//            String y=matcher1.group(1);
//            String d=matcher1.group(2);
//            if(y.length()<6){
//                //月份补0
//                y=y.substring(0,4)+"0"+y.substring(4);
//            }
//            if(d.length()<2){
//                //日期补0
//                d="0"+d;
//            }
//            date=y.substring(0,4)+"-"+y.substring(4,6)+"-"+d;
//        }
        String now=LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd");
        
        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);
        List<HCCMSliderLogDTO> logDTOList = new ArrayList<>();
        Integer lastPos=-1;
        LocalDateTime lastLogTime=null;
        while (matcher.find()) {
            try {
                HCCMSliderLogDTO logDTO = new HCCMSliderLogDTO();
                logDTO.setFilePath(filePath);
                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
                logDTO.setRawData(rawData);
                logDTO.setEquipId(equipId);

                String[] columns = rawData.split(",");
                if (defaultHeaders == null) {
                    defaultHeaders = new String[]{};
                }
                if (columns.length != defaultHeaders.length) {
                    // 切换打胶模式后, 第一行header不变, 实际上数据会变, 此时需要适应对应数据的header
                    defaultHeaders = getAdaptiveHeaders(columns.length);
                    if (defaultHeaders == null) {
                        throw new RuntimeException("no matched log headers found! rawData:" + rawData);
                    }
                }
                // update description
                logDTO.setDescription(String.join(",", defaultHeaders));

                StringBuilder logVal = new StringBuilder();
                boolean isFilled = true;
                for (int i = 0; i < defaultHeaders.length; i++) {
                    String header = defaultHeaders[i];
                    String column = columns[i].trim();
                    if (header.equals(column)) {
                        isFilled = false;
                        break;
                    }
                    switch (header) {
                        case "Time":
                               String[] a=column.split(":");
                               Integer hour=Integer.parseInt(a[0]);
                               Integer minute=Integer.parseInt(a[1]);
                               Integer seconds=Integer.parseInt(a[2]);
                               if(hour<10){
                                   a[0]="0"+a[0];
                               }
                                if(minute<10){
                                    a[1]="0"+a[1];
                                }
                                if(seconds<10){
                                    a[2]="0"+a[2];
                                }
                                column=now+" "+a[0]+":"+a[1]+":"+a[2];
                                logDTO.setLogTime(LocalDateTimeUtil.parse(column,"yyyy-MM-dd HH:mm:ss"));
                            break;
                        case "PosID":
                            logDTO.setPos(column);
                        default:
                            if (logVal.length() == 0) {
                                logVal.append(column);
                            } else {
                                logVal.append(",").append(column);
                            }
                            break;
                    }
                }
                if (logTime != null && logDTO.getLogTime().compareTo(logTime) < 0) {
                    continue;
                }
                if(lastPos!=null && lastPos.toString().equals(logDTO.getPos())){
                    long millisDiff = ChronoUnit.SECONDS.between(lastLogTime,logDTO.getLogTime());
                    if(millisDiff<5){
                        logDTOList.remove(logDTOList.size()-1);
                    }
                }
                if (isFilled) {
                    logDTOList.add(logDTO);
                    lastPos=Integer.parseInt(logDTO.getPos());
                    lastLogTime=logDTO.getLogTime();
                }
            } catch (Exception ignore) {}
        }
        return logDTOList;
    }

    private static final String oneDot = "Time,POS,Master,EX1,EY1,ED1,EDX1,EDY1,Result";
    private static final String twoDot = "Time,POS,Master,EX1,EX2,EY1,EY2,ED1,ED2,EDX1,EDY1,EDX2,EDY2,Result";
    private static final String threeDot = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,ED1,ED2,ED3,EDX1,EDY1,EDX2,EDY2,EDX3,EDY3,Result";

    private static final String oneDot2 = "Time,POS,Master,EX1,EY1,EW1,Result";
    private static final String twoDot2 = "Time,POS,Master,EX1,EX2,EY1,EY2,EW1,EW2,Result";
    private static final String threeDot2 = "Time,POS,Master,EX1,EX2,EX3,EY1,EY2,EY3,EW1,EW2,EW3,Result";

    private String[] getAdaptiveHeaders(int rawDataColumnLength) {
        if (rawDataColumnLength == 7) {
            return oneDot2.split(",");
        } else if (rawDataColumnLength == 10) {
            return twoDot2.split(",");
        } else if (rawDataColumnLength == 13) {
            return threeDot2.split(",");
        } else if (rawDataColumnLength == 9) {
            return oneDot.split(",");
        } else if (rawDataColumnLength == 14) {
            return twoDot.split(",");
        } else if (rawDataColumnLength == 19) {
            return threeDot.split(",");
        }
        return null;
    }

    public Map<String, Object> getHCCMSliderBFData(List<LocalDateTime> select){
        Map<String,Object> result=new HashMap<>();
//        LocalDateTime selected = LocalDateTimeUtil.parse(time, "yyyy-MM-dd");
//        LocalDateTime tommrow= selected.plusDays(1);
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.HCCM_SLIDER.getName());
        for(EquipmentDTO e:icsEquips) {
            List<HCCMSliderPO> HCCMSliderPOList=this.baseMapper.getHCCMSliderData(e.getId(),select);
//            List<HCCMSliderPO> HCCMSliderPOList = this.lambdaQuery()
//                    .ge(HCCMSliderPO::getLogTime,selected)
//                    .le(HCCMSliderPO::getLogTime,tommrow)
//                    .orderByAsc(HCCMSliderPO::getId).list();
            List<String> BFSDX = Lists.newArrayList();
            List<String> BFSDY = Lists.newArrayList();
            for (HCCMSliderPO hccmSliderPO : HCCMSliderPOList) {
                String[] rowData = hccmSliderPO.getRawData().split(",");
                BFSDX.add(rowData[4]);
                BFSDY.add(rowData[5]);
            }
            result.put("BFSDX",BFSDX);
            result.put("BFSDY",BFSDY);
        }

        return result;
    }

    public Map<String, Map<String,Object>> getHCCMSliderData(List<LocalDateTime> select, String label){
        Map<String, Map<String,Object>> result=new HashMap<>();
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.HCCM_SLIDER.getName());
//        LocalDateTime selected = LocalDateTimeUtil.parse(time, "yyyy-MM-dd");
//        LocalDateTime tommrow= selected.plusDays(1);
        initDistribute(result,"data");
        initMeanData(result,"mean_data");

        for(EquipmentDTO e:icsEquips){
            List<HCCMSliderPO> HCCMSliderPOList=null;
            HCCMSliderPOList=this.baseMapper.getHCCMSliderData(e.getId(),select);
//            HCCMSliderPOList = this.lambdaQuery()
//                    .eq(HCCMSliderPO::getEquipId,e.getId())
//                    .ge(HCCMSliderPO::getLogTime,selected)
//                    .le(HCCMSliderPO::getLogTime,tommrow)
//                    .orderByAsc(HCCMSliderPO::getId).list();
            int oneMeanSize = 0;
            HCCMSliderPO[] oneMean = new HCCMSliderPO[10];
            Queue<Integer> tray = new ArrayDeque<>(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
            for(HCCMSliderPO hccmResult:HCCMSliderPOList){
                int pos = Integer.parseInt(hccmResult.getPos().trim());
                if (Objects.equals(tray.poll(), pos)) {
                    oneMean[pos-1] = hccmResult;
                    oneMeanSize++;
                }else{
                    tray = new ArrayDeque<>(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
                    oneMean = new HCCMSliderPO[10];
                    oneMeanSize = 0;
                    if (pos == 1) {
                        tray.poll();
                        oneMean[pos-1] = hccmResult;
                        oneMeanSize++;
                    }
                }
                if (oneMeanSize == 10) {
                    LocalDateTime tempTime=oneMean[4].getLogTime();
                    String tt = LocalDateTimeUtil.format(tempTime, "HH:mm:ss");
                    fillData(result,"data",tt,oneMean,label);
                    fillMeanData(result,"mean_data",tt,oneMean,label);

                    tray = new ArrayDeque<>(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
                    oneMean = new HCCMSliderPO[10];
                    oneMeanSize = 0;
                }
            }

        }

        return result;
    }
    public void initMeanData(Map<String, Map<String,Object>> result,String key){
        Map<String,Object> temp=new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<BigDecimal> mean = new ArrayList<>();
        temp.put("xAxis",xAxisData);
        temp.put("mean",mean);
        result.put(key,temp);
    }
    public void fillMeanData(Map<String, Map<String,Object>> result, String Akey, String tt, HCCMSliderPO[] oneMean, String label){
        List<String> timesa = (List<String>) result.get(Akey).get("xAxis");
        List<BigDecimal> mean= (List<BigDecimal>) result.get(Akey).get("mean");
        timesa.add(tt);
        BigDecimal sum=BigDecimal.ZERO;
        for(HCCMSliderPO h:oneMean){
            BigDecimal value=castLogValue(h,label);
            sum=sum.add(value);
        }
        mean.add(sum.divide(new BigDecimal(10), 2, RoundingMode.HALF_UP));
    }
    public void initDistribute(Map<String, Map<String,Object>> result,String key){
        Map<String,Object> temp=new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<BigDecimal> p0 = new ArrayList<>();
        List<BigDecimal> p1 = new ArrayList<>();
        List<BigDecimal> p2 = new ArrayList<>();
        List<BigDecimal> p3 = new ArrayList<>();
        List<BigDecimal> p4 = new ArrayList<>();
        List<BigDecimal> p5 = new ArrayList<>();
        List<BigDecimal> p6 = new ArrayList<>();
        List<BigDecimal> p7 = new ArrayList<>();
        List<BigDecimal> p8 = new ArrayList<>();
        List<BigDecimal> p9 = new ArrayList<>();
        temp.put("xAxis",xAxisData);
        temp.put("p0",p0);
        temp.put("p1",p1);
        temp.put("p2",p2);
        temp.put("p3",p3);
        temp.put("p4",p4);
        temp.put("p5",p5);
        temp.put("p6",p6);
        temp.put("p7",p7);
        temp.put("p8",p8);
        temp.put("p9",p9);
        result.put(key,temp);
    }


    public void fillData(Map<String, Map<String,Object>> result,String Akey,String tt,HCCMSliderPO[] oneMean,String label){
        List<String> timesa = (List<String>) result.get(Akey).get("xAxis");
        List<BigDecimal> p0adata= (List<BigDecimal>) result.get(Akey).get("p0");
        List<BigDecimal> p1adata= (List<BigDecimal>) result.get(Akey).get("p1");
        List<BigDecimal> p2adata= (List<BigDecimal>) result.get(Akey).get("p2");
        List<BigDecimal> p3adata= (List<BigDecimal>) result.get(Akey).get("p3");
        List<BigDecimal> p4adata= (List<BigDecimal>) result.get(Akey).get("p4");
        List<BigDecimal> p5adata= (List<BigDecimal>) result.get(Akey).get("p5");
        List<BigDecimal> p6adata= (List<BigDecimal>) result.get(Akey).get("p6");
        List<BigDecimal> p7adata= (List<BigDecimal>) result.get(Akey).get("p7");
        List<BigDecimal> p8adata= (List<BigDecimal>) result.get(Akey).get("p8");
        List<BigDecimal> p9adata= (List<BigDecimal>) result.get(Akey).get("p9");
        timesa.add(tt);

        HCCMSliderPO p0=oneMean[0];
        HCCMSliderPO p1=oneMean[1];
        HCCMSliderPO p2=oneMean[2];
        HCCMSliderPO p3=oneMean[3];
        HCCMSliderPO p4=oneMean[4];
        HCCMSliderPO p5=oneMean[5];
        HCCMSliderPO p6=oneMean[6];
        HCCMSliderPO p7=oneMean[7];
        HCCMSliderPO p8=oneMean[8];
        HCCMSliderPO p9=oneMean[9];
        BigDecimal p0a=castLogValue(p0,label);
        p0adata.add(p0a);

        BigDecimal p1a=castLogValue(p1,label);
        p1adata.add(p1a);

        BigDecimal p2a=castLogValue(p2,label);
        p2adata.add(p2a);

        BigDecimal p3a=castLogValue(p3,label);
        p3adata.add(p3a);

        BigDecimal p4a=castLogValue(p4,label);
        p4adata.add(p4a);

        BigDecimal p5a=castLogValue(p5,label);
        p5adata.add(p5a);

        BigDecimal p6a=castLogValue(p6,label);
        p6adata.add(p6a);

        BigDecimal p7a=castLogValue(p7,label);
        p7adata.add(p7a);

        BigDecimal p8a=castLogValue(p8,label);
        p8adata.add(p8a);

        BigDecimal p9a=castLogValue(p9,label);
        p9adata.add(p9a);
    }

    public BigDecimal castLogValue(HCCMSliderPO temp,String label){
        int index = 0;
        if (label.equals(HCCMTypeEnums.SUSP_ANGLE.getDisplayName())){
            index = 2;
        }
        if (label.equals(HCCMTypeEnums.AF_SDX.getDisplayName())){
            index = 4;
        }
        if (label.equals(HCCMTypeEnums.AF_SDY.getDisplayName())){
            index = 5;
        }
        return new BigDecimal(temp.getRawData().split(",")[index]).setScale(2,BigDecimal.ROUND_HALF_UP);
    }

}
