package com.tdk.sae.collect.pubsub.listener.adjust;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.abDim.service.ABLogTrayMeanService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.service.ICSLogTrayMeanService;
import com.tdk.sae.collect.module.common.service.impl.WebSocketAdjustService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000DataService;
import com.tdk.sae.collect.pubsub.event.adjust.AdjustEvent;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@DependsOn("systemInfoService")
public class AdjustEventListener implements ApplicationListener<AdjustEvent> {

    private final ICSLogTrayMeanService icsLogTrayMeanService;
    private final ABLogTrayMeanService abLogTrayMeanService;
    private final KV8000DataService kv8000DataService;
    public static final Map<String,Map<String, String>> lastTimeMap=new HashMap<>();
    //key为topic队列名，value为队列数量
    public static final Map<String,Integer> topicMap=new HashMap<>();
    private static final Logger logger = LoggerFactory.getLogger(AdjustEventListener.class);
    private final WebSocketAdjustService webSocketAdjustService;
    @Override
    public void onApplicationEvent(AdjustEvent event) {
        //获取数据库数据
        if (StrUtil.isEmpty(event.getEquid()) || StrUtil.isEmpty(event.getParameter())) {
            logger.info("parameter invalid");
            return;
        }

        String last=lastTimeMap.get(event.getEquid()).get(event.getParameter());
        //如果没有执行初始化逻辑，则websocket不发送消息到前端
        if(!StrUtil.isEmpty(last) && adjustTopicIsAlive("/topic/"+event.getEquid()+"/"+event.getParameter())){
            LocalDateTime lastTimeVal = LocalDateTime.parse(last, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
            String selected = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd");

            String[] selectedDates = selected.split(",");
            List<LocalDateTime> selectedDateTimes = Arrays.stream(selectedDates)
                    .map(d -> LocalDateTimeUtil.parse(d, "yyyy-MM-dd"))
                    .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();

            Map<String, Object> meanChartData=new HashMap<>();
            if(event.getParameter().contains("A") || event.getParameter().contains("B")){
                meanChartData = abLogTrayMeanService.buildMeanChart(event.getEquid(), event.getParameter(), lastTimeVal, selectedDateTimes);
            }else{
                meanChartData = icsLogTrayMeanService.buildMeanChart(event.getEquid(), event.getParameter(), lastTimeVal, selectedDateTimes);
            }
            result.put("xAxisData", meanChartData.get("xAxisData"));
            result.put("series0Data", meanChartData.get("meanData"));

            @SuppressWarnings("unchecked")
            Map<String, Object> plcChartData = kv8000DataService.buildPlcChart(event.getEquid(), event.getParameter(), lastTimeVal, (List<LocalDateTime>) meanChartData.get("xAxisTimes"));
            result.put("series1Data", plcChartData.get("plcData"));
            result.put("scatterData", meanChartData.get("adjustData"));

            List<String> xAxisData= (List<String>) meanChartData.get("xAxisData");
            if(xAxisData.size()>0){
                String last1=xAxisData.get(xAxisData.size()-1);
                AdjustEventListener.lastTimeMap.get(event.getEquid()).put(event.getParameter(),last1);
                webSocketAdjustService.sendMessage("/topic/"+event.getEquid()+"/"+event.getParameter(),result);
            }
        }

    }

    @PostConstruct
    public void init() {
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_LOG.getName());
        icsLogEquips.forEach(e->{
            Map<String, String> le=new HashMap<>();
            le.put("ED1",null);
            le.put("ED2",null);
            le.put("EX1",null);
            le.put("EX2",null);
            le.put("EY1",null);
            le.put("EY2",null);
            le.put("ED3",null);
            le.put("EX3",null);
            le.put("EY3",null);
            lastTimeMap.put(e.getId(),le);
        });
        List<EquipmentDTO> abLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (!CollectionUtil.isEmpty(abLogEquips)){
            abLogEquips.forEach(e->{
                Map<String, String> le=new HashMap<>();
                le.put("A1",null);
                le.put("B1",null);
                lastTimeMap.put(e.getId(),le);
            });
        }
    }
    public static boolean adjustTopicIsAlive(String topicName){
        Integer topicNum=topicMap.getOrDefault(topicName,0);
        if(topicNum>0){
            return true;
        }
        return false;
    }
}
