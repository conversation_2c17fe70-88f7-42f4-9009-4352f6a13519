package com.tdk.sae.collect.module.ics.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ics.mapper.ICSAdjustRulesApplyMapper;
import com.tdk.sae.collect.module.ics.model.adjust.ICSAdjustRulesPopulate;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class ICSAdjustRulesApplyService extends ServiceImpl<ICSAdjustRulesApplyMapper, ICSAdjustRulesApplyPO> {

    private final ICSAdjustRulesApplyMapper icsAdjustRulesApplyMapper;

    public List<ICSAdjustRulesPopulate> getRulesPopulate(Long equipId) {
        return icsAdjustRulesApplyMapper.getRulesPopulate(equipId);
    }

    public List<ICSAdjustRulesApplyPO> getRulesApplyByType(Long equipId, Integer ruleType) {
        return icsAdjustRulesApplyMapper.getRulesApplyByType(equipId, ruleType);
    }
}
