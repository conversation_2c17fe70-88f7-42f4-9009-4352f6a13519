package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentStatusPO;
import com.tdk.sae.collect.module.equipment.service.EquipmentStatusService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@Component
public class EquipStatusUploadService {

    private static final Logger logger = LoggerFactory.getLogger(EquipStatusUploadService.class);

    private final EquipmentStatusService equipmentStatusService;

    private final MQProducerService mqProducerService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/20 * * * * ?")
    private synchronized void uploadUnSyncEquipStatus() {
        if (!enableUpload) {
            return;
        }
        List<EquipmentStatusPO> status = equipmentStatusService.list(Wrappers.lambdaQuery(EquipmentStatusPO.class)
                .eq(EquipmentStatusPO::getSynced, 0)
                .orderByAsc(EquipmentStatusPO::getCheckTime).last("limit 1000"));
        List<EquipmentStatusPO> updateStatus = equipmentStatusService.getLatestStatus();
        if (!updateStatus.isEmpty()) {
            status.addAll(updateStatus);
        }
        if (!status.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("EQUIP_STATUS", status);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                status.forEach(l -> {
                    l.setSynced(1);
                    l.setUpdatedAt(LocalDateTime.now());
                });
                status.forEach(equipmentStatusService::saveOrUpdate);
            }
        }
    }
}
