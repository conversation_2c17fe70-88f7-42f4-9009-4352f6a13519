package com.tdk.sae.collect.module.ivs.module.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ivs_log")
public class IVSLogPo extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "file_path")
    private String filePath;

    @TableField(value = "raw_data")
    private String rawData;


    @TableField(value = "synced")
    private Integer synced;


    @TableField(value = "log_time")
    private LocalDateTime logTime;

    @TableField(value = "read_type")
    private Integer readType;


    @TableField(value = "result_type")
    private Integer resultType;
}
