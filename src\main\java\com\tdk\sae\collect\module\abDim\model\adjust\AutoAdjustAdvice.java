package com.tdk.sae.collect.module.abDim.model.adjust;

import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDTO;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class AutoAdjustAdvice {

    private ABAdjustLogDTO adjustLog;

    private LocalDateTime lastStartTime;

    private Map<String, List<AutoAdjustRuleAdvice>> ruleAdvices = new HashMap<>();

    public AutoAdjustAdvice initLog(Long logId) {
        adjustLog = new ABAdjustLogDTO();
        adjustLog.setId(logId);
        adjustLog.setStartTime(LocalDateTime.now());
        adjustLog.setLogDetails(new ArrayList<>());
        return this;
    }

    public AutoAdjustRuleAdvice newRuleAdvice(String ruleName) {
        ruleAdvices.computeIfAbsent(ruleName, k -> new ArrayList<>());

        AutoAdjustRuleAdvice ruleAdvice = new AutoAdjustRuleAdvice();
        ruleAdvices.get(ruleName).add(ruleAdvice);
        return ruleAdvice;
    }

    public static class AutoAdjustRuleAdvice {

        private EquipmentDTO equipment;
        private KV8000ParamDTO kv8000Param;
        private String addressRepresent;
        private String newValueStr;

        private BigDecimal latestReadData;
        private BigDecimal diffValue;
        private BigDecimal newValue;

        public EquipmentDTO getEquipment() {
            return equipment;
        }

        public void setEquipment(EquipmentDTO equipment) {
            this.equipment = equipment;
        }

        public KV8000ParamDTO getKv8000Param() {
            return kv8000Param;
        }

        public void setKv8000Param(KV8000ParamDTO kv8000Param) {
            this.kv8000Param = kv8000Param;
        }

        public String getAddressRepresent() {
            return addressRepresent;
        }

        public void setAddressRepresent(String addressRepresent) {
            this.addressRepresent = addressRepresent;
        }

        public String getNewValueStr() {
            return newValueStr;
        }

        public void setNewValueStr(String newValueStr) {
            this.newValueStr = newValueStr;
        }

        public BigDecimal getLatestReadData() {
            return latestReadData;
        }

        public void setLatestReadData(BigDecimal latestReadData) {
            this.latestReadData = latestReadData;
        }

        public BigDecimal getDiffValue() {
            return diffValue;
        }

        public void setDiffValue(BigDecimal diffValue) {
            this.diffValue = diffValue;
        }

        public BigDecimal getNewValue() {
            return newValue;
        }

        public void setNewValue(BigDecimal newValue) {
            this.newValue = newValue;
        }

    }

}
