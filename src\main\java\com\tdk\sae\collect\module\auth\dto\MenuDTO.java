package com.tdk.sae.collect.module.auth.dto;

import com.tdk.sae.collect.domain.dto.DomainDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MenuDTO extends DomainDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 是否外链
     */
    private Integer external;
    /**
     * iframe菜单
     */
    private Integer iframe;
    /**
     * cache
     */
    private Integer cache;
    /**
     * 是否可见
     */
    private Integer hidden;
    /**
     * 是否显示父类
     */
    private Integer showParent;

    /**
     * 是否18n;
     */
    private Integer i18n;
    /**
     * 名称
     */
    @NotBlank(message = "名称不为空")
    private String label;
    /**
     * 英语名称
     */
    @NotBlank(message = "英文名称不为空")
    private String name;

    /**
     * 父菜单id
     */
    @NotNull(message = "父id不为空")
    private Long parentId;

    /**
     * 路由地址
     */
    @NotBlank(message = "菜单地址不为空")
    private String path;

    /**
     * 组件地址
     */
    private String component;

    /**
     * 图标
     */
    private String icon;

    /**
     * 展示顺序
     */
    @Min(value = 0, message = "最小只能为0")
    @Max(value = 999, message = "最大为999")
    private Integer sort;
    /**
     * 权限标识符
     */
    private List<String> authority;

    /**
     * 是否外链
     *
     * @return 是否外链
     */
    public boolean isExternal() {
        return this.external == 1;
    }

    /**
     * 是否显示
     *
     * @return 是否显示
     */
    public boolean isShowLink() {
        return this.hidden == 0;
    }

    /**
     * 是否i18n
     *
     * @return i18n
     */
    public boolean isI18n() {
        return this.i18n == 1;
    }

    /**
     * 是否iframe
     *
     * @return iframe
     */
    public boolean isIframe() {
        return this.iframe == 1;
    }

    /**
     * 是否显示父级菜单
     *
     * @return 是否显示
     */
    public boolean isShowParent() {
        return this.showParent == 1;
    }

    /**
     * 是否缓存
     *
     * @return 是否缓存
     */
    public boolean isCache() {
        return this.cache == 1;
    }

    public MenuDTO addAuth(String auth) {
        return this.addAuths(Collections.singletonList(auth));
    }

    public MenuDTO addAuths(List<String> auths) {
        if (null == this.authority) {
            this.authority = new ArrayList<>();
        }
        this.authority.addAll(auths);
        return this;
    }
}
