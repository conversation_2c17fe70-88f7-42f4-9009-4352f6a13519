package com.tdk.sae.collect.module.common.service;

import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;

public interface ILogFileService {

    FileLogDTO getLastLog(Long equipId, String filePath);

    List<? extends FileLogDTO> digestLogFile(Long equipId, File file);

    List<? extends FileLogDTO> digestLogFile(Long equipId, File file, LocalDateTime latestLogTime);

    boolean handleNewLogs(List<? extends FileLogDTO> newLogs);

}
