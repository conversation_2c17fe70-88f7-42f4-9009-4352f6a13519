package com.tdk.sae.collect.module.ics.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class ICSAdjustLogDetailDTO implements Serializable {

    private Long id;

    private Long logId;

    private Long applyId;

    private Long paramId;

    private Long meanId;

    private String logMsg;

    private Integer logLevel;

    private String fromValue;

    private String toValue;

    private LocalDateTime writeTime;

    private Integer isDeleted = 0;

    private Integer synced = 0;

}
