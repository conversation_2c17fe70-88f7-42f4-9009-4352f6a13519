package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.init.utils.FileMonitor;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.hccm.module.po.HCCMResultPO;
import com.tdk.sae.collect.module.ics.mapper.ICSPidLogMapper;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSPidLogDto;
import com.tdk.sae.collect.module.ics.model.dto.ICSPidStateDto;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSPidLogPO;
import com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamFormulaPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.impl.*;
import com.tdk.sae.collect.pubsub.event.adjust.AdjustEvent;
import com.tdk.sae.collect.pubsub.event.adjust.PidAdjustEvent;
import lombok.RequiredArgsConstructor;
import org.nfunk.jep.JEP;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
@RequiredArgsConstructor
@Service
public class ICSPidLogService extends ServiceImpl<ICSPidLogMapper, ICSPidLogPO> {
    private final KV8000ParamFormulaService kv8000ParamFormulaService;
    private final KV8000ParamService kv8000ParamService;
    private final SpecInfo specInfo;
    private final KV8000DataService kv8000DataService;
    private final String[] KV_ADDRESS_REPRESENTS = new String[]{"W_EX1", "W_EX2",
            "W_EY1", "W_EY2",
            "W_GT1", "W_GT2",
            "R_EX1", "R_EX2",
            "R_EY1", "R_EY2",
            "R_GT1", "R_GT2",
            "W_EX3", "W_EY3", "W_GT3", "R_EX3", "R_EY3", "R_GT3"};
    private static final Map<String,Map<String, ICSPidStateDto>> PidState=new ConcurrentHashMap<>();

    private static final Logger logger = LoggerFactory.getLogger(ICSPidLogService.class);

    protected final Map<String, Map<String, KV8000ParamPO>> KV8000ParamCache = new ConcurrentHashMap<>(2);
    private final SystemProperties systemProperties;
    public final KV8000ReadService kv8000ReadService;
    public final ScadaReadService scadaReadService;
    private final ICSAdjustRulesApplyService icsAdjustRulesApplyService;
    private final ApplicationEventPublisher eventPublisher;

    @Resource(name = "${system.write-type}")
    private IWritePlcService writePlcService;
    @Transactional
    public void calculatePidLog(EquipmentDTO equip, List<ICSLogPO> icsLogPOList) {
        ICSAdjustRulesApplyPO rulesApplyPO=icsAdjustRulesApplyService.lambdaQuery()
                .eq(ICSAdjustRulesApplyPO::getEquipId,equip.getId())
                .eq(ICSAdjustRulesApplyPO::getRule,"ICS-PID启用/禁用自动调整").one();
        KV8000ParamCache.computeIfAbsent(equip.getId(),p->new HashMap<>());
        checkKV8000ParamCache(equip);
        List<ICSLogPO> normalLog=icsLogPOList.stream().
                filter(l->l.getMaster().equals("N")||l.getMaster().equals("0"))
                .collect(Collectors.toList());
        int logSize=normalLog.size();
        List<ICSPidLogPO> addLogs=new ArrayList<>();
        List<Map<String,Object>> adjustData=new ArrayList<>();
        for(int i=0;i<logSize;i++){
            ICSLogPO tmpLog=normalLog.get(i);
            Map<String,BigDecimal> logValue=new HashMap<>();
            String[] columns = tmpLog.getDescription().split(",");
            String[] values = tmpLog.getRawData().split(",");
            if(tmpLog.getMaster().equals("N") || tmpLog.getMaster().equals("0")){
                for (int i1 = 0; i1 < columns.length; i1++) {
                    switch (columns[i1]) {
                        case "EX1":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "EX2":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "EX3":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "EY1":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "EY2":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "EY3":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "ED1":
                        case "EW1":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "ED2":
                        case "EW2":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                        case "ED3":
                        case "EW3":
                            logValue.put(columns[i1],new BigDecimal(values[i1]));
                            break;
                    }
                }
            }
            for (Map.Entry<String, BigDecimal> entry : logValue.entrySet()) {
                boolean isAdjust=(i==logSize-1 && rulesApplyPO.getEnabled()==1); //只调节最后一个log
                String key=entry.getKey();
                key=key.replaceAll("W","D");
                if(entry.getValue().compareTo(BigDecimal.ZERO) != 0){
                    addPidLog(entry.getValue(),key,isAdjust,equip,addLogs,adjustData, tmpLog.getLogTime());
                }
            }
        }
        List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        //插入log和执行调节&&推送websocket
        if(addLogs.size()>0){
            this.saveBatch(addLogs);
            for(ICSPidLogPO po:addLogs){
                eventPublisher.publishEvent(new PidAdjustEvent(this,po.getParameter(),equip.getId()));
            }
        }
        for (Map<String,Object> adjustParams : adjustData) {
            writePlcService.write(kv8000s.get(0),
                    (KV8000ParamDTO) adjustParams.get("write_param"),
                    (String) adjustParams.get("value"));
        }

    }
    public void addPidLog(BigDecimal value,String parameter,boolean isAdjust,EquipmentDTO equipment,
                          List<ICSPidLogPO> addLogs,List<Map<String,Object>> writeValues,LocalDateTime logTIme) {
        Map<String, Map<String, BigDecimal>> tmpSpecMap = specInfo.getSpecInDbFirst(equipment.getId());
        Map<String, BigDecimal> parameterSpec = tmpSpecMap.get(parameter);
        BigDecimal targetLine = parameterSpec.get("TARGET");
        BigDecimal AdjustUSL = parameterSpec.get("ADJUST_UPPER_SPEC_LIMIT");
        BigDecimal AdjustLSL = parameterSpec.get("ADJUST_LOWER_SPEC_LIMIT");
        BigDecimal USL = AdjustUSL != null ? AdjustUSL : targetLine.add(new BigDecimal(15));
        BigDecimal LSL = AdjustLSL != null ? AdjustLSL : targetLine.subtract(new BigDecimal(15));
//        if (value.compareTo(USL) <= 0 && value.compareTo(LSL) >= 0) {
            //在红线范围内
            ICSPidLogPO pidLogPO=new ICSPidLogPO();
            pidLogPO.setEquipId(Long.valueOf(equipment.getId()));
            pidLogPO.setParameter(parameter);
            pidLogPO.setLogValue(value);
            pidLogPO.setLogTime(logTIme);
            addLogs.add(pidLogPO);
            String replaceParameter=parameter;
            if (parameter.contains("ED")){
                replaceParameter = parameter.replaceAll("ED", "GT");
            }
            //获取读写参数以及pid公式
            KV8000ParamPO readparam = KV8000ParamCache.get(equipment.getId()).get("R_"+replaceParameter);
            KV8000ParamPO writeParam = KV8000ParamCache.get(equipment.getId()).get("W_"+replaceParameter);
            KV8000ParamFormulaPO formulaPO = kv8000ParamFormulaService.lambdaQuery()
                    .eq(KV8000ParamFormulaPO::getParamId, writeParam.getId()).last("limit 1").one();
            List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
            String equipType=systemProperties.getType();
            KV8000DataPO kv8000Data=null;
            List<KV8000DataDTO> cacheList=new ArrayList<>();
            //data从缓存里拿
            if(equipType.contains("KV")){
                cacheList=kv8000ReadService.getDataCacheByKvId(kv8000s.get(0).getId());
            }else{
                cacheList=scadaReadService.getDataCacheByKvId(kv8000s.get(0).getId());
            }
            if(!CollectionUtil.isEmpty(cacheList)) {
                //去缓存里找data
                kv8000Data = cacheList.stream()
                        .filter(c -> c.getParamId().toString().equals(readparam.getId().toString()))
                        .findFirst()
                        .map(k -> BeanUtil.copyProperties(k, KV8000DataPO.class))
                        .orElse(null);
            }
            if(kv8000Data==null){
                //缓存找不到则去数据库找
                kv8000Data = kv8000DataService.lambdaQuery()
                        .eq(KV8000DataPO::getParamId, readparam.getId())
                        .orderByDesc(KV8000DataPO::getReadTime)
                        .last("limit 1").one();
            }
            if (kv8000Data != null){
                pidLogPO.setFromValue(kv8000Data.getReadData());
            }
            String formula = formulaPO.getFormula();
            //插入log到pid表
            if (readparam == null || StrUtil.isEmpty(formula) || kv8000Data == null) {
                return;
            }
            ICSPidStateDto pidStateDto=PidState.computeIfAbsent(equipment.getId(),p->new HashMap<>())
                    .computeIfAbsent(parameter,pp->new ICSPidStateDto());
            if(isAdjust){
                //判断是否5分钟以内的log
                LocalDateTime nowTIme=LocalDateTime.now();
                long millisDiff = ChronoUnit.SECONDS.between(logTIme, nowTIme);
                if(millisDiff<300){
                    if(pidStateDto.getFirstTime()!=null){
                        long millisDiffs = ChronoUnit.SECONDS.between(pidStateDto.getFirstTime(), nowTIme);
                        if(millisDiffs>300){
                           setPidStateEmpty(pidStateDto);
                           return;
                        }
                    }
                    int size=Math.min(pidStateDto.getElementSize()+1,5);
                    pidStateDto.setElementSize(size);
                    if(size==1){
                        pidStateDto.setFirstTime(logTIme);
                        pidStateDto.setValue(value.setScale(2,BigDecimal.ROUND_HALF_UP));
                        return;
                    }else if(size<5){
                        pidStateDto.setValue(pidStateDto.getValue().add(value).setScale(2,BigDecimal.ROUND_HALF_UP));
                        return;
                    }
                    if(size==5){
                        pidStateDto.setValue(pidStateDto.getValue().add(value).setScale(2,BigDecimal.ROUND_HALF_UP));
                        value=pidStateDto.getValue().divide(new BigDecimal(5),2,RoundingMode.HALF_UP);;
                        setPidStateEmpty(pidStateDto);
                    }
//                        }
                    BigDecimal target = tmpSpecMap.get(parameter).get("TARGET");
                    BigDecimal error = target.subtract(value);
                    BigDecimal latestData=new BigDecimal(kv8000Data.getReadData());
                    BigDecimal calcuteValue=calculatePid(error,equipment,parameter,formula);
                    pidLogPO.setToValue(latestData.add(calcuteValue).toPlainString());
                    pidLogPO.setWriteTime(LocalDateTime.now());
                    pidLogPO.setParamId(writeParam.getId());
                    pidLogPO.setLogMsg((StrUtil.format("{} Follow rule: {}. From: ({}) To: ({})",
                            equipment.getEquipCode(),
                            "Pid rule",
                            latestData.toPlainString(),
                            latestData.add(calcuteValue).toPlainString())));
                    Map<String,Object> tmpWriteInfo=new HashMap<>();
                    tmpWriteInfo.put("write_param", BeanUtil.copyProperties(writeParam, KV8000ParamDTO.class));
                    tmpWriteInfo.put("value",latestData.add(calcuteValue).toPlainString());
                    writeValues.add(tmpWriteInfo);
                }else{
                    setPidStateEmpty(pidStateDto);
                }
            }else{
                setPidStateEmpty(pidStateDto);
            }
//        }

    }
    public void setPidStateEmpty(ICSPidStateDto pidStateDto){
        pidStateDto.setElementSize(0);
        pidStateDto.setValue(BigDecimal.ZERO);
        pidStateDto.setFirstTime(null);
    }
    public void checkKV8000ParamCache(EquipmentDTO equip) {
        List<KV8000ParamPO> paramList = kv8000ParamService.getCacheByEquipId(equip.getId());
        for (String kvAddressRepresent : KV_ADDRESS_REPRESENTS) {
            if (KV8000ParamCache.get(equip.getId()).get(kvAddressRepresent) != null) {
                continue;
            }
            List<KV8000ParamPO> filteredList = paramList.stream().filter(p -> p.getAddressRepresent().equals(kvAddressRepresent)).collect(Collectors.toList());
            if (!filteredList.isEmpty()) {
                KV8000ParamCache.get(equip.getId()).put(kvAddressRepresent, filteredList.get(0));
            }
        }
    }
    public BigDecimal calculatePid(BigDecimal error,EquipmentDTO equipment,String parameter,String formula){
        ICSPidStateDto pidStateDto=PidState.computeIfAbsent(equipment.getId(),p->new HashMap<>())
                .computeIfAbsent(parameter,pp->new ICSPidStateDto());
        BigDecimal integral = pidStateDto.getIntegral();
        BigDecimal previousError = pidStateDto.getPreviousError();
        pidStateDto.setPreviousError(error);
        pidStateDto.setIntegral(integral.add(error));
        BigDecimal tmp = error.subtract(previousError);
        JEP jep = new JEP();
        jep.addVariable("p", error.doubleValue());
        jep.addVariable("i", pidStateDto.getIntegral().doubleValue());
        jep.addVariable("d", tmp.doubleValue());
        jep.parseExpression(formula);
        Double calculatedVal = null;
        //调节值
        calculatedVal = jep.getValue();
        return new BigDecimal(calculatedVal).setScale(3, RoundingMode.HALF_UP);
    }
    public List<ICSPidLogDto> getChartDataInTimeRange(String equipId, String parameter, List<LocalDateTime> selectedDateTimes) {
        return this.baseMapper.getChartDataInTimeRange(equipId, parameter, selectedDateTimes);
    }

    public List<ICSPidLogDto> getChartDataLatest(String equipId, String parameter, LocalDateTime lastTime) {
        return this.baseMapper.getChartDataLatest(equipId, parameter, lastTime);
    }
    public Map<String, Object> buildMeanChart(String equipId, String parameter, List<LocalDateTime> selectedDateTimes) {
        List<ICSPidLogDto> chartDTOList = getChartDataInTimeRange(equipId, parameter, selectedDateTimes);
        return doBuildMeanChart(equipId, parameter, chartDTOList);
    }
    // 为什么要重新查询调整参数：调整参数过程中可能会有延迟，导致前端如果请求了数据但是调整参数还未更新的话就会丢失掉这次调整的显示。所以现在改为每次都按选中的日期获取所有调整参数
    public Map<String, Object> buildMeanChart(String equipId, String parameter, LocalDateTime lastTime, List<LocalDateTime> selectedDateTimes) {
        List<ICSPidLogDto> chartDTOList = getChartDataLatest(equipId, parameter, lastTime);
        Map<String, Object> result = doBuildMeanChart(equipId, parameter, chartDTOList);
        // override adjust points
        List<Object[]> adjustData = new ArrayList<>();
        List<ICSPidLogDto> adjustChartDTOList = this.baseMapper.getAdjustDataInTimeRange(equipId, parameter, selectedDateTimes);
        if (adjustChartDTOList.size() > 0) {
            adjustChartDTOList.forEach(point -> {
                Object[] tmp = new Object[2];
                tmp[0] = LocalDateTimeUtil.format(point.getLogTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                tmp[1] = point.getToValue();
                adjustData.add(tmp);
            });
            result.put("adjustData", adjustData);
        }
        return result;
    }
    public Map<String, Object> doBuildMeanChart(String equipId, String parameter, List<ICSPidLogDto> chartDTOList) {
        Map<String, Object> result = new HashMap<>(6);
        List<String> xAxisData = new ArrayList<>();
        List<LocalDateTime> xAxisTimes = new ArrayList<>();
        List<BigDecimal> meanData = new ArrayList<>();
        List<Object[]> adjustData = new ArrayList<>();
        chartDTOList.forEach(point -> {
            xAxisTimes.add(point.getLogTime());
            String time = LocalDateTimeUtil.format(point.getLogTime(), "yyyy-MM-dd HH:mm:ss.SSS");
            xAxisData.add(time);
            meanData.add(point.getLogValue());
            if (StrUtil.isNotEmpty(point.getToValue())) {
                Object[] tmp = new Object[2];
                tmp[0] = time;
                tmp[1] = point.getToValue();
                adjustData.add(tmp);
            }
        });

        Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(equipId);
        BigDecimal mus = specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
        BigDecimal mls = specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
        BigDecimal muc = specMap.get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
        BigDecimal mlc = specMap.get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
//        BigDecimal mui = specMap.get(parameter).get("ADJUST_INNER_UPPER_CONTROL_LIMIT");
//        BigDecimal mli = specMap.get(parameter).get("ADJUST_INNER_LOWER_CONTROL_LIMIT");
        BigDecimal target = specMap.get(parameter).get("TARGET");

//        BigDecimal mus = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
//        BigDecimal mls = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
//        BigDecimal muc = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
//        BigDecimal mlc = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
//        BigDecimal target = SpecInfo.getSpec().get(parameter).get("TARGET");
        Map<String, String> spec = new HashMap<>();
        spec.put("mus", mus.toPlainString());
        spec.put("mls", mls.toPlainString());
        spec.put("target", target.toPlainString());
        spec.put("muc", muc.toPlainString());
        spec.put("mlc", mlc.toPlainString());
//        if (!StringUtils.isEmpty(mli)){
//            spec.put("mui", mui.toPlainString());
//            spec.put("mli", mli.toPlainString());
//        }
        result.put("specs", spec);

        result.put("xAxisTimes", xAxisTimes);
        result.put("xAxisData", xAxisData);
        result.put("meanData", meanData);
        result.put("adjustData", adjustData);
        return result;
    }

    public Map<String, Object> doBuildPidDataChart(String equipId, String parameter, List<LocalDateTime> selectedDateTimes){
        // 查询
        List<ICSPidLogDto> pidLogList = this.getBaseMapper().
                getAdjustDataInTimeRange(equipId, parameter, selectedDateTimes);

        return buildPidDataResult(equipId, parameter, pidLogList);
    }


    public Map<String, Object> doBuildPidDataChart(String equipId, String parameter, LocalDateTime lastTime, List<LocalDateTime> selectedDateTimes){
        // 查询
        List<ICSPidLogDto> pidLogList = this.getBaseMapper().
                getChartDataLatest(equipId, parameter, lastTime);

        return buildPidDataResult(equipId, parameter, pidLogList);
    }


    public Map<String, Object> buildPidDataResult(String equipId, String parameter, List<ICSPidLogDto> pidLogList){
        // 准备数据
        Map<String, Object> result = Maps.newHashMap();
        List<BigDecimal> series0Data = new ArrayList<>();
        List<String> series1Data = new ArrayList<>();
        List<String> xAxisData = new ArrayList<>();
        List<String> IsArtificiallyAdjust=new ArrayList<>();
        List<Object[]> adjustData = new ArrayList<>();
        ICSPidLogDto CurrentFirstPid=pidLogList.get(0);
        ICSPidLogPO lastPid=this.lambdaQuery()
                .eq(ICSPidLogPO::getEquipId,Long.parseLong(equipId))
                .eq(ICSPidLogPO::getParameter,parameter)
                .le(ICSPidLogPO::getLogTime,CurrentFirstPid.getLogTime())
                .orderByDesc(ICSPidLogPO::getLogTime)
                .last("limit 1").one();
        ICSPidLogDto lastPidLogDto=null;
        if(lastPid!=null){
            lastPidLogDto=BeanUtil.copyProperties(lastPid, ICSPidLogDto.class);
        }
        // 遍历数据
        for (ICSPidLogDto pidLog : pidLogList) {
            series0Data.add(pidLog.getLogValue());

            String value =  pidLog.getFromValue();
            series1Data.add(value);

            //转化时间格式
            xAxisData.add(LocalDateTimeUtil.format(pidLog.getLogTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
            if (StrUtil.isNotEmpty(pidLog.getToValue())) {
                Object[] tmp = new Object[2];
                tmp[0] = LocalDateTimeUtil.format(pidLog.getLogTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                tmp[1] = pidLog.getToValue();
                adjustData.add(tmp);
            }
            if(lastPidLogDto!=null){
                String toValue=lastPidLogDto.getToValue();
                String fromValue=lastPidLogDto.getFromValue();
                if(StringUtils.isNotEmpty(toValue)){
                    //如果上个点调节了，但是调节的值和当前点的FromValue不一致，则是人为调节
                    if(!value.equals(toValue)){
                        IsArtificiallyAdjust.add("1");
                    }else{
                        IsArtificiallyAdjust.add("0");
                    }
                }else{
                    //如果上个点没调，但是当前的fromValue和当前点的FromValue不一致，则是人为调节
                    if(!value.equals(fromValue)){
                        IsArtificiallyAdjust.add("1");
                    }else{
                        IsArtificiallyAdjust.add("0");
                    }
                }
            }else{
                IsArtificiallyAdjust.add("0");
            }
            lastPidLogDto=pidLog;
        }
        result.put("series0Data", series0Data);
        result.put("series1Data", series1Data);
        result.put("xAxisData", xAxisData);
        result.put("scatterData", adjustData);
        result.put("artificiallyAdjustData", IsArtificiallyAdjust);

        // specs线
        Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(equipId);
        Map<String, String> spec = new HashMap<>();
        spec.put("mus", specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT").toPlainString());
        spec.put("mls", specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT").toPlainString());
        spec.put("target", specMap.get(parameter).get("TARGET").toPlainString());
        spec.put("muc", specMap.get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT").toPlainString());
        spec.put("mlc", specMap.get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT").toPlainString());
        result.put("specs", spec);

        return result;
    }

}
