package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.adjust.utils.ReachFullDropHalfList;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogTrayMeanDTO;
import com.tdk.sae.collect.module.ics.service.ICSAutoAdjustService;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component("ExceedSpecThenStopRule")
public class ExceedSpecThenStopRule extends AbstractRulesHandler {

    private final KV8000ParamService kv8000ParamService;

    private final SpecInfo specInfo;

    private static final String KV_ADDRESS_REPRESENT_STOP = "W_STOP";

    @Override
    public void checkKV8000ParamCache() {
        KV8000ParamPO param = KV8000ParamCache.get(getEquipment().getId()).get(KV_ADDRESS_REPRESENT_STOP);
        if (param == null) {
            List<KV8000ParamPO> paramList = kv8000ParamService.getCacheByEquipId(getEquipment().getId());
            List<KV8000ParamPO> filteredList = paramList.stream().filter(p -> p.getAddressRepresent().equals(KV_ADDRESS_REPRESENT_STOP)).collect(Collectors.toList());
            if (filteredList.size() > 0) {
                KV8000ParamCache.get(getEquipment().getId()).put(KV_ADDRESS_REPRESENT_STOP, filteredList.get(0));
            }
        }
    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
        // 超出mean SPEC线的点，不做自动调整，报警并停机
        // 触发规则的点及之前的点都删除(没触发不删除, 否则影响其他规则判断)
//        Console.log("ExceedSpecThenStopRule");
        if (isDisabled()) {
            return doNextRule(advice);
        }
        Map<String, ReachFullDropHalfList<ICSLogTrayMeanDTO>> paramDataMap = ICSAutoAdjustService.DATA_CACHE.get(getEquipment().getId());
        paramDataMap.forEach((parameter, parameterDataList) -> {
            List<ICSLogTrayMeanDTO> meanList = parameterDataList.getList();

            Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(getEquipment().getId());
            BigDecimal specUpperLimit = specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
            BigDecimal specLowerLimit = specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
//            BigDecimal specUpperLimit = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
//            BigDecimal specLowerLimit = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
            int rmIdx = -1;
            boolean uTlF = false;
            for (int i = 0; i < meanList.size(); i++) {
                ICSLogTrayMeanDTO mean = meanList.get(i);
                if (mean.getMeanValue().compareTo(specUpperLimit) > 0) {
                    rmIdx = i;
                    uTlF = true;
                    break;
                }
                if (mean.getMeanValue().compareTo(specLowerLimit) < 0) {
                    rmIdx = i;
                    break;
                }
            }
            if (rmIdx >= 0) {
                if (advice.getRuleAdvices().get("ExceedSpecThenStopRule") == null) {
                    advice.newRuleAdvice("ExceedSpecThenStopRule");
                }
                ICSLogTrayMeanDTO overSpecMean = parameterDataList.getList().get(rmIdx);
                // 记录log, 并写入停机信号
                ICSAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
                detail.setApplyId(getRulePopulate().getApplyId());
                detail.setParamId(KV8000ParamCache.get(getEquipment().getId()).get(KV_ADDRESS_REPRESENT_STOP).getId());
                detail.setMeanId(overSpecMean.getId());
                detail.setLogLevel(2);
                detail.setLogMsg(StrUtil.format("{}'s {} exceed spec limit {}! mean spec: {}",
                        getEquipment().getEquipCode(),
                        parameter, uTlF ? specUpperLimit : specLowerLimit,
                        overSpecMean.getMeanValue()));

                parameterDataList.removeBeforeIdxIncluded(rmIdx);
            }
        });
        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> ruleAdvices = advice.getRuleAdvices().get("ExceedSpecThenStopRule");
        if (ruleAdvices != null) {
            advice.getAdjustLog().setAdjusted(1);
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());

            AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice = ruleAdvices.get(0);
            ruleAdvice.setEquipment(getEquipment());
            KV8000ParamPO po = KV8000ParamCache.get(getEquipment().getId()).get(KV_ADDRESS_REPRESENT_STOP);
            ruleAdvice.setKv8000Param(BeanUtil.copyProperties(po, KV8000ParamDTO.class));
            ruleAdvice.setAddressRepresent(ruleAdvice.getKv8000Param().getAddressRepresent());
            ruleAdvice.setNewValueStr("1");
            return advice;
        }
        return doNextRule(advice);
    }

}
