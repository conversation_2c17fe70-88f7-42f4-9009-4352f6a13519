package com.tdk.sae.collect.module.auth.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_user")
public class UserPO extends DomainPO {

    private static final long serialVersionUID = 1L;

    @TableField(value = "dept_id")
    private Long deptId;

    @TableField(value = "emp_no")
    private String empNo;

    @TableField(value = "password")
    private String password;

    @TableField(value = "eng_name")
    private String engName;

    @TableField(value = "zh_name")
    private String zhName;

    @TableField(value = "zh_name_simple")
    private String zhNameSimple;

    @TableField(value = "tel_type")
    private String telType;

    @TableField(value = "title")
    private String title;

    @TableField(value = "email")
    private String email;

    @TableField(value = "phone")
    private String phone;

    @TableField(value = "extension")
    private String extension;

    @TableField(value = "location")
    private String location;

    @TableField(value = "is_enabled")
    private Integer isEnabled;

}
