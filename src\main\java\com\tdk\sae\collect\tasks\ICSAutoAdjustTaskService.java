package com.tdk.sae.collect.tasks;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.service.ICSAutoAdjustService;
import com.tdk.sae.collect.module.ics.service.ICSLogService;
import com.tdk.sae.collect.module.ics.service.ICSLogTrayMeanService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ICSAutoAdjustTaskService {

    private static final Logger logger = LoggerFactory.getLogger(ICSAutoAdjustTaskService.class);

    private final ICSLogService icsLogService;
    private final ICSLogTrayMeanService icsLogTrayMeanService;
    private final ICSAutoAdjustService icsAutoAdjustService;
    private final SystemProperties systemProperties;

    /**
     * 每5秒执行一次ICS自动调整
     */
    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void startAutoAdjust() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_LOG.getName());
        if (CollectionUtil.isNotEmpty(icsEquips)) {
            icsEquips.forEach(icsAutoAdjustService::startAutoAdjusting);
        }
    }

    /**
     * 每5秒执行一次mean值计算
     */
    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void calculateICSLogMean() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_LOG.getName());
        if (CollectionUtil.isEmpty(icsEquips)) {
            return;
        }
        icsEquips.forEach(equip -> {
            ICSLogTrayMeanPO icsLogTrayMeanPO = icsLogTrayMeanService.lambdaQuery()
                    .eq(ICSLogTrayMeanPO::getEquipId, equip.getId())
                    .orderByDesc(ICSLogTrayMeanPO::getEndTime).last("limit 1").one();
            List<ICSLogPO> icsLogPOList;
            if (icsLogTrayMeanPO == null) {
                icsLogPOList = icsLogService.lambdaQuery()
                        .eq(ICSLogPO::getEquipId, equip.getId())
                        .orderByAsc(ICSLogPO::getLogTime).last("limit 1000").list();
            } else {
                icsLogPOList = icsLogService.lambdaQuery()
                        .eq(ICSLogPO::getEquipId, equip.getId())
                        .and(c -> c.gt(ICSLogPO::getLogTime, icsLogTrayMeanPO.getEndTime()))
                        .orderByAsc(ICSLogPO::getLogTime).last("limit 1000").list();
            }
            String code = systemProperties.getClientCode();
            if (code.equals("4D-CCM6") || code.equals("4D-GPM2") || code.equals("4D-CCM6-2")){
                icsLogTrayMeanService.calculateTrayMeanByOther(equip, icsLogPOList);
            }else {
                icsLogTrayMeanService.calculateTrayMean(equip, icsLogPOList);
            }

        });
    }

}
