spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    # 启动执行的sql脚本
    schema:
    # - classpath:db/sql/ics_conf_schema.sql
    - classpath:db/sql/ab_log_add_head.sql
    - classpath:db/sql/ivs_table.sql
    - classpath:db/sql/lncm_table.sql
    - classpath:db/sql/auto_add_table.sql
    # data: classpath:db/sql/ics_conf_sql.sql
    # 执行策略 always： 每次执行，never：不执行，embedded：内存数据库执行
    initialization-mode: always
    # 将$$作为结束符号，编写启动存储过程脚本
    separator: $$
