package com.tdk.sae.collect.module.auth.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tdk.sae.collect.domain.enums.EnabledEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class LoggedInUser extends UserDTO {
    /**
     * 用户 token
     */
    private String token;
    /**
     * 用户登录时间
     */
    private Date loginTime;
    /**
     * 用户过期时间
     */
    private Date expireTime;
    /**
     * 登录IP地址
     */
    private String ipaddr;
    /**
     * 浏览器类型
     */
    private String browser;
    /**
     * 操作系统
     */
    private String os;

    @JsonIgnore
    public String getUsername() {
        return super.getEmpNo();
    }

    @Override
    public String getPassword() {
        return super.getPassword();
    }

    @JsonIgnore
    public boolean isAccountNonExpired() {
        return isEnabled();
    }

    @JsonIgnore
    public boolean isAccountNonLocked() {
        return isEnabled();
    }

    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return isEnabled();
    }

    public boolean isEnabled() {
        return EnabledEnum.ENABLED.getValue().equals(getIsEnabled());
    }

    @JsonIgnore
    public boolean isAdmin() {
        return false;
    }

}
