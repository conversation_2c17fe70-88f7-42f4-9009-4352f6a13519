package com.tdk.sae.collect.module.ics.adjust.utils;

import java.util.List;

public class ReachFullDropHalfList<T> {

    private List<T> list;

    private final int capacity;

    public List<T> getList() {
        return list;
    }

    public ReachFullDropHalfList(int capacity, List<T> list) {
        this.capacity = capacity;
        this.list = list;
    }

    public int size() {
        return this.list.size();
    }

    public ReachFullDropHalfList<T> add(T data) {
        if (list.size() + 1 > capacity) {
            dumpHalf();
        }
        this.list.add(data);
        return this;
    }

    public ReachFullDropHalfList<T> append(List<T> list) {
        this.list.addAll(list);
        return this;
    }

    // index = 4 size = 5 lidx = 4
    public void removeBeforeIdxIncluded(int index) {
        if (index + 1 > list.size() - 1) {
            list.clear();
            return;
        }
        list = list.subList(index + 1, list.size());
    }

    private void remove(int size) {
        int head = list.size() - size;
        if (head < 0) {
            list.clear();
        } else {
            list = list.subList(head, list.size());
        }
    }

    private void dumpHalf() {
        remove(capacity / 2);
    }

    public static void main(String[] args) {

    }

}
