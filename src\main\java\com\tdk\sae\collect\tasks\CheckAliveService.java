package com.tdk.sae.collect.tasks;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ReUtil;
import com.tdk.sae.collect.config.SnowFlakeIdGenerator;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.init.biz.MonitorABLog;
import com.tdk.sae.collect.init.biz.MonitorICSLog;
import com.tdk.sae.collect.init.biz.MonitorIVSLog;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentStatusPO;
import com.tdk.sae.collect.module.equipment.service.EquipmentService;
import com.tdk.sae.collect.module.equipment.service.EquipmentStatusService;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import com.tdk.sae.collect.module.ics.service.ICSAdjustLogService;
import com.tdk.sae.collect.module.ics.service.ICSAdjustRulesApplyService;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;

@RequiredArgsConstructor
@Component
public class CheckAliveService {

    private static final Logger logger = LoggerFactory.getLogger(CheckAliveService.class);

    private final MonitorICSLog monitorICSLog;

    private final MonitorABLog monitorABLog;

    private final MonitorIVSLog monitorIVSLog;

    private final EquipmentStatusService equipmentStatusService;

    private final SystemProperties systemProperties;

    private static final ExecutorService Local_Pool = ThreadUtil.newExecutor(10);

    private static final String IP_REGEX = "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)";

    private final ICSAdjustRulesApplyService icsAdjustRulesApplyService;

    @Resource(name = "${system.write-type}")
    private IWritePlcService WriteService;
    private final KV8000ParamService kv8000ParamService;
    private final SnowFlakeIdGenerator snowFlakeIdGenerator;
    private final EquipmentService equipmentService;
    private final ICSAdjustLogService icsAdjustLogService;
    /**
     * 每分钟检查一次启动时加载失败的目录监控, 并尝试重新加载
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    private void checkICSLogMonitorAlive() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_LOG.getName());
        if (!CollectionUtil.isEmpty(icsEquips)) {
            icsEquips.forEach(e -> {
                try {
                    FileAlterationMonitor monitor = MonitorICSLog.MONITORS.get(e.getId());
                    if (monitor == null) {
                        monitorICSLog.start(e);
                    }
                    if (MonitorICSLog.MONITORS.get(e.getId()) == null) {
                        logger.error("Retry MonitorICSLog failed! retryAt:" + LocalDateTime.now());
                    }
                } catch (Exception ex) {
                    logger.error("Retry MonitorICSLog exception! error:" + ex.getMessage(), e);
                }
            });
        }
    }

    @Scheduled(cron = "0 0/3 * * * ?")
    private void checkEquipmentAlive() {
        Map<String, List<EquipmentDTO>> equipmentMap = SystemInfoService.getEquipmentMap();
        List<Future<EquipmentStatusPO>> checkList = new ArrayList<>();
        // 测试连接3秒超时
        int timeout = 3000;
        equipmentMap.forEach((equipName, equipList) -> equipList.forEach(equip -> {
            FutureTask<EquipmentStatusPO> task = new FutureTask<>(() -> {
                String address = equip.getAddress();
                if (equip.getEquipType().equals(EquipmentTypeEnum.Constants.IPC)) {
                    return new EquipmentStatusPO(Long.parseLong(equip.getId()),
                            1,
                            "connected",
                            LocalDateTime.now());
                }
                // 若address有','分隔,也只是提取第一个IP检查
                boolean isConnected = NetUtil.ping(ReUtil.get(IP_REGEX, address, 0), timeout);
                return new EquipmentStatusPO(Long.parseLong(equip.getId()),
                        isConnected ? 1 : 0,
                        isConnected ? "connected" : "connection lost",
                        LocalDateTime.now());
            });
            Local_Pool.submit(task);
            checkList.add(task);
        }));
        List<EquipmentStatusPO> checkResult = new ArrayList<>();
        checkList.forEach(task -> {
            try {
                checkResult.add(task.get());
            } catch (InterruptedException | ExecutionException ignore) {}
        });
        if (checkResult.isEmpty()){
            return;
        }
        List<EquipmentStatusPO> newStatus = new ArrayList<>();
        List<EquipmentStatusPO> updateStatus = new ArrayList<>();
        List<EquipmentStatusPO> oldStatus = equipmentStatusService.getLatestStatus();
        checkResult.forEach(newS -> {
            boolean hasMatch = false;
            for (EquipmentStatusPO oldS : oldStatus) {
                if (newS.getEquipId().equals(oldS.getEquipId())) {
                    hasMatch = true;
                    if (!newS.getEquipStatus().equals(oldS.getEquipStatus())) {
                        newStatus.add(newS);
                    } else {
                        oldS.setCheckTime(newS.getCheckTime());
                        updateStatus.add(oldS);
                    }
                }
            }
            if (!hasMatch) {
                newStatus.add(newS);
            }
        });
        if (!newStatus.isEmpty()) {
            equipmentStatusService.saveBatch(newStatus);
        }
        if (!updateStatus.isEmpty()) {
            equipmentStatusService.saveOrUpdateBatch(updateStatus);
        }
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.ICS_LOG.getName());
        if (CollectionUtil.isNotEmpty(icsLogEquips)) {
            newStatus.addAll(updateStatus);
            newStatus.forEach(equip->{
                String equid=equip.getEquipId().toString();
                Optional<EquipmentDTO> optional=icsLogEquips.stream().filter(i->i.getId().equals(equid)).findFirst();
                if(optional.isPresent()){
                    Integer status=equip.getEquipStatus();
                    if(status==0){
                        //断线
                        List<ICSAdjustRulesApplyPO> applies = icsAdjustRulesApplyService.lambdaQuery()
                                .eq(ICSAdjustRulesApplyPO::getEquipId, equip.getEquipId())
                                .in(ICSAdjustRulesApplyPO::getRule, "ICS启用/禁用自动调整", "ICS-PID启用/禁用自动调整")
                                .list();
                        boolean anyEnabled = applies.stream()
                                .filter(Objects::nonNull)  // 过滤掉可能的null实体
                                .map(ICSAdjustRulesApplyPO::getEnabled)
                                .anyMatch(enabled -> enabled == 1);
                        if(anyEnabled){
                            logger.info(optional.get().getClientCode()+"断联");
                            List<EquipmentDTO> kv8000 = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
                            KV8000ParamPO paramPO = kv8000ParamService.lambdaQuery()
                                    .eq(KV8000ParamPO::getEquipId, equip.getEquipId())
                                    .and(c -> c.eq(KV8000ParamPO::getAddressRepresent, "W_STOP"))
                                    .last("limit 1").one();
                            //WriteService.write(kv8000.get(0), BeanUtil.copyProperties(paramPO, KV8000ParamDTO.class), "1");
                            ICSAdjustLogDTO logDTO=new ICSAdjustLogDTO();
                            logDTO.setId(snowFlakeIdGenerator.nextId());
                            logDTO.setEquipId(equip.getEquipId());
                            logDTO.setStartTime(LocalDateTime.now());
                            logDTO.setEndTime(LocalDateTime.now());
                            logDTO.setAdjusted(0);
                            logDTO.setAdjustType(1);
                            logDTO.setLogDetails(new ArrayList<>());
                            ICSAdjustLogDetailDTO detail=logDTO.newDetail();
                            detail.setWriteTime(LocalDateTime.now());
                            String code=equipmentService.getById(equip.getEquipId()).getEquipCode();
                            detail.setLogMsg(code+"断开连接,触发报警");
                            detail.setParamId(paramPO.getId());
                            detail.setFromValue("0");
                            detail.setToValue("1");
                            icsAdjustLogService.saveLogs(logDTO);
                        }
                    }
                }
            });
        }
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    private void checkABLogMonitorAlive() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (!CollectionUtil.isEmpty(icsEquips)) {
            icsEquips.forEach(e -> {
                try {
                    FileAlterationMonitor monitor = MonitorABLog.MONITORS.get(e.getId());
                    if (monitor == null) {
                        monitorABLog.start(e);
                    }
                    if (MonitorABLog.MONITORS.get(e.getId()) == null) {
                        logger.error("Retry MonitorABLog failed! retryAt:" + LocalDateTime.now());
                    }
                } catch (Exception ex) {
                    logger.error("Retry MonitorABLog exception! error:" + ex.getMessage(), e);
                }
            });
        }
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    private void checkIVSLogMonitorAlive() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.IVS_LOG.getName());
        if (!CollectionUtil.isEmpty(icsEquips)) {
            icsEquips.forEach(e -> {
                try {
                    FileAlterationMonitor monitor = MonitorIVSLog.MONITORS.get(e.getId());
                    if (monitor == null) {
                        monitorIVSLog.start(e);
                    }
                    if (MonitorIVSLog.MONITORS.get(e.getId()) == null) {
                        logger.error("Retry MonitorIVSLog failed! retryAt:" + LocalDateTime.now());
                    }
                } catch (Exception ex) {
                    logger.error("Retry MonitorIVSLog exception! error:" + ex.getMessage(), e);
                }
            });
        }
    }

}
