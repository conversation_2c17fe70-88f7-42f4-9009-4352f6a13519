package com.tdk.sae.collect.tasks.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Hot Laser and Air Temperature Parameters
 */
@Getter
public enum LncmHotLaserEnum {
    // Hot Air Left Side
    TEMP_HOT_AIR1_L("Temp_HotAir1_L", GroupType.MARK_HOT_AIR_L),
    TEMP_HOT_AIR2_L("Temp_HotAir2_L", GroupType.MARK_HOT_AIR_L),
    TEMP_HOT_AIR3_L("Temp_HotAir3_L", GroupType.MARK_HOT_AIR_L),
    TEMP_HOT_AIR4_L("Temp_HotAir4_L", GroupType.MARK_HOT_AIR_L),
    TEMP_HOT_AIR5_L("Temp_HotAir5_L", GroupType.MARK_HOT_AIR_L),

    // Hot Air Right Side
    TEMP_HOT_AIR1_R("Temp_HotAir1_R", GroupType.MARK_HOT_AIR_R),
    TEMP_HOT_AIR2_R("Temp_HotAir2_R", GroupType.MARK_HOT_AIR_R),
    TEMP_HOT_AIR3_R("Temp_HotAir3_R", GroupType.MARK_HOT_AIR_R),
    TEMP_HOT_AIR4_R("Temp_HotAir4_R", GroupType.MARK_HOT_AIR_R),
    TEMP_HOT_AIR5_R("Temp_HotAir5_R", GroupType.MARK_HOT_AIR_R),

    // Laser Left Side
    TEMP_LASER1_L("Temp_Laser1_L", GroupType.MARK_LASER_L),
    TEMP_LASER2_L("Temp_Laser2_L", GroupType.MARK_LASER_L),
    TEMP_LASER3_L("Temp_Laser3_L", GroupType.MARK_LASER_L),
    TEMP_LASER4_L("Temp_Laser4_L", GroupType.MARK_LASER_L),
    TEMP_LASER5_L("Temp_Laser5_L", GroupType.MARK_LASER_L),
    TEMP_LASER6_L("Temp_Laser6_L", GroupType.MARK_LASER_L),
    TEMP_LASER7_L("Temp_Laser7_L", GroupType.MARK_LASER_L),
    TEMP_LASER8_L("Temp_Laser8_L", GroupType.MARK_LASER_L),
    TEMP_LASER9_L("Temp_Laser9_L", GroupType.MARK_LASER_L),
    TEMP_LASER10_L("Temp_Laser10_L", GroupType.MARK_LASER_L),

    // Laser Right Side
    TEMP_LASER1_R("Temp_Laser1_R", GroupType.MARK_LASER_R),
    TEMP_LASER2_R("Temp_Laser2_R", GroupType.MARK_LASER_R),
    TEMP_LASER3_R("Temp_Laser3_R", GroupType.MARK_LASER_R),
    TEMP_LASER4_R("Temp_Laser4_R", GroupType.MARK_LASER_R),
    TEMP_LASER5_R("Temp_Laser5_R", GroupType.MARK_LASER_R),
    TEMP_LASER6_R("Temp_Laser6_R", GroupType.MARK_LASER_R),
    TEMP_LASER7_R("Temp_Laser7_R", GroupType.MARK_LASER_R),
    TEMP_LASER8_R("Temp_Laser8_R", GroupType.MARK_LASER_R),
    TEMP_LASER9_R("Temp_Laser9_R", GroupType.MARK_LASER_R),
    TEMP_LASER10_R("Temp_Laser10_R", GroupType.MARK_LASER_R),
    ;

    private final String name;
    private final GroupType flag;
    

    LncmHotLaserEnum(String parameterName, GroupType groupFlag) {
        this.name = parameterName;
        this.flag = groupFlag;
    }

    /**
     * Group type enumeration
     */
    @Getter
    public enum GroupType {
        MARK_HOT_AIR_L("Mark_HotAir_L",Direction.LEFT),
        MARK_HOT_AIR_R("Mark_HotAir_R",Direction.RIGHT),
        MARK_LASER_L("Mark_Laser_L",Direction.LEFT),
        MARK_LASER_R("Mark_Laser_R",Direction.RIGHT);

        private final String value;
        private final Direction direction;
        
        GroupType(String value, Direction direction) {
            this.value = value;
            this.direction = direction;
        }
    }

    @Getter
    public enum Direction {
        LEFT("-L-"),
        RIGHT("-R-");

        private final String value;

        Direction(String value) {
            this.value = value;
        }
    }

    public static List<LncmHotLaserEnum> getEnumsByFlag(GroupType flag) {
        return Arrays.stream(values())
                .filter(param -> param.getFlag() == flag)
                .collect(Collectors.toList());
    }

    
}