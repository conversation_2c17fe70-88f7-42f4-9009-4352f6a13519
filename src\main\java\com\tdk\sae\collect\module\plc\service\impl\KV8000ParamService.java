package com.tdk.sae.collect.module.plc.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.mapper.KV8000ParamMapper;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
public class KV8000ParamService extends ServiceImpl<KV8000ParamMapper, KV8000ParamPO> {

    private static final Logger logger = LoggerFactory.getLogger(KV8000ParamService.class);

    private final SystemProperties systemProperties;

    // 参数缓存
    private final TimedCache<String, List<KV8000ParamPO>> kv8000ParamCache = CacheUtil.newTimedCache(30000);

    /**
     * 尝试同步服务器参数
     * @param equip equipment
     */
    @Transactional
    public void trySyncParams(EquipmentDTO equip) {
        String code = systemProperties.getClientCode();
        if (code.equals("4D-CCM6") || code.equals("4D-CCM6-2")){
            return;
        }
        HttpRequest request = HttpUtil.createGet(systemProperties.getServer().getEndpoint() + "/equip/client/info/kv8000/params/" + equip.getId());
        try {
            HttpResponse response = request.execute();
            JSONObject result = JSONObject.parseObject(response.body());
            List<KV8000ParamDTO> paramList = JSONObject.parseArray(result.get("data").toString(), KV8000ParamDTO.class);
            if (!paramList.isEmpty()) {
                try {
                    this.saveOrUpdateBatch(BeanUtil.copyToList(paramList, KV8000ParamPO.class));
                    kv8000ParamCache.remove(equip.getId());
                } catch (Exception e) {
                    logger.error("SyncKV8000 error! msg:{}", e.getMessage());
                }
            }
        } catch (IORuntimeException ioRuntimeException) {
            logger.error("Can't not connect to HDS Server! /equip/client/info/kv8000/params fetch failed! Use local data instead.");
        }
    }

    public List<KV8000ParamPO> getCacheByEquipId(String equipId) {
        List<KV8000ParamPO> params = kv8000ParamCache.get(equipId);
        if (CollectionUtil.isEmpty(params)) {
            params = this.lambdaQuery().eq(KV8000ParamPO::getEquipId, equipId).list();
            kv8000ParamCache.put(equipId, params);
        }
        return params;
    }

    public List<KV8000ParamPO> getCacheByKvId(String kvId) {
        List<KV8000ParamPO> params = kv8000ParamCache.get(kvId);
        if (CollectionUtil.isEmpty(params)) {
            params = this.lambdaQuery().eq(KV8000ParamPO::getKvId, kvId).list();
            kv8000ParamCache.put(kvId, params);
        }
        return params;
    }

}
