package com.tdk.sae.collect.module.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import com.tdk.sae.collect.exceptions.LoginException;
import com.tdk.sae.collect.module.auth.cache.LoginParamsCache;
import com.tdk.sae.collect.module.auth.mapper.UserMapper;
import com.tdk.sae.collect.module.auth.param.LoginParams;
import com.tdk.sae.collect.module.auth.po.UserPO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class UserAccountService extends ServiceImpl<UserMapper, UserPO> {

    private final LoginParamsCache loginParamsCache;

    private final PasswordEncoder passwordEncoder;

    public UserPO getUserByUserId(Long userId) {
        return super.getById(userId);
    }

    public UserPO getUserByEmpNo(String empNo) {
        LambdaQueryWrapper<UserPO> queryWrapper = Wrappers.lambdaQuery(UserPO.class)
                .eq(UserPO::getEmpNo, empNo).last("limit 1");
        UserPO userPO = super.getOne(queryWrapper);
        LoginParams loginParams = loginParamsCache.get(empNo);
        if (null == userPO) {
            throw new LoginException(ResponseCodeEnum.USE_LOGIN_ERROR, "User not found!");
        } else if (loginParams != null) {
            if (!passwordEncoder.matches(loginParams.getPassword(), userPO.getPassword())) {
                throw new LoginException(ResponseCodeEnum.USE_LOGIN_ERROR, "Password incorrect!");
            }
        }
        return userPO;
    }
}
