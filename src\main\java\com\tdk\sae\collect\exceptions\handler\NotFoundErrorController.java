package com.tdk.sae.collect.exceptions.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import com.tdk.sae.collect.domain.result.Response;
import org.springframework.boot.autoconfigure.web.servlet.error.AbstractErrorController;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Controller
public class NotFoundErrorController extends AbstractErrorController {

    public NotFoundErrorController() {
        super(new DefaultErrorAttributes());
    }

    @RequestMapping("${server.error.path:${error.path:/error}}")
    public void error(final HttpServletRequest request, final HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.OK.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        Map<String, Object> body = getErrorAttributes(request, ErrorAttributeOptions.defaults());
        Integer status = (Integer) body.get("status");
        body.remove("timestamp");
        body.remove("status");
        body.remove("error");

        Response<Map<String, Object>> result;
        switch (status) {
            case 403:
                result = Response.error(ResponseCodeEnum.UNAUTHORIZED,
                        ResponseCodeEnum.UNAUTHORIZED.getMessage(),
                        body);
                break;
            case 404:
                result = Response.error(ResponseCodeEnum.NOT_FOUND,
                        ResponseCodeEnum.NOT_FOUND.getMessage(),
                        body);
                break;
            default:
                result = Response.error(ResponseCodeEnum.FAIL,
                        ResponseCodeEnum.FAIL.getMessage(),
                        body);
        }
        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getWriter(), result);
    }

}