CREATE DATABASE hdslocal CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE hdslocal;

CREATE  TABLE t_biz_equip (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_name           VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	equip_code           VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	group_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	client_code          VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	address              VARCHAR(256)  NOT NULL DEFAULT ('')   ,
	equip_type           INT  NOT NULL DEFAULT ('0')    ,
	file_save_day        INT  NOT NULL DEFAULT ('0')    ,
	file_user_domain     VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	file_user_account    VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	file_user_pwd        VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	file_updated_time    TIMESTAMP       ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_equip_gid ON t_biz_equip ( group_id );

CREATE INDEX idx_t_biz_equip_cd ON t_biz_equip ( client_code );

CREATE  TABLE t_biz_equip_status (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	equip_status         TINYINT  NOT NULL DEFAULT ('0')    ,
	equip_msg            VARCHAR(500)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	check_time           TIMESTAMP(3)       ,
	synced               TINYINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_equip_status_eid ON t_biz_equip_status ( equip_id );

CREATE INDEX idx_t_biz_equip_status_ct ON t_biz_equip_status ( check_time );

CREATE INDEX idx_t_biz_equip_status_sync ON t_biz_equip_status ( synced );

CREATE  TABLE t_biz_ics_adjust_log (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	start_time           TIMESTAMP(3)  NOT NULL     ,
	end_time             TIMESTAMP(3)  NOT NULL     ,
	adjusted             TINYINT  NOT NULL DEFAULT ('0')    ,
	adjust_type          TINYINT  NOT NULL DEFAULT ('0')    ,
	adjust_by            BIGINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	synced               TINYINT  NOT NULL DEFAULT ('0')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_adjust_log_eid ON t_biz_ics_adjust_log ( equip_id );

CREATE INDEX idx_t_biz_ics_adjust_log_et ON t_biz_ics_adjust_log ( end_time );

CREATE INDEX idx_t_biz_ics_adjust_log_adjf ON t_biz_ics_adjust_log ( adjusted );

CREATE INDEX idx_t_biz_ics_adjust_log_sync ON t_biz_ics_adjust_log ( synced );

CREATE  TABLE t_biz_ics_adjust_log_detail (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	log_id               BIGINT  NOT NULL DEFAULT ('0')    ,
	apply_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	param_id             BIGINT       ,
	mean_id              BIGINT       ,
	log_msg              VARCHAR(200)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	log_level            TINYINT  NOT NULL DEFAULT ('0')    ,
	from_value           VARCHAR(100)    COLLATE utf8mb4_general_ci   ,
	to_value             VARCHAR(100)    COLLATE utf8mb4_general_ci   ,
	write_time           TIMESTAMP(3)       ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	synced               TINYINT  NOT NULL DEFAULT ('0')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_adjust_log_detail_lid ON t_biz_ics_adjust_log_detail ( log_id );

CREATE INDEX idx_t_biz_ics_adjust_log_detail_aid ON t_biz_ics_adjust_log_detail ( apply_id );

CREATE INDEX idx_t_biz_ics_adjust_log_detail_wt ON t_biz_ics_adjust_log_detail ( write_time );

CREATE INDEX idx_t_biz_ics_adjust_log_detail_mid ON t_biz_ics_adjust_log_detail ( mean_id );

CREATE INDEX idx_t_biz_ics_adjust_log_detail_sync ON t_biz_ics_adjust_log_detail ( synced );

CREATE INDEX idx_tbiald_to_value ON t_biz_ics_adjust_log_detail ( to_value );

CREATE  TABLE t_biz_ics_adjust_rules (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	rule                 VARCHAR(1000)  NOT NULL DEFAULT ('')   ,
	rule_type            TINYINT  NOT NULL     ,
	rule_weight          TINYINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE  TABLE t_biz_ics_adjust_rules_apply (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	rule_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	rule                 VARCHAR(1000)  NOT NULL DEFAULT ('')   ,
	enabled              TINYINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_adjust_rules_apply_eid ON t_biz_ics_adjust_rules_apply ( equip_id );

CREATE INDEX idx_t_biz_ics_adjust_rules_apply_rid ON t_biz_ics_adjust_rules_apply ( rule_id );

CREATE  TABLE t_biz_ics_log (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	file_path            VARCHAR(256)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	raw_data             VARCHAR(512)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	pos                  VARCHAR(20)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	master               VARCHAR(20)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	result               VARCHAR(20)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	log_value            VARCHAR(512)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	synced               TINYINT  NOT NULL DEFAULT ('0')    ,
	log_time             TIMESTAMP(3)       ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	CONSTRAINT idx_t_biz_ics_log_uni UNIQUE ( file_path, raw_data )
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_log_eid ON t_biz_ics_log ( equip_id );

CREATE INDEX idx_t_biz_ics_log_ltime ON t_biz_ics_log ( log_time );

CREATE INDEX idx_t_biz_ics_log_sync ON t_biz_ics_log ( synced );

CREATE  TABLE t_biz_ics_log_tray_mean (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	parameter            VARCHAR(20)  NOT NULL DEFAULT ('')   ,
	mean_value           DECIMAL(10,2)  NOT NULL     ,
	start_time           TIMESTAMP(3)  NOT NULL     ,
	end_time             TIMESTAMP(3)  NOT NULL     ,
	calculated_time      TIMESTAMP(3)  NOT NULL     ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	synced               TINYINT  NOT NULL DEFAULT ('0')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_log_mean_eid ON t_biz_ics_log_tray_mean ( equip_id );

CREATE INDEX idx_t_biz_ics_log_mean_et ON t_biz_ics_log_tray_mean ( end_time );

CREATE INDEX idx_t_biz_ics_log_mean_ct ON t_biz_ics_log_tray_mean ( calculated_time );

CREATE INDEX idx_t_biz_ics_log_mean_sync ON t_biz_ics_log_tray_mean ( synced );

CREATE INDEX idx_t_biz_ics_log_mean_param ON t_biz_ics_log_tray_mean ( parameter );

CREATE  TABLE t_biz_kv8000_data (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	param_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	command              VARCHAR(200)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	raw_data             VARCHAR(512)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	read_data            VARCHAR(512)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	read_time            TIMESTAMP(3)       ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	synced               TINYINT  NOT NULL DEFAULT ('0')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_kv8000_data_pid ON t_biz_kv8000_data ( param_id );

CREATE INDEX idx_t_biz_kv8000_data_rt ON t_biz_kv8000_data ( read_time );

CREATE INDEX idx_t_biz_ics_log_sync ON t_biz_kv8000_data ( synced );

CREATE  TABLE t_biz_kv8000_param (
	id                   BIGINT  NOT NULL   AUTO_INCREMENT  PRIMARY KEY,
	kv_id                BIGINT  NOT NULL DEFAULT ('0')    ,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	variable             VARCHAR(200)  NOT NULL DEFAULT ('')   ,
	data_type            VARCHAR(50)  NOT NULL DEFAULT ('')   ,
	data_length          INT       ,
	address              VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	address_represent    VARCHAR(50)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	variable_name        VARCHAR(200)  NOT NULL DEFAULT ('')   ,
	comm_cycle           VARCHAR(50)  NOT NULL DEFAULT ('')   ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('')
 ) ENGINE=InnoDB AUTO_INCREMENT=207 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_kv8000_param_eid ON t_biz_kv8000_param ( equip_id );

CREATE INDEX idx_t_biz_kv8000_param_var ON t_biz_kv8000_param ( variable );

CREATE INDEX idx_t_biz_kv8000_param_kid ON t_biz_kv8000_param ( kv_id );

CREATE  TABLE t_biz_kv8000_param_formula (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	param_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	address_represent    VARCHAR(50)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	formula              VARCHAR(100)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_kv8000_param_formula_pid ON t_biz_kv8000_param_formula ( param_id );

CREATE  TABLE t_biz_xjsbb_data (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	param_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	read_data            VARCHAR(256)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	read_time            TIMESTAMP(3)  NOT NULL     ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	synced               TINYINT  NOT NULL DEFAULT ('0')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_xjsbb_data_eid ON t_biz_xjsbb_data ( equip_id );

CREATE INDEX idx_t_biz_xjsbb_data_rtime ON t_biz_xjsbb_data ( read_time );

CREATE INDEX idx_t_biz_xjsbb_data_pid ON t_biz_xjsbb_data ( param_id );

CREATE  TABLE t_biz_xjsbb_log (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	file_path            VARCHAR(256)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	raw_data             VARCHAR(512)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	var_name             VARCHAR(100)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	var_value            VARCHAR(100)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	log_time             TIMESTAMP(3)  NOT NULL     ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	synced               TINYINT  NOT NULL DEFAULT ('0')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_xjsbb_log_eid ON t_biz_xjsbb_log ( equip_id );

CREATE INDEX idx_t_biz_xjsbb_log_ltime ON t_biz_xjsbb_log ( log_time );

CREATE INDEX idx_t_biz_xjsbb_log_vname ON t_biz_xjsbb_log ( var_name );

CREATE  TABLE t_biz_xjsbb_param (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
	variable             VARCHAR(200)  NOT NULL DEFAULT ('')   ,
	variable_name        VARCHAR(100)  NOT NULL DEFAULT ('')   ,
	data_type            VARCHAR(50)  NOT NULL DEFAULT ('')   ,
	data_unit            VARCHAR(50)      ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('')
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_xjsbb_param_var ON t_biz_xjsbb_param ( variable );

CREATE INDEX idx_t_biz_xjsbb_param_eid ON t_biz_xjsbb_param ( equip_id );

CREATE  TABLE t_sys_menu (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	parent_id            BIGINT  NOT NULL DEFAULT ('0')    ,
	icon                 VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	label                VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	path                 VARCHAR(1024)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	component            VARCHAR(1024)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	name                 VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	sort                 TINYINT  NOT NULL DEFAULT ('0')    ,
	is_i18n              TINYINT  NOT NULL DEFAULT ('0')    ,
	is_cache             TINYINT  NOT NULL DEFAULT ('0')    ,
	is_iframe            TINYINT  NOT NULL DEFAULT ('0')    ,
	is_hidden            TINYINT  NOT NULL DEFAULT ('0')    ,
	is_external          TINYINT  NOT NULL DEFAULT ('0')    ,
	is_show_parent       TINYINT  NOT NULL DEFAULT ('1')    ,
	is_enabled           TINYINT  NOT NULL DEFAULT ('1')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE  TABLE t_sys_permission (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	menu_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	value                VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	name                 VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	sort                 TINYINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE  TABLE t_sys_role (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	role                 VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	role_name            VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	sort                 TINYINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE  TABLE t_sys_role_perm (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	role_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	perm_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_sys_role_perm_role_id ON t_sys_role_perm ( role_id );

CREATE  TABLE t_sys_user (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	dept_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	emp_no               VARCHAR(32)  NOT NULL DEFAULT ('')   ,
	password             VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	eng_name             VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	zh_name              VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	zh_name_simple       VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	title                VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	email                VARCHAR(64)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	phone                VARCHAR(32)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	tel_type             VARCHAR(20)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	extension            VARCHAR(32)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	location             VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci   ,
	is_enabled           TINYINT  NOT NULL DEFAULT ('1')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_sys_user_empno ON t_sys_user ( emp_no );

CREATE  TABLE t_sys_user_role (
	id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
	user_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	role_id              BIGINT  NOT NULL DEFAULT ('0')    ,
	is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	created_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT (CURRENT_TIMESTAMP)    ,
	updated_by           BIGINT  NOT NULL DEFAULT ('0')    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ('') COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_sys_user_role_uid ON t_sys_user_role ( user_id );

CREATE  TABLE t_biz_ics_conf (
    id                   BIGINT  NOT NULL DEFAULT ('0')    PRIMARY KEY,
    equip_id             BIGINT  NOT NULL DEFAULT ('0')    ,
    conf_name            VARCHAR(50)  NOT NULL DEFAULT ('')   ,
    conf_key             VARCHAR(50)  NOT NULL DEFAULT ('')   ,
    conf_value           VARCHAR(50)  NOT NULL DEFAULT ('')   ,
    is_deleted           TINYINT  NOT NULL DEFAULT ('0')    ,
    remark               VARCHAR(200)  NOT NULL DEFAULT ('')
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_conf_eid ON t_biz_ics_conf ( equip_id );


INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 1, 0, 'tools', 'menus.hssysManagement', '/system', '', 'System', 0, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-12 03:51:58', 0, '2023-04-12 03:51:58', 0, 'SYSTEM INIT');
INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 2, 1, 'user-filled', 'menus.sys-user', '/system/user', 'system/user/index', 'User', 0, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-12 04:03:41', 0, '2023-04-12 04:03:41', 0, 'SYSTEM INIT');
INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 3, 1, 'stamp', 'menus.sys-role', '/system/role', 'system/role/index', 'Role', 1, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-12 04:05:11', 0, '2023-04-12 04:05:11', 0, 'SYSTEM INIT');
INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 4, 0, 'platform', 'menus.equip-root', '/equipment', '', 'Equipment', 0, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-12 22:35:14', 0, '2023-04-12 22:35:14', 0, 'SYSTEM INIT');
INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 5, 4, 'film', 'menus.equip-auto-ics', '/equipment/auto/ics', 'equipment/auto/ics/index', 'AutoICS', 1, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-12 22:44:52', 0, '2023-04-12 22:44:52', 0, 'SYSTEM INIT');
INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 6, 4, 'histogram', 'menus.equip-ocr-ics', '/equipment/ocr/ics', 'equipment/ocr/ics/index', 'OcrICS', 0, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-12 22:46:26', 0, '2023-04-12 22:46:26', 0, 'SYSTEM INIT');
INSERT INTO t_sys_menu( id, parent_id, icon, label, path, component, name, sort, is_i18n, is_cache, is_iframe, is_hidden, is_external, is_show_parent, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 7, 4, 'notebook', 'menus.equip-parameters', '/equipment/parameters', 'equipment/parameters/index', 'EquipParameters', 2, 1, 0, 0, 0, 0, 1, 1, 0, '2023-04-19 20:37:36', 0, '2023-04-19 20:37:36', 0, 'SYSTEM INIT');

INSERT INTO t_sys_permission( id, menu_id, value, name, sort, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 8, 5, 'autoics:read', 'READ', 0, 0, '2023-04-12 22:48:10', 0, '2023-04-12 22:48:10', 0, 'SYSTEM INIT');
INSERT INTO t_sys_permission( id, menu_id, value, name, sort, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 10, 5, 'autoics:update', 'UPDATE', 0, 0, '2023-12-25 13:13:33', 0, '2023-12-25 13:13:33', 0, 'SYSTEM INIT');
INSERT INTO t_sys_permission( id, menu_id, value, name, sort, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 11, 7, 'eparams:read', 'READ', 0, 1, '2023-12-25 13:15:04', 0, '2023-12-25 13:15:04', 0, 'SYSTEM INIT');

INSERT INTO t_sys_role( id, role, role_name, sort, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 1, 'ADMIN', 'Administrator', 0, 0, '2023-04-12 03:44:34', 0, '2023-04-12 03:44:34', 0, 'SYSTEM INIT');
INSERT INTO t_sys_role( id, role, role_name, sort, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 2, 'DEFAULT', 'User', 1, 0, '2023-04-12 21:56:04', 0, '2023-04-12 21:56:04', 0, 'SYSTEM INIT');

INSERT INTO t_sys_role_perm( id, role_id, perm_id, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 1, 1, 8, 0, '2023-12-25 13:16:50', 0, '2023-12-25 13:16:50', 0, 'SYSTEM INIT');
INSERT INTO t_sys_role_perm( id, role_id, perm_id, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 2, 1, 10, 0, '2023-12-25 13:16:50', 0, '2023-12-25 13:16:50', 0, 'SYSTEM INIT');
INSERT INTO t_sys_role_perm( id, role_id, perm_id, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 3, 1, 11, 0, '2023-12-25 13:16:50', 0, '2023-12-25 13:16:50', 0, 'SYSTEM INIT');

INSERT INTO t_sys_user( id, dept_id, emp_no, password, eng_name, zh_name, zh_name_simple, title, email, phone, tel_type, extension, location, is_enabled, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 1646349940668108801, 1646349940525502464, 'admin', '$2a$10$LIRe/ka5s/NSkuza26SJBOjm12VuwqLN05bBW1QOrOOu8vPVTeski', 'Admin', 'Admin', 'Admin', 'Admin', '', '', 'staff', '', '', 1, 0, '2023-11-28 16:09:57', 0, '2023-11-28 16:09:57', 0, '');

INSERT INTO t_sys_user_role( id, user_id, role_id, is_deleted, created_at, created_by, updated_at, updated_by, description ) VALUES ( 1, 1646349940668108801, 1, 0, '2023-10-17 05:20:09', 0, '2023-10-17 05:20:09', 0, '');
commit;