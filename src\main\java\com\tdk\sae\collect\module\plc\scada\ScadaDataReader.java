package com.tdk.sae.collect.module.plc.scada;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class ScadaDataReader extends ScadaConnection {

    private static final Logger logger = LoggerFactory.getLogger(ScadaDataReader.class);

    private String address;

    private String dataType;

    private String scale;

    public ScadaDataReader(String ip, Integer port) {
        super(ip, port);
    }

    public ScadaDataReader setDataPoint(String address) {
        this.address = address;
        return this;
    }

    public ScadaDataReader setDataType(String dataType) {
        this.dataType = dataType;
        return this;
    }

    public ScadaDataReader setScale(String scale) {
        this.scale = scale;
        return this;
    }

    public KVData read() {
        String[] rawArr = dbComm.GetData(initial, new String[]{address});
        if (rawArr == null) {
            logger.error("error reading " + address + ", scada return null");
            return null;
        }
        try {
            String rawData = rawArr[0].split(",")[1];
            KVData kvData = new KVData(address, rawData, LocalDateTime.now());
            // 读出的都是int, 但是上层逻辑是按照小数来的, 这里按scale算回小数返回
            if (dataType.equals(KVTypeFormat.Constants.REAL)) {
                String data = new BigDecimal(rawData).multiply(new BigDecimal(scale)).toPlainString();
                kvData.setTranslatedData(data);
            } else if (dataType.equals(KVTypeFormat.Constants.DINT)) {
                // 即使是整形, 返回的值也都带.00, int类型时需要把这些去掉
                String data = String.valueOf(new BigDecimal(rawData).intValue());
                kvData.setTranslatedData(data);
            }
            return kvData;
        } catch (Exception e) {
            logger.error("error reading " + address + ", scada return: " + Arrays.toString(rawArr));
        }
        return null;
    }

    public static List<KVData> read(String ip, Integer port, Map<String, KV8000ParamPO> paramMap, String[] addressArr) {
        if (dbComm == null) {
            new ScadaDataReader(ip, port);
        }
        if (!isConnected()) {
            logger.error("Scada is not connected!");
            return new ArrayList<>();
        }
        String[] strtagname = dbComm.GetAllTagName(initial);
        String[] rawArr = dbComm.GetData(initial, strtagname);
        if (ArrayUtil.isEmpty(rawArr)) {
            logger.error("error reading " + Arrays.toString(addressArr) + ", scada return null");
            return new ArrayList<>();
        }
        try {
            List<KVData> result = new ArrayList<>();
            for (String rawData : rawArr) {
                String[] addressAndData = rawData.split(",");
                String address = addressAndData[0];
                String rdata = addressAndData[1];
                KV8000ParamPO param = paramMap.get(address);
                if (param == null) {
                    logger.error("Address:" + address + " not exist!");
                    continue;
                }
                KVData kvData = new KVData(address, rdata, LocalDateTime.now());
                // 读出的都是int, 但是上层逻辑是按照小数来的, 这里按scale算回小数返回
                if (param.getDataType().equals(KVTypeFormat.Constants.REAL)) {
                    String data = new BigDecimal(rdata).multiply(new BigDecimal(param.getCommCycle())).toPlainString();
                    kvData.setTranslatedData(data);
                } else if (param.getDataType().equals(KVTypeFormat.Constants.DINT)) {
                    // 即使是整形, 返回的值也都带.00, int类型时需要把这些去掉
                    String data = String.valueOf(new BigDecimal(rdata).intValue());
                    kvData.setTranslatedData(data);
                }
                result.add(kvData);
            }
            return result;
        } catch (Exception e) {
            logger.error("error reading scada return", e);
        }
        return new ArrayList<>();
    }

}
