package com.tdk.sae.collect.exceptions;

import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class HdsException extends RuntimeException {

    protected ResponseCodeEnum code;
    protected String data;

    public HdsException() {}

    public HdsException(String message) {
        super(message);
        this.code = ResponseCodeEnum.FAIL;
    }

    public HdsException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResponseCodeEnum.FAIL;
    }

    public HdsException(@NonNull ResponseCodeEnum code, String message) {
        super(message);
        this.code = code;
        this.data = message;
    }

}