package com.tdk.sae.collect.module.auth.cache;

import com.tdk.sae.collect.cache.KeyDefine;
import com.tdk.sae.collect.module.auth.param.LoginParams;

import java.util.concurrent.TimeUnit;

public class LoggedInUserKeyDefine extends KeyDefine {

    @Override
    public String getName() {
        return "登录用户缓存";
    }

    @Override
    public String getPrefix() {
        return "logged_in_user";
    }

    @Override
    public long getExpire() {
        return 7L;
    }

    @Override
    public TimeUnit getTimeUnit() {
        return TimeUnit.DAYS;
    }

    @Override
    public Class<?> getClazz() {
        return LoginParams.class;
    }

}
