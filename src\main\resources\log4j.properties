log4j.rootLogger=DEBUG, stdout, debug, error

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.Threshold=INFO
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d %p [%c] - %m%n

log4j.appender.debug=org.apache.log4j.DailyRollingFileAppender
log4j.appender.debug.Append=true
log4j.appender.debug.Threshold=INFO
log4j.appender.debug.Encoding=UTF-8
log4j.appender.debug.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.debug.File=logs/collect_debug.log
log4j.appender.debug.layout=org.apache.log4j.PatternLayout
log4j.appender.debug.layout.ConversionPattern=%d %p [%c] - %m%n

log4j.appender.error=org.apache.log4j.DailyRollingFileAppender
log4j.appender.error.Append=true
log4j.appender.error.Threshold=ERROR
log4j.appender.error.Encoding=UTF-8
log4j.appender.error.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.error.File=logs/collect_error.log
log4j.appender.error.layout=org.apache.log4j.PatternLayout
log4j.appender.error.layout.ConversionPattern=%d %p [%c] - %m%n
