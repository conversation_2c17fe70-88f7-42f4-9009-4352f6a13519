package com.tdk.sae.collect.module.plc.kv8000.rw;

import com.tdk.sae.collect.module.plc.kv8000.KVDataType;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;

public class KVDataStream implements Closeable {

    private static final Logger logger = LoggerFactory.getLogger(KVDataStream.class);

    protected final String ip;

    protected final int port;

    protected int dataLength;

    protected Socket socket;

    public KVDataStream(String ip, int port) throws IOException {
        this.ip = ip;
        this.port = port;
        this.socket = new Socket();
        socket.connect(new InetSocketAddress(ip, port), 200);
    }

    /**
     * KV8000数据类型与自定义数据类型转换规则
     * @param format 自定义数据类型
     * @return KV8000数据类型
     */
    protected KVDataType getTypeByFormat(KVTypeFormat format) {
        switch (format) {
            case BOOL:
            case UINT:
            case STRING:
                return KVDataType.U_16DEC;
            case REAL:
            case DINT:
                dataLength /= 2; // UINT本身表示32位长度,所以只需要读一半就行
                return KVDataType.U_32DEC;
            default:
                throw new RuntimeException("Unknown KV datatype");
        }
    }

    @Override
    public void close() {
        try {
            this.socket.close();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        }
    }

}
