package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ics.mapper.ICSAdjustLogDetailMapper;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
@Service
public class ICSAdjustLogDetailService extends ServiceImpl<ICSAdjustLogDetailMapper, ICSAdjustLogDetailPO> {

    private final ICSAdjustLogDetailMapper icsAdjustLogDetailMapper;

    @Transactional
    public void saveDetails(List<ICSAdjustLogDetailDTO> logDetails) {
        if (CollectionUtil.isEmpty(logDetails)) {
            return;
        }
        List<ICSAdjustLogDetailPO> logDetailPOs = BeanUtil.copyToList(logDetails, ICSAdjustLogDetailPO.class);
        this.saveBatch(logDetailPOs);
    }

    public List<ICSAdjustLogDetailPO> getAdjustLogDetailsAscByTime(Set<String> eIds, LocalDateTime lastTime) {
        return icsAdjustLogDetailMapper.getAdjustLogDetailsAscByTime(eIds, lastTime);
    }

    public List<ICSAdjustLogDetailPO> getAdjustLogDetailsAscByDate(Set<String> eIds, List<LocalDateTime> selectedDateTimes) {
        return icsAdjustLogDetailMapper.getAdjustLogDetailsAscByDate(eIds, selectedDateTimes);
    }

}
