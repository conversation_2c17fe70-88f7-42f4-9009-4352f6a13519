package com.tdk.sae.collect.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
public class BasePO implements Serializable {

    // 默认非删,删除需要传值覆盖
    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy = 0L;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt = LocalDateTime.now();

    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private Long updatedBy = 0L;

    @TableField(value = "updated_at", fill = FieldFill.UPDATE)
    private LocalDateTime updatedAt = LocalDateTime.now();

    @TableField(value = "description")
    private String description = "";

    public static final String DESCRIPTION = "description";

    public static final String CREATED_AT = "created_at";

    public static final String UPDATED_AT = "updated_at";

}