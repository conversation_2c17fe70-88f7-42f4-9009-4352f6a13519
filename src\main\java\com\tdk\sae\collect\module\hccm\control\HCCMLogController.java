package com.tdk.sae.collect.module.hccm.control;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.hccm.module.HCCMTypeEnums;
import com.tdk.sae.collect.module.hccm.service.HCCMResultLogService;
import com.tdk.sae.collect.module.hccm.service.HCCMSliderLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@RestController
@RequestMapping("/client/hccm")
public class HCCMLogController {

    @GetMapping("/info")
    public Response<String> getTrendInfo() {
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.HCCM_RESULT.getName());
        if (CollectionUtil.isNotEmpty(icsLogEquips)) {
            return Response.ok();
        }
        return Response.ok("empty","empty");
    }
    private final HCCMResultLogService hccmResultLogService;
    private final HCCMSliderLogService hccmSliderLogService;
    @GetMapping("/distributeData")
    public Response<Object> distributeData(String selected, String type) {
        if (HCCMTypeEnums.getDisplayNameByKey(type) == null){
            return Response.ok();
        }
        if (StrUtil.isEmpty(selected)) {
            selected = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
        }
        String[] selectedDates = selected.split(",");
        List<LocalDateTime> selectedDateTimes = Arrays.stream(selectedDates)
                .map(d -> LocalDateTimeUtil.parse(d, "yyyy-MM-dd"))
                .collect(Collectors.toList());

        Object data = null;
        if (HCCMTypeEnums.SUSP_X.getDisplayName().equals(type) || HCCMTypeEnums.SUSP_Y.getDisplayName().equals(type)){
            data = hccmResultLogService.getHCCMResultData(selectedDateTimes, type);
        }
        if (HCCMTypeEnums.SUSP_X_Y_BY_POS.getDisplayName().equals(type)){
            data = hccmResultLogService.getXoffsetAndYoffset(selectedDateTimes);
        }
        if (HCCMTypeEnums.SUSP_ANGLE.getDisplayName().equals(type) ||HCCMTypeEnums.AF_SDX.getDisplayName().equals(type) 
                ||HCCMTypeEnums.AF_SDY.getDisplayName().equals(type) ){
            data = hccmSliderLogService.getHCCMSliderData(selectedDateTimes, type);
        }
        if (HCCMTypeEnums.BFSDX_BFSDY.getDisplayName().equals(type)){
            data = hccmSliderLogService.getHCCMSliderBFData(selectedDateTimes);
        }
        return Response.ok(data);

    }
}
