package com.tdk.sae.collect.module.equipment.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquipmentDTO implements Serializable {

    private static final long serialVersionUID = 6334566926823815228L;

    private String id;

    private String equipName;

    private String equipCode;

    private Long groupId;

    private String clientCode;

    private String address;

    private Integer equipType;

    private Integer fileSaveDay;

    private String fileUserDomain;

    private String fileUserAccount;

    private String fileUserPwd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fileUpdatedTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

}
