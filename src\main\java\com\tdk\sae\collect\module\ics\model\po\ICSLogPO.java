package com.tdk.sae.collect.module.ics.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_ics_log")
public class ICSLogPO extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "file_path")
    private String filePath;

    @TableField(value = "raw_data")
    private String rawData;

    @TableField(value = "pos")
    private String pos;

    @TableField(value = "master")
    private String master;

    @TableField(value = "log_value")
    private String logValue;

    @TableField(value = "synced")
    private Integer synced;

    @TableField(value = "result")
    private String result;

    @TableField(value = "log_time")
    private LocalDateTime logTime;

}
