package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogDetailPO;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogPO;
import com.tdk.sae.collect.module.abDim.service.ABAdjustLogDetailService;
import com.tdk.sae.collect.module.abDim.service.ABAdjustLogService;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogPO;
import com.tdk.sae.collect.module.ics.service.ICSAdjustLogDetailService;
import com.tdk.sae.collect.module.ics.service.ICSAdjustLogService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ABAdjustLogUploadService {

    private final ABAdjustLogService icsAdjustLogService;
    private final ABAdjustLogDetailService icsAdjustLogDetailService;

    private final MQProducerService mqProducerService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/15 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<ABAdjustLogPO> logs = icsAdjustLogService.list(Wrappers.lambdaQuery(ABAdjustLogPO.class)
                .eq(ABAdjustLogPO::getSynced, 0)
                .orderByAsc(ABAdjustLogPO::getEndTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ADJUST_AB_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsAdjustLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/15 * * * * ?")
    private synchronized void uploadUnSyncLogDetails() {
        if (!enableUpload) {
            return;
        }
        List<ABAdjustLogDetailPO> logs = icsAdjustLogDetailService.list(Wrappers.lambdaQuery(ABAdjustLogDetailPO.class)
                .eq(ABAdjustLogDetailPO::getSynced, 0)
                .orderByAsc(ABAdjustLogDetailPO::getWriteTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("ADJUST_AB_DETAIL_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsAdjustLogDetailService.saveOrUpdateBatch(logs);
            }
        }
    }

}
