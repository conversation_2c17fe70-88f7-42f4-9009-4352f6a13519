package com.tdk.sae.collect.module.ivs.mapper;

import com.tdk.sae.collect.module.ivs.module.dto.IVSPadTxtDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSPadTxtPo;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Mapper
public interface IVSPadTxtMapper extends BaseMapper<IVSPadTxtPo> {
    List<IVSPadTxtDTO> getPadData(@Param("start")LocalDateTime start, @Param("end")LocalDateTime end);
}
