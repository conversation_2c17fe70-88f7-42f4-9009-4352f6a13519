package com.tdk.sae.collect.module.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tdk.sae.collect.aop.annotation.FieldInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
public class FileLogDTO implements Serializable {

    @FieldInfo(name = "id", columnName = "id")
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime logTime;

}
