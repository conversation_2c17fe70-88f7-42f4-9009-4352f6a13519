package com.tdk.sae.collect.module.abDim.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ab_conf")
public class ABConfPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "equip_id")
    private Long equipId;

    // A1,B1
    @TableField(value = "conf_name")
    private String confName;

    // TARGET
    @TableField(value = "conf_key")
    private String confKey;

    @TableField(value = "conf_value")
    private String confValue;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "remark")
    private String remark;

}
