package com.tdk.sae.collect.module.ics.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ics_lncm_hot_laser_log")
public class ICSHotLaserLog  extends DomainPO {

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "temperature1")
    private String Temperature1;

    @TableField(value = "temperature2")
    private String Temperature2;

    @TableField(value = "temperature3")
    private String Temperature3;

    @TableField(value = "temperature4")
    private String Temperature4;

    @TableField(value = "temperature5")
    private String Temperature5;

    @TableField(value = "temperature6")
    private String Temperature6;

    @TableField(value = "temperature7")
    private String Temperature7;

    @TableField(value = "temperature8")
    private String Temperature8;

    @TableField(value = "temperature9")
    private String Temperature9;

    @TableField(value = "temperature10")
    private String Temperature10;

    @TableField(value = "synced")
    private Integer synced;
}
