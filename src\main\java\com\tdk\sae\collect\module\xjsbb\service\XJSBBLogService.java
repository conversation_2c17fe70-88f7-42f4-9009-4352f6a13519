package com.tdk.sae.collect.module.xjsbb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.xjsbb.mapper.XJSBBLogMapper;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBLogDTO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBLogPO;
import com.tdk.sae.collect.pubsub.event.xjsbb.XJSBBLogEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
public class XJSBBLogService extends ServiceImpl<XJSBBLogMapper, XJSBBLogPO> implements ILogFileService {

    private final ApplicationEventPublisher eventPublisher;

    private static final String REGEX_LOG_ROW = "(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}:\\d{3})   (.*):(.*)\r\n";

    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        XJSBBLogPO lastLog = getOne(Wrappers.lambdaQuery(XJSBBLogPO.class)
                .eq(XJSBBLogPO::getEquipId, equipId)
                .eq(XJSBBLogPO::getFilePath, filePath)
                .orderByDesc(XJSBBLogPO::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, XJSBBLogDTO.class);
    }

    @Override
    public List<XJSBBLogDTO> digestLogFile(Long equipId, File file) {
        return digestLogFile(equipId, file, null);
    }

    @Override
    public List<XJSBBLogDTO> digestLogFile(Long equipId, File file, LocalDateTime latestLogTime) {
        String fileContent = FileUtil.readString(file, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        return doDigest(equipId, file.getPath(), fileContent, latestLogTime);
    }

    private List<XJSBBLogDTO> doDigest(Long equipId, String filePath, String fileContent, LocalDateTime logTime) {
        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);

        List<XJSBBLogDTO> result = new ArrayList<>();
        while (matcher.find()) {
            try{
                XJSBBLogDTO logDTO = new XJSBBLogDTO();
                logDTO.setEquipId(equipId);
                logDTO.setFilePath(filePath);
                logDTO.setRawData(StrUtil.removeAllLineBreaks(matcher.group()));
                logDTO.setLogTime(LocalDateTimeUtil.parse(matcher.group(1), "yyyy-MM-dd HH:mm:ss:SSS"));
                logDTO.setVarName(StrUtil.trim(matcher.group(2)));
                logDTO.setVarValue(StrUtil.trim(matcher.group(3)));
                if (logTime != null && logDTO.getLogTime().compareTo(logTime) <= 0) {
                    continue;
                }
                result.add(logDTO);
            } catch (Exception ignore) {}
        }
        return result;
    }

    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        eventPublisher.publishEvent(new XJSBBLogEvent(this, newLogs));
        return saveBatch(BeanUtil.copyToList(newLogs, XJSBBLogPO.class), newLogs.size());
    }

}
