package com.tdk.sae.collect.tasks;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogTrayMeanPO;
import com.tdk.sae.collect.module.abDim.service.ABAutoAdjustService;
import com.tdk.sae.collect.module.abDim.service.ABLogService;
import com.tdk.sae.collect.module.abDim.service.ABLogTrayMeanService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ABAutoAdjustTaskService {

    private static final Logger logger = LoggerFactory.getLogger(ABAutoAdjustTaskService.class);

    private final ABLogService abLogService;
    private final ABLogTrayMeanService abLogTrayMeanService;
    private final ABAutoAdjustService abAutoAdjustService;

    /**
     * 每5秒执行一次ICS自动调整
     */
    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void startAutoAdjust() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (CollectionUtil.isNotEmpty(icsEquips)) {
            icsEquips.forEach(abAutoAdjustService::startAutoAdjusting);
        }
    }

    /**
     * 每5秒执行一次mean值计算
     */
    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void calculateICSLogMean() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.AB_LOG.getName());
        if (CollectionUtil.isEmpty(icsEquips)) {
            return;
        }
        icsEquips.forEach(equip -> {
            ABLogTrayMeanPO icsLogTrayMeanPO = abLogTrayMeanService.lambdaQuery()
                    .eq(ABLogTrayMeanPO::getEquipId, equip.getId())
                    .orderByDesc(ABLogTrayMeanPO::getEndTime).last("limit 1").one();
            List<ABLogPO> icsLogPOList;
            if (icsLogTrayMeanPO == null) {
                icsLogPOList = abLogService.lambdaQuery()
                        .eq(ABLogPO::getEquipId, equip.getId())
                        .orderByAsc(ABLogPO::getLogTime).last("limit 1000").list();
            } else {
                icsLogPOList = abLogService.lambdaQuery()
                        .eq(ABLogPO::getEquipId, equip.getId())
                        .and(c -> c.gt(ABLogPO::getLogTime, icsLogTrayMeanPO.getEndTime()))
                        .orderByAsc(ABLogPO::getLogTime).last("limit 1000").list();
            }
            abLogTrayMeanService.calculateABLogTrayMean(equip, icsLogPOList);
        });
    }

}
