package com.tdk.sae.collect.module.equipment.controller;

import com.tdk.sae.collect.domain.result.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/equip/client")
public class ClientController {

    @GetMapping("/local/check")
    public Response<String> checkLocal() {
        // 返回clientCode
        return Response.ok("ok", "4D-5B-B19");
    }

}

