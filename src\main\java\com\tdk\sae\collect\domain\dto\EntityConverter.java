package com.tdk.sae.collect.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.lang3.reflect.TypeUtils;

import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 类型转换
 */
public interface EntityConverter<T> {

    /**
     * 转换 domain
     * @return 具有相同值的新域（非空）
     */
    @SuppressWarnings("unchecked")
    default T convertTo() {
        Map<TypeVariable<?>, Type> typeArguments = TypeUtils.getTypeArguments(getClass(), EntityConverter.class);
        if (CollectionUtil.isEmpty(typeArguments)) {
            return null;
        }
        Collection<Type> values = typeArguments.values();
        List<Object> list = CollectionUtil.list(false);
        list.addAll(values);
        Class<T> clazz = (Class<T>) list.get(0);
        return BeanUtil.toBean(this, clazz);
    }

    /**
     * 值更新
     * @param domain 更新目标
     */
    default void update(T domain) {
        BeanUtil.copyProperties(this, domain);
    }

}
