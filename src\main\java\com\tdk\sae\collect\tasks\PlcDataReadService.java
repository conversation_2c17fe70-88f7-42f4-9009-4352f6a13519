package com.tdk.sae.collect.tasks;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IReadPlcService2;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@RequiredArgsConstructor
@Component
public class PlcDataReadService {

    private static final Logger logger = LoggerFactory.getLogger(PlcDataReadService.class);

    @Resource(name = "${system.read-type}")
    private IReadPlcService readPlcService;
    private final SystemProperties systemProperties;

    /**
     * 每1s读一次kv8000参数,有更新则保存
     */
//    @Scheduled(fixedRate = 250)
    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void readParamsAtFixedRate() {
        List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        if (CollectionUtil.isEmpty(kv8000s)) {
            return;
        }
        try {
            kv8000s.forEach(readPlcService::readParams);
        } catch (Exception e) {
            logger.error("PLCRwService Read error:{}", e.getMessage());
        }
    }

}
