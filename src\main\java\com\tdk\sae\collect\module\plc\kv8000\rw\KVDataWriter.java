package com.tdk.sae.collect.module.plc.kv8000.rw;

import com.tdk.sae.collect.module.plc.kv8000.util.KV8000Utils;
import com.tdk.sae.collect.module.plc.kv8000.KVDataType;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.kv8000.util.KVInstructBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class KVDataWriter extends KVDataStream {

    private static final Logger logger = LoggerFactory.getLogger(KVDataWriter.class);

    private String address;

    private KVTypeFormat type;

    public KVDataWriter(String ip, int port) throws IOException {
        super(ip, port);
    }

    public static KVDataWriter getInstance(String ip, int port) throws IOException {
        return new KVDataWriter(ip, port);
    }

    public KVDataWriter setAddress(String address) {
        this.address = address;
        return this;
    }

    public KVDataWriter setType(KVTypeFormat type) {
        this.type = type;
        return this;
    }

    public KVDataWriter setDataLength(int dataLength) {
        this.dataLength = dataLength;
        return this;
    }

    public boolean write(Object ...values) {
        if (address == null || type == null) {
            throw new RuntimeException("address or type not set!");
        }
        if (dataLength <= 0) {
            this.dataLength = type.getDefaultLength();
        }
        return doWrite(values);
    }

    private boolean doWrite(Object ...values) {
        KVDataType dataType = getTypeByFormat(type);
        try {
            String instruct = KVInstructBuilder.getInstance()
                    .write(dataLength, dataType)
                    .address(address)
                    .value(values)
                    .build();
            KVData kvData = KV8000Utils.executeInstruct(socket, instruct);
            logger.error("Writing result from KV8000: " + (kvData == null ? "Null" : kvData.toString()));
            return kvData != null && "OK".equals(kvData.getRawData());
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return false;
    }

}
