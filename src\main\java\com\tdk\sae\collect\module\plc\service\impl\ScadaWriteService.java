package com.tdk.sae.collect.module.plc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.scada.ScadaDataWriter;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import com.tdk.sae.collect.module.plc.service.IWritePlcService2;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service("scadawrite")
public class ScadaWriteService implements IWritePlcService {

    private final Logger logger = LoggerFactory.getLogger(ScadaWriteService.class);

    @Value("${system.auto-mode}")
    private String autoMode;

    @Override
    public Boolean write(EquipmentDTO equip, KV8000ParamDTO param, String value) {
        if (!"PRODUCT".equals(autoMode)) {
            return false;
        }
        String scadaAddress = equip.getAddress();
        if (StrUtil.isEmpty(scadaAddress)) {
            return false;
        }
        Object valueObj = null;
        switch (param.getDataType()) {
            case KVTypeFormat.Constants.BOOL:
            case KVTypeFormat.Constants.UINT:
            case KVTypeFormat.Constants.DINT:
                try {
                    valueObj = Long.parseLong(value);
                } catch (NumberFormatException e) {
                    logger.error("Error parsing value type:" + param.getDataType() + ", address:" + param.getAddress() + ", value:" + value);
                    valueObj = new BigDecimal(value).setScale(0, RoundingMode.HALF_UP).longValue();
                }
                break;
            case KVTypeFormat.Constants.REAL:
                valueObj = new BigDecimal(value).doubleValue();
                break;
            case KVTypeFormat.Constants.STRING:
                byte[] bytes = value.getBytes(StandardCharsets.US_ASCII);
                List<Integer> strValList = new ArrayList<>();
                for (int i = 0; i < bytes.length; i+=2) {
                    int higherByte = bytes[i];
                    int paddingCount = i + 1;
                    int lowerByte = 0;
                    if (paddingCount < bytes.length) {
                        lowerByte = bytes[i + 1];
                    }
                    String hByteStr = Integer.toBinaryString(higherByte);
                    hByteStr = StringUtils.leftPad(hByteStr, 8, '0');
                    String lByteStr = Integer.toBinaryString(lowerByte);
                    lByteStr = StringUtils.leftPad(lByteStr, 8, '0');
                    strValList.add(Integer.parseInt(hByteStr + lByteStr, 2));
                }
                // 用实际长度覆盖最大长度
                param.setDataLength(strValList.size());
                valueObj = strValList.toArray();
                break;
            default:
                throw new IllegalArgumentException("Type not found!");
        }
        String[] ipPort = scadaAddress.split(":");
        String ip = ipPort[0];
        int port = Integer.parseInt(ipPort[1]);
        ScadaDataWriter writer = new ScadaDataWriter(ip, port);
        try {
            return writer.setDataPoint(param.getAddress())
                    .setDataType(param.getDataType())
                    .setScale(param.getCommCycle())
                    .write(valueObj);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return false;
    }

}
