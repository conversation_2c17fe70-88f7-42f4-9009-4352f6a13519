package com.tdk.sae.collect.module.ics.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ics.mapper.ICSAdjustRulesMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ICSAdjustRulesService extends ServiceImpl<ICSAdjustRulesMapper, ICSAdjustRulesPO> {
}
