package com.tdk.sae.collect.domain.common;

import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@EqualsAndHashCode
@ToString
@Configuration
@ConfigurationProperties(prefix = "system")
public class SystemProperties {

    private String clientCode;

    private Long datacenterId;

    private Long machineId;

    private Integer writeTime;

    private ServerProperties server;

    private String readType;

    public String getType(){
        String value;
        if (readType.contains("kv8000")){
            value = EquipmentTypeEnum.KEYENCE_KV8000.getName();
        }else {
            value = EquipmentTypeEnum.SCADA.getName();
        }
        return value;
    }

}
