server:
  port: 8080

system:
  clientCode: 4D-B26
  fileCacheFolder: E://project_data/fileCache
  # 这是服务器t_biz_equip_group表中对应groupId
  datacenterId: 7
  # t_biz_equip表对应equipId
  machineId: 31
  server:
    endpoint: http://*************:8080
  write-time: 0
  projectCode: BTS
  auto-mode: PRODUCT1
  read-type: kv8000read
  write-type: kv8000write
  # 关闭上传功能
  enable-upload: false

spring:
  jackson:
    generator:
      write_numbers_as_strings: true
  main:
    allow-circular-references: true
  application:
    name: collect
  config:
    import:
      - classpath:config/datasource-config.yml
      - classpath:config/mybatis-config.yml
      - classpath:config/redis-config.yml

# 文件系统
oss:
  path-style-access: true
  endpoint: http://*************:9200
  access-key: adminuser
  secret-key: adminuser
  bucketName: test

rocketmq:
  name-server: *************:9876 # 访问地址
  producer:
    group: AUTO_ADJUST_GROUP # 必须指定group
    send-message-timeout: 3000 # 消息发送超时时长，默认3s
    retry-times-when-send-failed: 3 # 同步发送消息失败重试次数，默认2
    retry-times-when-send-async-failed: 3 # 异步发送消息失败重试次数，默认2
