package com.tdk.sae.collect.tasks;



import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.equipment.model.po.EquipmentPO;
import com.tdk.sae.collect.module.ivs.module.dto.IVSConfDto;
import com.tdk.sae.collect.module.ivs.module.po.IVSConfPO;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import com.tdk.sae.collect.module.ivs.service.IVSConfService;
import com.tdk.sae.collect.module.ivs.service.IVSLogService;
import com.tdk.sae.collect.module.ivs.service.IVSTxtService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@RequiredArgsConstructor
@Component
public class IVSLogReadService {

    private static final Logger logger = LoggerFactory.getLogger(IVSLogReadService.class);
    private final IVSLogService ivsLogService;
    private final IVSTxtService ivsTxtService;
    private final SystemProperties systemProperties;
    private final IVSConfService ivsConfService;
    /**
     * 每5秒执行一次IVS日志读取
     */
    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void startReadLog() {
        //升序
        List<IVSLogPo> ivsLogPos = ivsLogService.getBaseMapper()
                .selectList(new QueryWrapper<IVSLogPo>()
                        .eq("read_type", 0)  // 查询 read_type 为 0 的记录
                        .orderByAsc("log_time"));  // 按照 log_time 升序排序
        if(CollectionUtils.isEmpty(ivsLogPos)){
            return;
        }
        logger.info("startParseData ivsLogPos size:{}", ivsLogPos.size());
        ivsTxtService.parseData(ivsLogPos);
    }

    @Scheduled(cron = "0 0 * * * ?")
    private synchronized void getIVSConfInfo() {
        List<EquipmentDTO> icsEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.IVS_LOG.getName());
        if(CollectionUtil.isEmpty(icsEquips)){
            return;
        }
        HttpRequest request = HttpUtil.createGet(systemProperties.getServer().getEndpoint() + "/biz/ivs/confInfo");
        try {
            HttpResponse response = request.execute();
            JSONObject result = JSONObject.parseObject(response.body());
            List<IVSConfDto> confDtoList = JSONObject.parseArray(result.get("data").toString(), IVSConfDto.class);
            if (!confDtoList.isEmpty()) {
                ivsConfService.saveOrUpdateBatch(BeanUtil.copyToList(confDtoList, IVSConfPO.class));
            }
        } catch (IORuntimeException ioRuntimeException) {
            logger.error("Can't not connect to HDS Server!");
        }
    }

}
