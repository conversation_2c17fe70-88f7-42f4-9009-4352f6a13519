package com.tdk.sae.collect.module.ivs.service;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.tdk.sae.collect.module.ivs.mapper.IVSTxtMapper;
import com.tdk.sae.collect.module.ivs.module.dto.IVSPadTxtDTO;
import com.tdk.sae.collect.module.ivs.module.dto.IVSTxtDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import com.tdk.sae.collect.module.ivs.module.po.IVSTxtPo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class IVSTxtService extends ServiceImpl<IVSTxtMapper, IVSTxtPo> {
    private static final Logger logger = LoggerFactory.getLogger(IVSTxtService.class);

    private final IVSLogService ivsLogService;
    private final IVSPadTxtService ivsPadTxtService;
    private final static String NG = "NG";
    private final IVSTxtMapper ivsTxtMapper;


    @Transactional(rollbackFor = Exception.class)
    public void parseData(List<IVSLogPo> ivsLogPos){
        List<IVSLogPo> ivsLogPosPart = Lists.newArrayList();
        for (IVSLogPo ivsLogPo : ivsLogPos) {
            ivsLogPosPart.add(ivsLogPo);
            if (ivsLogPo.getResultType() == 1) {
                IVSTxtPo ivsTxt = parseIVSTxt(ivsLogPo);
                if (ivsTxt != null) {
                    for (IVSLogPo part : ivsLogPosPart) {
                        part.setReadType(1);
                        if (part.getResultType() == 2) {
                            ivsPadTxtService.parseIVSPadTxt(part, ivsTxt.getId(),ivsTxt.getDirection());
                        }
                    }
                }else {
                    logger.error("IVS数据解析失败，检测结果解析失败，对应pad数据不入库: {}", ivsLogPo.getRawData());
                }
                ivsLogService.updateBatchById(ivsLogPosPart);
                ivsLogPosPart.clear();
            }

        }
    }

    public IVSTxtPo parseIVSTxt(IVSLogPo ivsLogPo) {
        String input = ivsLogPo.getRawData();
        // 解析字符串
        String[] parts = input.split(",");
        if (parts.length < 12) {
            logger.error("IVS数据解析失败，数据格式错误: {}", input);
            return null;
        }
        IVSTxtPo ivsTxt = new IVSTxtPo();

        LocalDateTime collectTime = LocalDateTimeUtil.parse(parts[0], "yyyy/MM/dd HH:mm:ss");
        // 创建 IVSTxt 实体
        ivsTxt.setCollectTime(collectTime);
        ivsTxt.setResult0(handleEmptyResult(parts[2])); // result_0
        ivsTxt.setResult1(handleEmptyResult(parts[3])); // result_1
        ivsTxt.setResult2(handleEmptyResult(parts[4])); // result_2
        ivsTxt.setResult3(handleEmptyResult(parts[5])); // result_3
        ivsTxt.setResult4(handleEmptyResult(parts[6])); // result_4
        ivsTxt.setResult5(handleEmptyResult(parts[7])); // result_5
        ivsTxt.setResult6(handleEmptyResult(parts[8])); // result_6
        ivsTxt.setResult7(handleEmptyResult(parts[9])); // result_7
        ivsTxt.setResult8(handleEmptyResult(parts[10])); // result_8
        ivsTxt.setResult9(handleEmptyResult(parts[11]));
        String direction = parts[1].substring(0, 1);
        ivsTxt.setDirection(direction); // 方向 (L检测结果)
        ivsTxt.setEquipmentId(ivsLogPo.getEquipId());
        //入库
        save(ivsTxt);
        return ivsTxt;
    }

    // 如果 result 是空字符串，设置为 "NG"
    private String handleEmptyResult(String result) {
        return StringUtils.isEmpty(result) ? NG : result;
    }

    public Map<String,Object> getIvsData(String start_time, String end_time){
        Map<String,Object> resultMap=new HashMap<>();
        Integer[] Lcount=new Integer[10];
        Arrays.fill(Lcount,0);
        Integer[] Rcount=new Integer[10];
        Arrays.fill(Rcount,0);
        resultMap.put("L-IVS",Lcount);
        resultMap.put("R-IVS",Rcount);
        LocalDateTime start=LocalDateTimeUtil.parse(start_time, DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime end=LocalDateTimeUtil.parse(end_time, DatePattern.NORM_DATETIME_PATTERN);
        List<IVSTxtDTO> padTxtDTOList = ivsTxtMapper.getIvsTxtData(start,end);
        padTxtDTOList.forEach(pad->{
            String direction=pad.getDirection();
            String resultInfo=pad.getResultInfo();
            String[] resultStr=resultInfo.split("-");
            for(Integer i=0;i<10;i++) {
                if (direction.equals("L")) {
                    if(!resultStr[i].equals("OK")){
                        Lcount[i]=Lcount[i]+1;
                    }
                }else{
                    if(!resultStr[i].equals("OK")){
                        Rcount[i]=Rcount[i]+1;
                    }
                }
            }
        });
        return resultMap;
    }
}
