package com.tdk.sae.collect.module.plc.controller;

import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IWritePlcService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequiredArgsConstructor
@RestController
@RequestMapping("/plc")
public class PlcRwController {

    @Resource(name = "${system.read-type}")
    private IReadPlcService readPlcService;

    @Resource(name = "${system.write-type}")
    private IWritePlcService writePlcService;

    @GetMapping("/get")
    public Response<KVData> getPlcValue(String address, String type) {
//        KVData result = readPlcService.testRead(address, type);
//        return Response.ok(result);
        return null;
    }

    @GetMapping("/set")
    public Response<Boolean> setPlcValue(String address, String type, String value) {
//        Boolean result = writePlcService.testWrite(address, type, value);
//        return Response.ok(result);
        return null;
    }

}
