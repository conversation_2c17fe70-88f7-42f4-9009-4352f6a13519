CREATE TABLE IF NOT EXISTS `t_biz_ivs_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `read_type` tinyint NOT NULL DEFAULT '0',
  `result_type` tinyint NOT NULL,
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ivs_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ivs_log_eid` (`equip_id`),
  KEY `idx_t_biz_ivs_log_ltime` (`log_time`),
  KEY `idx_t_biz_ivs_log_sync` (`synced`),
  KEY `t_biz_ivs_log_read_type_IDX` (`read_type`) USING BTREE,
  KEY `t_biz_ivs_log_result_type_IDX` (`result_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$

-- `collect`.t_biz_ivs_pad_txt definition

CREATE TABLE IF NOT EXISTS  `t_biz_ivs_pad_txt` (
  `id` bigint NOT NULL,
  `collect_time` datetime DEFAULT NULL,
  `result` varchar(40) DEFAULT NULL,
  `is_deleted` tinyint DEFAULT (0),
  `created_by` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `pos` tinyint NOT NULL,
  `synced` tinyint NOT NULL DEFAULT '0',
  `check_id` bigint NOT NULL,
  `direction` varchar(10) NOT NULL,
  `equip_id` bigint NOT NULL,
  `description` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `t_biz_ivs_pad_txt_collect_time_IDX` (`collect_time`) USING BTREE,
  KEY `t_biz_ivs_pad_txt_pos_IDX` (`pos`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci$$



CREATE TABLE IF NOT EXISTS  `t_biz_ivs_txt` (
  `id` bigint NOT NULL,
  `collect_time` datetime DEFAULT NULL,
  `result_0` varchar(40) DEFAULT NULL,
  `result_1` varchar(40) DEFAULT NULL,
  `result_2` varchar(40) DEFAULT NULL,
  `result_3` varchar(40) DEFAULT NULL,
  `result_4` varchar(40) DEFAULT NULL,
  `result_5` varchar(40) DEFAULT NULL,
  `result_6` varchar(40) DEFAULT NULL,
  `result_7` varchar(40) DEFAULT NULL,
  `result_8` varchar(40) DEFAULT NULL,
  `result_9` varchar(40) DEFAULT NULL,
  `equipment_id` bigint DEFAULT NULL,
  `is_deleted` tinyint DEFAULT (0),
  `created_by` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `synced` tinyint NOT NULL DEFAULT '0',
  `direction` varchar(10) NOT NULL,
  `description` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `t_biz_ivs_txt_collect_time_IDX` (`collect_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci$$

-- `collect`.t_biz_ivs_all_log definition

CREATE TABLE IF NOT EXISTS `t_biz_ivs_all_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ivs_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ivs_log_eid` (`equip_id`),
  KEY `idx_t_biz_ivs_log_ltime` (`log_time`),
  KEY `idx_t_biz_ivs_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$

CREATE TABLE IF NOT EXISTS `t_biz_ics_lncm_hot_laser_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `temperature1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature6` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature7` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature8` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature9` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature10` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_trend_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_trend_log_time` (`created_at`),
  KEY `idx_t_biz_ics_trend_log_sync` (`synced`),
  KEY `t_biz_ics_trend_log_description_IDX` (`description`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$


CREATE TABLE IF NOT EXISTS `t_biz_ab_conf` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `conf_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `conf_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `conf_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ab_conf_eid` (`equip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$