package com.tdk.sae.collect.module.ivs.module.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ivs_txt")
public class IVSTxtPo extends DomainPO  {

    @TableField(value = "collect_time")
    private LocalDateTime collectTime;

    @TableField(value = "equipment_id")
    private Long equipmentId;

    @TableField(value = "result_0")
    private String result0;

    @TableField(value = "result_1")
    private String result1;
    @TableField(value = "result_2")
    private String result2;
    @TableField(value = "result_3")
    private String result3;
    @TableField(value = "result_4")
    private String result4;
    @TableField(value = "result_5")
    private String result5;
    @TableField(value = "result_6")
    private String result6;
    @TableField(value = "result_7")
    private String result7;
    @TableField(value = "result_8")
    private String result8;
    @TableField(value = "result_9")
    private String result9;

    @TableField(value = "direction")
    private String direction;

    @TableField(value = "synced")
    private Integer synced;


}
