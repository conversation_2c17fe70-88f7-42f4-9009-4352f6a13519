<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.ics.mapper.ICSAdjustRulesApplyMapper">

    <select id="getRulesPopulate" resultType="com.tdk.sae.collect.module.ics.model.adjust.ICSAdjustRulesPopulate">
        select
            r1.id as apply_id, r1.equip_id, r1.rule_id, r1.rule, r1.enabled, r2.description as classz, r2.rule_type, r2.rule_weight
        from t_biz_ics_adjust_rules_apply r1
        left join t_biz_ics_adjust_rules r2 on r1.rule_id = r2.id and r2.is_deleted = 0
        where r1.equip_id = #{equipId}
        order by r2.rule_type desc, r2.rule_weight desc
    </select>

    <select id="getRulesApplyByType" resultType="com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO">
        select
            r1.*
        from t_biz_ics_adjust_rules_apply r1
        left join t_biz_ics_adjust_rules r2 on r1.rule_id = r2.id and r2.is_deleted = 0
        where
            r1.equip_id = #{equipId}
            and r2.rule_type = #{ruleType}
        order by r2.rule_weight desc
    </select>

</mapper>
