package com.tdk.sae.collect.module.ics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ICSLogMapper extends BaseMapper<ICSLogPO> {

    @Delete("DELETE FROM t_biz_ics_log WHERE log_time < #{time}")
    int deleteICSLogData(@Param("time") String time);

}
