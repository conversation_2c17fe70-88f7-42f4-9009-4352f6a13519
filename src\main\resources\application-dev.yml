spring:
  config:
    import:
      - classpath:config/datasource-config-dev.yml
      - classpath:config/mybatis-config.yml
      - classpath:config/redis-config-dev.yml

rocketmq:
  name-server: 43.154.93.77:9001 # 访问地址
  producer:
    group: AUTO_ADJUST_GROUP # 必须指定group
    send-message-timeout: 3000 # 消息发送超时时长，默认3s
    retry-times-when-send-failed: 3 # 同步发送消息失败重试次数，默认2
    retry-times-when-send-async-failed: 3 # 异步发送消息失败重试次数，默认2
