package com.tdk.sae.collect.module.plc.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode
public class KV8000DataDTO {

    private Long id;

    private Long paramId;

    private String command;

    private String rawData;

    private String readData;

    private LocalDate readTime;

    private Integer isDeleted = 0;

    private Integer synced;

    @JsonIgnore
    private String address;

    private String addressRepresent;

}
