package com.tdk.sae.collect.module.common.controller;

import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.pubsub.listener.adjust.AdjustEventListener;
import com.tdk.sae.collect.pubsub.listener.adjust.PidAdjustEventListener;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/websocket")
public class WebSocketController {
    @MessageMapping("/subscribeTopicInfo") // 前端发送消息的目的地
    public void receiveMessage(String topic) {
        // 处理接收到的消息
        Integer topicNum= AdjustEventListener.topicMap.computeIfAbsent(topic, k->0);
        if(topicNum>0){
            topicNum++;
        }else{
            topicNum=1;
        }
        AdjustEventListener.topicMap.put(topic,topicNum);
    }
    @MessageMapping("/unsubscribeTopicInfo") // 前端发送消息的目的地
    public void unsubscribeTopicInfo(String topic) {
        // 处理接收到的消息
        Integer topicNum=AdjustEventListener.topicMap.computeIfAbsent(topic,k->0);
        if(topicNum>0){
            topicNum--;
        }else{
            topicNum=0;
        }
        if(topicNum==0){
            String equid=topic.substring(7,topic.lastIndexOf("/"));
            String param=topic.substring(topic.lastIndexOf("/")+1);
            AdjustEventListener.lastTimeMap.get(equid).put(param,null);
        }
        AdjustEventListener.topicMap.put(topic,topicNum);
    }
    @GetMapping("/getTopicInfo")
    public Response<Object> getTopicInfo(){
        return Response.ok("success",AdjustEventListener.topicMap);
    }


    @MessageMapping("/pid/subscribeTopicInfo") // 前端发送消息的目的地
    public void receivePidMessage(String topic) {
        // 处理接收到的消息
        Integer topicNum= PidAdjustEventListener.topicMap.computeIfAbsent(topic, k->0);
        if(topicNum>0){
            topicNum++;
        }else{
            topicNum=1;
        }
        PidAdjustEventListener.topicMap.put(topic,topicNum);
    }
    @MessageMapping("/pid/unsubscribeTopicInfo") // 前端发送消息的目的地
    public void unsubscribeTopicPidInfo(String topic) {
        // 处理接收到的消息
        Integer topicNum=PidAdjustEventListener.topicMap.computeIfAbsent(topic,k->0);
        if(topicNum>0){
            topicNum--;
        }else{
            topicNum=0;
        }
        if(topicNum==0){
            String equid=topic.substring(7,topic.lastIndexOf("/"));
            String param=topic.substring(topic.lastIndexOf("/")+1);
            PidAdjustEventListener.lastTimeMap.get(equid).put(param,null);
        }
        PidAdjustEventListener.topicMap.put(topic,topicNum);
    }
    @GetMapping("/pid/getTopicInfo")
    public Response<Object> getTopicPidInfo(){
        return Response.ok("success",PidAdjustEventListener.topicMap);
    }

    //pid topic
    @MessageMapping("/subscribePidTopicInfo") // 前端发送消息的目的地
    public void subscribePidTopicInfo(String topic) {
        // 处理接收到的消息
        PidAdjustEventListener.topicMap.merge(topic, 1, Integer::sum);
    }
    @MessageMapping("/unsubscribePidTopicInfo") // 前端发送消息的目的地
    public void unsubscribePidTopicInfo(String topic) {
        // 处理接收到的消息
        Integer topicNum=PidAdjustEventListener.topicMap.merge(topic, 0, (old, unused) -> Math.max(old - 1, 0));
        if(topicNum==0){
            String equid=topic.substring(7,topic.lastIndexOf("/"));
            String param=topic.substring(topic.lastIndexOf("/")+1);
            PidAdjustEventListener.lastTimeMap.get(equid).put(param,null);
        }
    }
    @GetMapping("/getPidTopicInfo")
    public Response<Object> getPidTopicInfo(){
        return Response.ok("success",PidAdjustEventListener.topicMap);
    }

}
