package com.tdk.sae.collect.module.auth.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.auth.mapper.RolePermissionMapper;
import com.tdk.sae.collect.module.auth.po.RolePermissionPO;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class RolePermissionService extends ServiceImpl<RolePermissionMapper, RolePermissionPO> {

    private final RolePermissionMapper rolePermissionMapper;

    @Nullable
    public Map<Long, List<Long>> getPermissionIdByRoleIds(@NotNull Collection<Long> roleIds) {
        Assert.notEmpty(roleIds, "角色id为空");
        LambdaQueryWrapper<RolePermissionPO> queryWrapper = Wrappers.lambdaQuery(RolePermissionPO.class)
                .in(RolePermissionPO::getRoleId, roleIds);
        List<RolePermissionPO> rolePermissionPOList = rolePermissionMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(rolePermissionPOList)) {
            return null;
        }
        return rolePermissionPOList.stream()
                .collect(
                        Collectors.groupingBy(
                                RolePermissionPO::getRoleId,
                                Collectors.mapping(RolePermissionPO::getPermId, Collectors.toList())
                        )
                );
    }

}
