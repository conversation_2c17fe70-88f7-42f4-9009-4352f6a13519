package com.tdk.sae.collect.module.ivs.module.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IVSConfDto implements Serializable {

    private static final long serialVersionUID = 6334566926823815228L;

    private String id;

    private String confName;

    private String confValue;

}
