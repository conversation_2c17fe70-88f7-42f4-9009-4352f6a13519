package com.tdk.sae.collect.module.xjsbb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.xjsbb.model.dto.XJSBBDataDTO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBDataPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface XJSBBDataMapper extends BaseMapper<XJSBBDataPO> {

    List<XJSBBDataDTO> getLatestData();

}
