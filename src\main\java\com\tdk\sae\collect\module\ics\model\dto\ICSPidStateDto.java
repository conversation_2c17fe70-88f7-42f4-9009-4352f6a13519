package com.tdk.sae.collect.module.ics.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class ICSPidStateDto {
    private BigDecimal integral = BigDecimal.ZERO;
    private BigDecimal previousError = BigDecimal.ZERO;
    private int elementSize=0;
    private BigDecimal value=BigDecimal.ZERO;
    private LocalDateTime FirstTime=null;

}
