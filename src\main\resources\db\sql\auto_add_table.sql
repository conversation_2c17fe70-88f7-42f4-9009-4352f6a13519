CREATE TABLE IF NOT EXISTS  `t_biz_ics_pid_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `parameter` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `log_value` decimal(10,2) NOT NULL,
  `log_time` timestamp(3) NOT NULL,
  `write_time` timestamp(3) NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `from_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `to_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `log_msg` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `param_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  PRIMARY KEY (`id`),
  KEY `t_biz_ics_pid_log_equip_id_IDX` (`equip_id`) USING BTREE,
  KEY `t_biz_ics_pid_log_parameter_IDX` (`parameter`) USING BTREE,
  KEY `t_biz_ics_pid_log_log_time_IDX` (`log_time`) USING BTREE,
  KEY `t_biz_ics_pid_log_synced_IDX` (`synced`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$


-- `collect`.t_biz_hccm_slider_log definition

CREATE TABLE IF NOT EXISTS `t_biz_hccm_slider_log` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `pos` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `master` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `log_value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `synced` tinyint NOT NULL DEFAULT '0',
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ics_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ics_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_log_ltime` (`log_time`),
  KEY `idx_t_biz_ics_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$



-- `collect`.t_biz_hccm_result_log definition

CREATE TABLE IF NOT EXISTS  `t_biz_hccm_result_log` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `pos` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `master` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `log_value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `synced` tinyint NOT NULL DEFAULT '0',
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ics_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ics_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_log_ltime` (`log_time`),
  KEY `idx_t_biz_ics_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$



-- `collect`.t_biz_ivs_conf definition

CREATE TABLE IF NOT EXISTS `t_biz_ivs_conf` (
  `id` bigint NOT NULL DEFAULT '0',
  `conf_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `conf_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$