package com.tdk.sae.collect.module.plc.kv8000.entity;

import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;

import java.io.Serializable;
import java.time.LocalDateTime;

public final class KVData implements Serializable {

    private final String instruct;

    private final String rawData;

    private String translatedData;

    private final LocalDateTime fetchDate;

    public KVData(String instruct, String rawData, LocalDateTime fetchDate) {
        this.instruct = instruct;
        this.rawData = rawData;
        this.fetchDate = fetchDate;
    }

    public String getInstruct() {
        return instruct;
    }

    public String getRawData() {
        return rawData;
    }

    public String getTranslatedData() {
        return translatedData;
    }

    public LocalDateTime getFetchDate() {
        return fetchDate;
    }

    public void setTranslatedData(String translatedData) {
        this.translatedData = translatedData;
    }

    @Override
    public String toString() {
        return "KVData{" +
                "instruct='" + instruct.replaceAll("\\r","#") + '\'' +
                ", rawData='" + rawData + '\'' +
                ", translatedData='" + translatedData + '\'' +
                ", fetchDate=" + fetchDate +
                '}';
    }

    public KV8000DataPO convert(KV8000ParamPO param) {
        KV8000DataPO data = new KV8000DataPO();
        data.setParamId(param.getId());
        data.setCommand(this.getInstruct());
        data.setReadTime(this.getFetchDate());
        data.setRawData(this.getRawData());
        data.setReadData(this.getTranslatedData());
        data.setSynced(0);
        return data;
    }
}
