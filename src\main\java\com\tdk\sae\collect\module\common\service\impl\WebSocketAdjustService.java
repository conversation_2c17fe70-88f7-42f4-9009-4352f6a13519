package com.tdk.sae.collect.module.common.service.impl;

import com.tdk.sae.collect.domain.result.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor

public class WebSocketAdjustService {
    private final SimpMessagingTemplate simpMessagingTemplate;
    public void sendMessage(String destination, Map<String, Object> message){
        simpMessagingTemplate.convertAndSend(destination, Response.ok(message));
    }
}
