package com.tdk.sae.collect.module.auth.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_menu")
public class MenuPO extends DomainPO {

    private static final long serialVersionUID = 1L;

    /**
     * 是否外链
     */
    @TableField("is_external")
    private Integer external;
    /**
     * iframe菜单
     */
    @TableField("is_iframe")
    private Integer iframe;
    /**
     * cache
     */
    @TableField("is_cache")
    private Integer cache;
    /**
     * 是否可见
     */
    @TableField("is_hidden")
    private Integer hidden;
    /**
     * 是否18n;
     */
    @TableField("is_i18n")
    private Integer i18n;
    /**
     * 是否显示父级菜单
     */
    @TableField("is_show_parent")
    private Integer showParent;
    /**
     * 名称
     */
    @TableField("label")
    private String label;
    /**
     * 组件名称
     */
    @TableField("name")
    private String name;

    /**
     * 父菜单id
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 路由地址
     */
    @TableField("path")
    private String path;

    /**
     * 组件地址
     */
    @TableField("component")
    private String component;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 展示顺序
     */
    @TableField("sort")
    private Integer sort;

    @TableField("is_enabled")
    private Integer isEnabled;


    public static final String LABEL = "label";

    public static final String NAME = "name";

    public static final String PARENT_ID = "parent_id";

    public static final String PATH = "path";

    public static final String COMPONENT = "component";

    public static final String ICON = "icon";

    public static final String SORT = "sort";

    public static final String IS_ENABLED = "is_enabled";

}