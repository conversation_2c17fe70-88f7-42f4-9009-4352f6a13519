package com.tdk.sae.collect.module.abDim.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.abDim.model.adjust.ABAdjustRulesPopulate;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustRulesApplyPO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ABAdjustRulesApplyMapper extends BaseMapper<ABAdjustRulesApplyPO> {

    List<ABAdjustRulesPopulate> getRulesPopulate(@Param("equipId") Long equipId);

    List<ABAdjustRulesApplyPO> getRulesApplyByType(@Param("equipId") Long equipId, @Param("ruleType") Integer ruleType);

}
