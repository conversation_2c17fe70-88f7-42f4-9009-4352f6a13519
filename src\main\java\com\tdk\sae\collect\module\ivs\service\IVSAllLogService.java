package com.tdk.sae.collect.module.ivs.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.CharsetDetector;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.common.model.dto.FileLogDTO;
import com.tdk.sae.collect.module.common.service.ILogFileService;
import com.tdk.sae.collect.module.ivs.mapper.IVSAllLogMapper;
import com.tdk.sae.collect.module.ivs.module.dto.IVSAllLogDTO;
import com.tdk.sae.collect.module.ivs.module.dto.IVSAllLogDataDto;
import com.tdk.sae.collect.module.ivs.module.po.IVSAllLogPo;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class IVSAllLogService extends ServiceImpl<IVSAllLogMapper, IVSAllLogPo> implements ILogFileService {

    private static final String REGEX_LOG_ROW = "\\d{4}/\\d{1,2}/\\d{1,2} .*,.*\n|.*\r\n";
    private final String emptyStr1="OK=0,NG=0,Yield=NaN";
    private final String emptyStr2="Empty=0,T1=0,T2=0,T3=0,T4=0,T5=0,T6=0,T7=0,TOther=0,OK=0";
    private final String LemptyStr="LEmpty=0,LT1=0,LT2=0,LT3=0,LT4=0,LT5=0,LT6=0,LT7=0,LTOther=0,LOK=0";
    private final String RemptyStr="REmpty=0,RT1=0,RT2=0,RT3=0,RT4=0,RT5=0,RT6=0,RT7=0,RTOther=0,ROK=0";
    public  final String REGEX_1 = "OK=([\\s\\S]*?),NG=([\\s\\S]*?),Yield=([\\s\\S]*)";
    public  final String REGEX_2 = "Empty=([\\s\\S]*?),T1=([\\s\\S]*?),T2=([\\s\\S]*?),T3=([\\s\\S]*?),T4=([\\s\\S]*?),T5=([\\s\\S]*?),T6=([\\s\\S]*?),T7=([\\s\\S]*?),TOther=([\\s\\S]*?),OK=([\\s\\S]*?),";
    public  final String REGEX_3 = "LEmpty=([\\s\\S]*?),LT1=([\\s\\S]*?),LT2=([\\s\\S]*?),LT3=([\\s\\S]*?),LT4=([\\s\\S]*?),LT5=([\\s\\S]*?),LT6=([\\s\\S]*?),LT7=([\\s\\S]*?),LTOther=([\\s\\S]*?),LOK=([\\s\\S]*?),";
    public  final String REGEX_4 = "REmpty=([\\s\\S]*?),RT1=([\\s\\S]*?),RT2=([\\s\\S]*?),RT3=([\\s\\S]*?),RT4=([\\s\\S]*?),RT5=([\\s\\S]*?),RT6=([\\s\\S]*?),RT7=([\\s\\S]*?),RTOther=([\\s\\S]*?),ROK=([\\s\\S]*?),";
    public  final Integer reservation=3;

    private final IVSLogService ivsLogService;
    @Override
    public FileLogDTO getLastLog(Long equipId, String filePath) {
        IVSAllLogPo lastLog = getOne(Wrappers.lambdaQuery(IVSAllLogPo.class)
                .eq(IVSAllLogPo::getEquipId, equipId)
                .eq(IVSAllLogPo::getFilePath, filePath)
                .orderByDesc(IVSAllLogPo::getLogTime).last("limit 1"));
        return BeanUtil.toBean(lastLog, IVSAllLogDTO.class);
    }


    @Transactional
    @Override
    public boolean handleNewLogs(List<? extends FileLogDTO> newLogs) {
        for(FileLogDTO logDTO:newLogs){
            IVSAllLogDTO IVSAllLogDTO= (IVSAllLogDTO) logDTO;
            try{
                IVSAllLogPo addLog=BeanUtil.copyProperties(IVSAllLogDTO,IVSAllLogPo.class);
                LocalDateTime logTime=addLog.getLogTime();
                LocalDateTime start=null;
                LocalDateTime end=null;
                if(logTime.getHour()>=12 && logTime.getHour()<=23){
                    start=logTime.withHour(12).withMinute(0).withSecond(0);
                    end=logTime.withHour(23).withMinute(59).withSecond(59);
                }else{
                    start=logTime.withHour(0).withMinute(0).withSecond(0);
                    end=logTime.withHour(11).withMinute(59).withSecond(59);
                }
                Long count=this.lambdaQuery()
                        .eq(IVSAllLogPo::getDescription,addLog.getDescription())
                        .ge(IVSAllLogPo::getLogTime,start)
                        .le(IVSAllLogPo::getLogTime,end)
                        .count();
                if(count==0L){
                    save(addLog);
                }
//                save(BeanUtil.copyProperties(IVSAllLogDTO,IVSAllLogPo.class));
            }catch (Exception e){
            }
        }
        return true;
    }

    /**
     * read file to Log records
     * @param f file
     * @return Log records
     */
    public List<IVSAllLogDTO> digestLogFile(Long equipId, File f) {
        if(!f.getPath().contains("AllLog")){
            return new ArrayList<>();
        }
        return digestLogFile(equipId, f, null);
    }

    /**
     * read file to Log records which logTime is greater(>) than parameter 'logTime'
     * @param f file
     * @param logTime logTime
     * @return Log records
     */
    public List<IVSAllLogDTO> digestLogFile(Long equipId, File f, LocalDateTime logTime) {
        if(!f.getPath().contains("AllLog")){
            return new ArrayList<>();
        }
        Charset charset= CharsetDetector.detect(f);
        InputStream inuptStream=FileUtil.getInputStream(f);
        String fileContent= IoUtil.read(inuptStream,charset);
        if (StrUtil.isEmpty(fileContent)) {
            return new ArrayList<>();
        }
        return doDigest(equipId, f.getPath(), fileContent, logTime);
    }



    private List<IVSAllLogDTO> doDigest(Long equipId, String filePath, String fileContent, LocalDateTime logTime) {
        Pattern pattern = Pattern.compile(REGEX_LOG_ROW);
        Matcher matcher = pattern.matcher(fileContent);
        List<IVSAllLogDTO> logDTOList = new ArrayList<>();
        while (matcher.find()) {
            try {
                IVSAllLogDTO logDTO = new IVSAllLogDTO();
                logDTO.setFilePath(filePath);
                String rawData = StrUtil.removeAllLineBreaks(matcher.group());
                if(!(rawData.contains("LEmpty") || rawData.contains("REmpty"))){
                    continue;
                }
                if(checkEmpty(rawData)){
                    continue;
                }
                if(rawData.contains("统计信息,LE")){
                    logDTO.setDescription("L");
                }else{
                    logDTO.setDescription("R");
                }
                logDTO.setRawData(rawData);
                logDTO.setEquipId(equipId);
                String[] columns = rawData.split(",");

                String time=columns[0];
                time=time.replaceAll("/","-");
                logDTO.setLogTime(LocalDateTimeUtil.parse(time, "yyyy-MM-dd HH:mm:ss"));

                if (logTime != null && logDTO.getLogTime().compareTo(logTime) < 0) {
                }else{
                    logDTOList.add(logDTO);
                }
            } catch (Exception ignore) {
                System.out.println(ignore);
            }
        }
        return logDTOList;
    }
    private boolean checkEmpty(String str){
        if(str.contains(emptyStr1) || str.contains(emptyStr2) || str.contains(LemptyStr) || str.contains(RemptyStr)){
            return true;
        }
        //过滤OK=0,其他不为0的情况
        if(str.contains("LOK=0") || str.contains("ROK=0")){
            return true;
        }
        return false;
    }
    public LocalDateTime findCorrectShiftByLog(IVSAllLogPo log,Integer type){
        LocalDateTime logTime=log.getLogTime();
        LocalDateTime firstStart=null;
        LocalDateTime firstEnd=null;
        LocalDateTime start=null;
        LocalDateTime end=null;
        if(logTime.getHour()>=12 && logTime.getHour()<=23){
            start=logTime.withHour(7).withMinute(0).withSecond(0);
            end=logTime.withHour(19).withMinute(0).withSecond(0);
        }else{
            start=logTime.minusDays(1).withHour(19).withMinute(0).withSecond(0);
            end=logTime.withHour(7).withMinute(0).withSecond(0);
        }
        firstStart=start;
        firstEnd=end;
        int recordNum=0;
        int count=0;
        while (recordNum<=0 && count<60){
            List<IVSLogPo> ivsLogPos=ivsLogService.lambdaQuery()
                    .ge(IVSLogPo::getLogTime,start)
                    .le(IVSLogPo::getLogTime,end).list();
            if(CollectionUtil.isEmpty(ivsLogPos)){
                start=start.minusHours(12);
                end=end.minusHours(12);
            }else{
                recordNum=ivsLogPos.size();
            }
            count++;
        }
        if(count<60){
            return type==1?start:end;
        }else{
            return type==1?firstStart:firstEnd;
        }
    }
    public Map<String, Map<String,Object>> getIvsAllDataByShift(){
        Map<String, Map<String,Object>> result=new HashMap<>();
        initData(result,"L-IVS-ALL");
        initData(result,"R-IVS-ALL");
        List<IVSAllLogPo> ivsLAllLogPos = this.lambdaQuery()
                .eq(IVSAllLogPo::getDescription,"L")
                .orderByAsc(IVSAllLogPo::getLogTime).list();
        List<IVSAllLogPo> ivsRAllLogPos = this.lambdaQuery()
                .eq(IVSAllLogPo::getDescription,"R")
                .orderByAsc(IVSAllLogPo::getLogTime).list();
        if(CollectionUtil.isNotEmpty(ivsLAllLogPos)){
            Set<String> xAxisData = (LinkedHashSet<String>) result.get("L-IVS-ALL").get("xAxis");
            for(IVSAllLogPo log:ivsLAllLogPos){
                //找到对应班次
                LocalDateTime shiftAxis=findCorrectShiftByLog(log,1);
                String xAxis=ConvertXaxisByTime(shiftAxis);
                if(xAxisData.add(xAxis)) {
                    Pattern pattern = Pattern.compile(REGEX_3);
                    Matcher matcher = pattern.matcher(log.getRawData());
                    fillData(matcher,result,"L-IVS-ALL",null);
                }
            }
        }
        if(CollectionUtil.isNotEmpty(ivsRAllLogPos)){
            Set<String> xAxisData = (LinkedHashSet<String>) result.get("R-IVS-ALL").get("xAxis");
            for(IVSAllLogPo log:ivsRAllLogPos){
                LocalDateTime shiftAxis=findCorrectShiftByLog(log,1);
                String xAxis=ConvertXaxisByTime(shiftAxis);
//                String xAxis=ConvertXaxis(log);
                if(xAxisData.add(xAxis)) {
                    Pattern pattern = Pattern.compile(REGEX_4);
                    Matcher matcher = pattern.matcher(log.getRawData());
                    fillData(matcher,result,"R-IVS-ALL",null);
                }
            }
        }
        return result;
    }
    public void initData(Map<String, Map<String,Object>> result,String key){
        Map<String,Object> temp=new HashMap<>();
        Set<String> xAxisData = new LinkedHashSet<String>();
        List<BigDecimal> p0 = new ArrayList<>();
        List<BigDecimal> p1 = new ArrayList<>();
        List<BigDecimal> p2 = new ArrayList<>();
        List<BigDecimal> p3 = new ArrayList<>();
        List<BigDecimal> p4 = new ArrayList<>();
        List<BigDecimal> p5 = new ArrayList<>();
        List<BigDecimal> p6 = new ArrayList<>();
        List<BigDecimal> p7 = new ArrayList<>();
        temp.put("xAxis",xAxisData);
        temp.put("t1",p0);
        temp.put("t2",p1);
        temp.put("t3",p2);
        temp.put("t4",p3);
        temp.put("t5",p4);
        temp.put("t6",p5);
        temp.put("t7",p6);
        temp.put("yield",p7);
        result.put(key,temp);
    }
    public Map<String,BigDecimal> fillTXdataIntoMap(BigDecimal... TXData){
        Map<String,BigDecimal> map=new HashMap<>();
        map.put("T1",TXData[0]);
        map.put("T2",TXData[1]);
        map.put("T3",TXData[2]);
        map.put("T4",TXData[3]);
        map.put("T5",TXData[4]);
        map.put("T6",TXData[5]);
        map.put("T7",TXData[6]);
        map.put("SUM",TXData[7]);
        return map;
    }
    public String ConvertXaxis(IVSAllLogPo log){
        LocalDateTime currentDate=log.getLogTime();
        int currentHour=currentDate.getHour();
        String currentTime=LocalDateTimeUtil.format(currentDate,"yyyy/MM/dd");
        if(currentHour>=12 && currentHour<=23){
            //早班
            currentTime=currentTime+"MS";
        }else{
            //晚班要减去12小时
            currentTime=LocalDateTimeUtil.format(currentDate.minusHours(12),"yyyy/MM/dd")+"ES";
        }
        return currentTime;
    }
    public String ConvertXaxisByTime(LocalDateTime time){
        int currentHour=time.getHour();
        String currentTime=LocalDateTimeUtil.format(time,"yyyy/MM/dd");
        if(currentHour==7){
            currentTime=currentTime+"MS";
        }else{
            currentTime=currentTime+"ES";
        }
        return currentTime;
    }
    public BigDecimal calcute(BigDecimal TX,BigDecimal SUM){
        return TX.divide(SUM,reservation, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100))
                .setScale(1,RoundingMode.HALF_UP);
    }
    public void fillData(Matcher matcher,Map<String, Map<String,Object>> result,String key,Map<String,BigDecimal> propertiesMap){
        List<BigDecimal> t1=(List<BigDecimal>)result.get(key).get("t1");
        List<BigDecimal> t2=(List<BigDecimal>)result.get(key).get("t2");
        List<BigDecimal> t3=(List<BigDecimal>)result.get(key).get("t3");
        List<BigDecimal> t4=(List<BigDecimal>)result.get(key).get("t4");
        List<BigDecimal> t5=(List<BigDecimal>)result.get(key).get("t5");
        List<BigDecimal> t6=(List<BigDecimal>)result.get(key).get("t6");
        List<BigDecimal> t7=(List<BigDecimal>)result.get(key).get("t7");
        List<BigDecimal> yield=(List<BigDecimal>)result.get(key).get("yield");
        if(propertiesMap==null){
            if (matcher.find()) {
                BigDecimal T1=new BigDecimal(matcher.group(2).toString());
                BigDecimal T2= new BigDecimal(matcher.group(3).toString());
                BigDecimal T3= new BigDecimal(matcher.group(4).toString());
                BigDecimal T4= new BigDecimal(matcher.group(5).toString());
                BigDecimal T5= new BigDecimal(matcher.group(6).toString());
                BigDecimal T6= new BigDecimal(matcher.group(7).toString());
                BigDecimal T7= new BigDecimal(matcher.group(8).toString());
                BigDecimal OK=new BigDecimal(matcher.group(10).toString());
                BigDecimal SUM=OK.add(T1).add(T2).add(T3).add(T4).add(T5).add(T6).add(T7);
                BigDecimal T1Percent=calcute(T1,SUM);
                BigDecimal T2Percent=calcute(T2,SUM);
                BigDecimal T3Percent=calcute(T3,SUM);
                BigDecimal T4Percent=calcute(T4,SUM);
                BigDecimal T5Percent=calcute(T5,SUM);
                BigDecimal T6Percent=calcute(T6,SUM);
                BigDecimal T7Percent=calcute(T7,SUM);
                t1.add(T1Percent);
                t2.add(T2Percent);
                t3.add(T3Percent);
                t4.add(T4Percent);
                t5.add(T5Percent);
                t6.add(T6Percent);
                t7.add(T7Percent);
                BigDecimal one=new BigDecimal(100).
                        subtract(T1Percent).
                        subtract(T2Percent).
                        subtract(T3Percent).
                        subtract(T4Percent).
                        subtract(T5Percent).
                        subtract(T6Percent).
                        subtract(T7Percent);
                yield.add(one);
            }else{
                BigDecimal zero=new BigDecimal(0);
                t1.add(zero);
                t2.add(zero);
                t3.add(zero);
                t4.add(zero);
                t5.add(zero);
                t6.add(zero);
                t7.add(zero);
                yield.add(zero);
            }
        }else{
            BigDecimal T1Percent=calcute(propertiesMap.get("T1"),propertiesMap.get("SUM"));
            BigDecimal T2Percent=calcute(propertiesMap.get("T2"),propertiesMap.get("SUM"));
            BigDecimal T3Percent=calcute(propertiesMap.get("T3"),propertiesMap.get("SUM"));
            BigDecimal T4Percent=calcute(propertiesMap.get("T4"),propertiesMap.get("SUM"));
            BigDecimal T5Percent=calcute(propertiesMap.get("T5"),propertiesMap.get("SUM"));
            BigDecimal T6Percent=calcute(propertiesMap.get("T6"),propertiesMap.get("SUM"));
            BigDecimal T7Percent=calcute(propertiesMap.get("T7"),propertiesMap.get("SUM"));
            t1.add(T1Percent);
            t2.add(T2Percent);
            t3.add(T3Percent);
            t4.add(T4Percent);
            t5.add(T5Percent);
            t6.add(T6Percent);
            t7.add(T7Percent);
            BigDecimal one=new BigDecimal(100).
                    subtract(T1Percent).
                    subtract(T2Percent).
                    subtract(T3Percent).
                    subtract(T4Percent).
                    subtract(T5Percent).
                    subtract(T6Percent).
                    subtract(T7Percent);
            yield.add(one);
        }


    }
    public LocalDateTime getNextMonthFirstAt19(LocalDateTime time){
        Month currentMonth=time.getMonth();
        Month nextMonth=currentMonth.plus(1);
        int year=time.getYear();
        if(currentMonth==Month.DECEMBER){
            year++;
        }
        LocalDateTime nextMonthFirst=LocalDateTime.of(year,nextMonth,1,0,0,0);
        return nextMonthFirst.withHour(19);
    }
    public Map<String, Map<String,Object>> getIvsAllDataByMonth(){
        Map<String, Map<String,Object>> result=new HashMap<>();
        initData(result,"L-IVS-ALL");
        initData(result,"R-IVS-ALL");
        List<IVSAllLogPo> ivsLAllLogPos = this.lambdaQuery()
                .eq(IVSAllLogPo::getDescription,"L")
                .orderByAsc(IVSAllLogPo::getId).list();

        List<IVSAllLogPo> ivsRAllLogPos = this.lambdaQuery()
                .eq(IVSAllLogPo::getDescription,"R")
                .orderByAsc(IVSAllLogPo::getId).list();

        if(CollectionUtil.isNotEmpty(ivsLAllLogPos)) {
            IVSAllLogPo startLog = ivsLAllLogPos.get(0);
            LocalDateTime monthTime=findCorrectShiftByLog(startLog,1);
            String currentTime=LocalDateTimeUtil.format(monthTime,"yyyy/MM")+"月";
            LocalDateTime nextMonthTime=getNextMonthFirstAt19(monthTime);
            boolean startFlag=true;
            BigDecimal LT1=new BigDecimal(0);
            BigDecimal LT2=new BigDecimal(0);
            BigDecimal LT3=new BigDecimal(0);
            BigDecimal LT4=new BigDecimal(0);
            BigDecimal LT5=new BigDecimal(0);
            BigDecimal LT6=new BigDecimal(0);
            BigDecimal LT7=new BigDecimal(0);
            BigDecimal LOK=new BigDecimal(0);
            BigDecimal LSUM=new BigDecimal(0);
            Set<String> xAxisData = (LinkedHashSet<String>)  result.get("L-IVS-ALL").get("xAxis");
            for(int i=0;i<ivsLAllLogPos.size();i++){
                LocalDateTime nextTime=null;
                IVSAllLogPo log=ivsLAllLogPos.get(i);
                if(i+1<ivsLAllLogPos.size()){
                    //下一个log的日期
//                    nextTime=ivsLAllLogPos.get(i+1).getLogTime();
                    nextTime=findCorrectShiftByLog(ivsLAllLogPos.get(i+1),2);
                }
                if(startFlag){
                    //新的月份最开始第一个log
                    xAxisData.add(currentTime);
                }
                Pattern pattern = Pattern.compile(REGEX_3);
                Matcher matcher = pattern.matcher(log.getRawData());
                if (matcher.find()) {
                     LT1=LT1.add(new BigDecimal(matcher.group(2).toString()));
                     LT2= LT2.add(new BigDecimal(matcher.group(3).toString()));
                     LT3= LT3.add(new BigDecimal(matcher.group(4).toString()));
                     LT4= LT4.add(new BigDecimal(matcher.group(5).toString()));
                     LT5= LT5.add(new BigDecimal(matcher.group(6).toString()));
                     LT6= LT6.add(new BigDecimal(matcher.group(7).toString()));
                     LT7= LT7.add(new BigDecimal(matcher.group(8).toString()));
                     LOK=LOK.add(new BigDecimal(matcher.group(10).toString()));
                     LSUM=LOK.add(LT1).add(LT2).add(LT3).add(LT4).add(LT5).add(LT6).add(LT7);
                }
                if(nextTime==null){
                    //列表最后一个，累加计算
                    Map<String,BigDecimal> propertiesMap=fillTXdataIntoMap(LT1,LT2,LT3,LT4,LT5,LT6,LT7,LSUM);
                    fillData(null,result,"L-IVS-ALL",propertiesMap);
                }else{
                    if(nextTime.compareTo(nextMonthTime)>=0){
                        //如果下一个log的time大于上次记录的下个月结束时间，则生成一个新的下个月结束时间和x轴当前月份
                        currentTime=LocalDateTimeUtil.format(nextTime,"yyyy/MM")+"月"; //x轴当前月份
                        nextMonthTime=getNextMonthFirstAt19(nextTime); //新的下个月结束时间
                        startFlag=true;
                        //开始把累加计算值塞入列表
                        Map<String,BigDecimal> propertiesMap=fillTXdataIntoMap(LT1,LT2,LT3,LT4,LT5,LT6,LT7,LSUM);
                        fillData(null,result,"L-IVS-ALL",propertiesMap);
                        //累加计算完后置空
                         LT1=new BigDecimal(0);
                         LT2=new BigDecimal(0);
                         LT3=new BigDecimal(0);
                         LT4=new BigDecimal(0);
                         LT5=new BigDecimal(0);
                         LT6=new BigDecimal(0);
                         LT7=new BigDecimal(0);
                         LOK=new BigDecimal(0);
                         LSUM=new BigDecimal(0);
                    }else{
                        //若下个log还在当前月份范围内，则x轴的时间列表不塞入新的值，
                        startFlag=false;
                    }
                }
            }

        }
        if(CollectionUtil.isNotEmpty(ivsRAllLogPos)) {
            IVSAllLogPo startLog = ivsRAllLogPos.get(0);
            LocalDateTime monthTime=findCorrectShiftByLog(startLog,1);
            String currentTime=LocalDateTimeUtil.format(monthTime,"yyyy/MM")+"月";
            LocalDateTime nextMonthTime=getNextMonthFirstAt19(monthTime.minusHours(12));
            boolean startFlag=true;
            BigDecimal RT1=new BigDecimal(0);
            BigDecimal RT2=new BigDecimal(0);
            BigDecimal RT3=new BigDecimal(0);
            BigDecimal RT4=new BigDecimal(0);
            BigDecimal RT5=new BigDecimal(0);
            BigDecimal RT6=new BigDecimal(0);
            BigDecimal RT7=new BigDecimal(0);
            BigDecimal ROK=new BigDecimal(0);
            BigDecimal RSUM=new BigDecimal(0);
            Set<String> xAxisData = (LinkedHashSet<String>)  result.get("R-IVS-ALL").get("xAxis");
            for(int i=0;i<ivsRAllLogPos.size();i++){
                LocalDateTime nextTime=null;
                IVSAllLogPo log=ivsRAllLogPos.get(i);
                if(i+1<ivsRAllLogPos.size()){
                    //下一个log的日期
//                    nextTime=ivsRAllLogPos.get(i+1).getLogTime();
                    nextTime=findCorrectShiftByLog(ivsLAllLogPos.get(i+1),2);

                }
                if(startFlag){
                    //新的月份最开始第一个log
                    xAxisData.add(currentTime);
                }
                Pattern pattern = Pattern.compile(REGEX_4);
                Matcher matcher = pattern.matcher(log.getRawData());
                if (matcher.find()) {
                    RT1=RT1.add(new BigDecimal(matcher.group(2).toString()));
                    RT2= RT2.add(new BigDecimal(matcher.group(3).toString()));
                    RT3= RT3.add(new BigDecimal(matcher.group(4).toString()));
                    RT4= RT4.add(new BigDecimal(matcher.group(5).toString()));
                    RT5= RT5.add(new BigDecimal(matcher.group(6).toString()));
                    RT6= RT6.add(new BigDecimal(matcher.group(7).toString()));
                    RT7= RT7.add(new BigDecimal(matcher.group(8).toString()));
                    ROK=ROK.add(new BigDecimal(matcher.group(10).toString()));
                    RSUM=ROK.add(RT1).add(RT2).add(RT3).add(RT4).add(RT5).add(RT6).add(RT7);
                }
                if(nextTime==null){
                    //列表最后一个，累加计算
                    Map<String,BigDecimal> propertiesMap=fillTXdataIntoMap(RT1,RT2,RT3,RT4,RT5,RT6,RT7,RSUM);
                    fillData(null,result,"R-IVS-ALL",propertiesMap);
                }else{
                    if(nextTime.compareTo(nextMonthTime)>=0){
                        //如果下一个log的time大于上次记录的下个月结束时间，则生成一个新的下个月结束时间和x轴当前月份
                        currentTime=LocalDateTimeUtil.format(nextTime,"yyyy/MM")+"月"; //x轴当前月份
                        nextMonthTime=getNextMonthFirstAt19(nextTime); //新的下个月结束时间
                        startFlag=true;
                        //开始把累加计算值塞入列表
                        Map<String,BigDecimal> propertiesMap=fillTXdataIntoMap(RT1,RT2,RT3,RT4,RT5,RT6,RT7,RSUM);
                        fillData(null,result,"R-IVS-ALL",propertiesMap);
                        //累加计算完后置空
                        RT1=new BigDecimal(0);
                        RT2=new BigDecimal(0);
                        RT3=new BigDecimal(0);
                        RT4=new BigDecimal(0);
                        RT5=new BigDecimal(0);
                        RT6=new BigDecimal(0);
                        RT7=new BigDecimal(0);
                        ROK=new BigDecimal(0);
                        RSUM=new BigDecimal(0);
                    }else{
                        //若下个log还在当前月份范围内，则x轴的时间列表不塞入新的值，
                        startFlag=false;
                    }
                }

            }
        }
        return result;
    }



    // 正则模式：解析字段对
    private static final Pattern FIELD_PATTERN = Pattern.compile("([LR]\\w+)=(\\d+)");


    /**
     * 解析日志数据并根据左右分类。
     */
    public Map<String, Object> digestRowData(String type) {
        //数据库获取所有数据
        List<IVSAllLogPo> rawData = baseMapper.selectList(new QueryWrapper<IVSAllLogPo>().orderByAsc("log_time"));
        if (rawData.isEmpty()) {
            return Collections.emptyMap();
        }
        List<IVSAllLogDataDto> leftLogs = new ArrayList<>();
        List<IVSAllLogDataDto> rightLogs = new ArrayList<>();

        for (IVSAllLogPo logDTO : rawData) {
            String description = logDTO.getDescription(); // L or R
            IVSAllLogDataDto logData = parseFields(logDTO.getRawData());
            // 分类到左右日志列表
            if ("L".equals(description)) {
                leftLogs.add(logData);
            }
            if ("R".equals(description)) {
                rightLogs.add(logData);
            }

        }

        //统计数据
        Map<String,List<String>> leftData=getCalData(leftLogs, type);
        Map<String,List<String>> rightData=getCalData(rightLogs, type);

        Map<String,Object> result=new HashMap<>();
        result.put("L-IVS-ALL",leftData);
        result.put("R-IVS-ALL",rightData);
        return result;
    }

    /**
     * 解析单条日志记录中的字段。
     *
     * @return 填充后的 IVSAllLogDataDto 实例
     */
    private IVSAllLogDataDto parseFields(String rowData) {
        IVSAllLogDataDto dto = new IVSAllLogDataDto();
        dto.setLogTime(LocalDateTimeUtil.parse(rowData.split(",")[0],"yyyy/MM/dd HH:mm:ss"));
        Matcher fieldMatcher = FIELD_PATTERN.matcher(rowData);
        while (fieldMatcher.find()) {
            String key = fieldMatcher.group(1);
            int value = Integer.parseInt(fieldMatcher.group(2));
            switch (key) {
                case "LEmpty":
                case "REmpty":
                    dto.setEmpty(value);
                    break;
                case "LT1":
                case "RT1":
                    dto.setT1(value);
                    break;
                case "LT2":
                case "RT2":
                    dto.setT2(value);
                    break;
                case "LT3":
                case "RT3":
                    dto.setT3(value);
                    break;
                case "LT4":
                case "RT4":
                    dto.setT4(value);
                    break;
                case "LT5":
                case "RT5":
                    dto.setT5(value);
                    break;
                case "LT6":
                case "RT6":
                    dto.setT6(value);
                    break;
                case "LT7":
                case "RT7":
                    dto.setT7(value);
                    break;
                case "LTOther":
                case "RTOther":
                    dto.setTOther(value);
                    break;
                case "LOK":
                case "ROK":
                    dto.setOk(value);
                    break;
                default:
                    // 忽略未知字段
                    break;
            }
        }

        return dto;
    }

    private void dealDay(List<IVSAllLogDataDto> data){
        LocalDateTime startTime;
        LocalDateTime endTime;
        // 遍历数据
        for (IVSAllLogDataDto dto : data) {
            // 获取日志时间
            LocalDateTime logTime = dto.getLogTime();

            if (logTime.getHour() >= 0 && logTime.getHour() < 12) {
                // 晚班时间范围：昨天19:00到今天7:00
                startTime = logTime.minusDays(1).withHour(19).withMinute(0).withSecond(0);
                endTime = logTime.withHour(7).withMinute(0).withSecond(0);

            } else {
                // 早班时间范围：当天7:00到当天19:00
                startTime = logTime.withHour(7).withMinute(0).withSecond(0);
                endTime = logTime.withHour(19).withMinute(0).withSecond(0);
            }
            int count = 0;
            IVSLogPo ivsLogPo = null;
            LocalDateTime firstStartTime = startTime;
            while (ivsLogPo == null && count++ < 60) {
                //查询是否有日志
                ivsLogPo = ivsLogService.lambdaQuery().ge(IVSLogPo::getLogTime, startTime).le(IVSLogPo::getLogTime, endTime).last("limit 1").one();
                if (ivsLogPo != null) {
                    // 设置当天的日志时间，保证为当天，方便分组
                    if (ivsLogPo.getLogTime().getHour() >= 19 || ivsLogPo.getLogTime().getHour() < 7) {
                        dto.setLogTime(startTime);
                    } else {
                        dto.setLogTime(endTime);
                    }
                }
                startTime = startTime.minusHours(12);
                endTime = endTime.minusHours(12);
            }
            if (ivsLogPo == null && count >= 60) {
                dto.setLogTime(firstStartTime);
            }
        }
    }
    /**
     * 根据类型返回前端所需数据
     * @param data
     * @return
     */
    private Map<String,List<String>> getCalData(List<IVSAllLogDataDto> data, String type){
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> listMap = initializeTemperatureData();
        //遍历数据，同一天的为一组一起统计
        Map<LocalDate, List<IVSAllLogDataDto>> groupedData = new LinkedHashMap<>();

        //处理日期，过滤没有生产的日期
        dealDay(data);
        if ("2".equals(type)) {
            //按天分组统计
            groupedData = data.stream()
                    .collect(Collectors.groupingBy(dto -> dto.getLogTime().toLocalDate(), TreeMap::new, Collectors.toList()));
        }

        if ("3".equals(type)) {
            //按新科周分组统计（星期日到下星期六为七天一周）
            groupedData = data.stream()
                    .collect(Collectors.groupingBy(
                            dto -> {
                                LocalDate adjustedDate = dto.getLogTime().toLocalDate();
                                WeekFields weekFields = WeekFields.of(DayOfWeek.SUNDAY, 1); // 获取新科周的第一天
                                return adjustedDate.with(weekFields.dayOfWeek(), 1);// 设置星期天为每周的第一天                                return adjustedDate.with(weekFields.dayOfWeek(), 1); // 以周为单位分组
                            },
                            TreeMap::new, // 确保结果按周顺序排列
                            Collectors.toList()
                    ));
        }

        groupedData.forEach((time, logDatas) -> {
            IVSAllLogDataDto ivsAllLogDataDto = logDatas.get(0);
            for (int i = 1; i < logDatas.size(); i++) {
                ivsAllLogDataDto.addLog(logDatas.get(i));
            }
            // 获取每个值的比例
            String t1Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT1());
            String t2Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT2());
            String t3Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT3());
            String t4Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT4());
            String t5Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT5());
            String t6Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT6());
            String t7Rate = ivsAllLogDataDto.getT1Rate(ivsAllLogDataDto.getT7());

            // 计算总和
            BigDecimal totalRate = new BigDecimal(t1Rate)
                    .add(new BigDecimal(t2Rate))
                    .add(new BigDecimal(t3Rate))
                    .add(new BigDecimal(t4Rate))
                    .add(new BigDecimal(t5Rate))
                    .add(new BigDecimal(t6Rate))
                    .add(new BigDecimal(t7Rate));

            // 计算 yield
            BigDecimal yield = BigDecimal.valueOf(100).subtract(totalRate);

            // 添加到 listMap 中
            listMap.get("t1").add(t1Rate);
            listMap.get("t2").add(t2Rate);
            listMap.get("t3").add(t3Rate);
            listMap.get("t4").add(t4Rate);
            listMap.get("t5").add(t5Rate);
            listMap.get("t6").add(t6Rate);
            listMap.get("t7").add(t7Rate);
            listMap.get("yield").add(yield.toPlainString());
            listMap.get("xAxis").add(LocalDateTimeUtil.format(time, "yyyy/MM/dd"));

        });
        return listMap;
    }


    //初始化前端数据结构
    private Map<String, List<String>> initializeTemperatureData() {
        Map<String, List<String>> temperatureData = new LinkedHashMap<>();
        temperatureData.put("t1", new ArrayList<>());
        temperatureData.put("t2", new ArrayList<>());
        temperatureData.put("t3", new ArrayList<>());
        temperatureData.put("t4", new ArrayList<>());
        temperatureData.put("t5", new ArrayList<>());
        temperatureData.put("t6", new ArrayList<>());
        temperatureData.put("t7", new ArrayList<>());
        temperatureData.put("yield", new ArrayList<>());
        temperatureData.put("xAxis", new ArrayList<>());
        return temperatureData;
    }
}
