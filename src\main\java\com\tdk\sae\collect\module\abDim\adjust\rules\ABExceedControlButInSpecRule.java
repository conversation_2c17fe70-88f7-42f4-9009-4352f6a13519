package com.tdk.sae.collect.module.abDim.adjust.rules;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.abDim.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.abDim.adjust.utils.ReachFullDropHalfList;
import com.tdk.sae.collect.module.abDim.model.ABSpecInfo;
import com.tdk.sae.collect.module.abDim.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDTO;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDetailDTO;
import com.tdk.sae.collect.module.abDim.model.dto.ABLogTrayMeanDTO;
import com.tdk.sae.collect.module.abDim.service.ABAutoAdjustService;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000DataService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamFormulaService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component("ABExceedControlButInSpecRule")
public class ABExceedControlButInSpecRule extends AbstractRulesHandler {

    private final KV8000ParamService kv8000ParamService;

    private final KV8000DataService kv8000DataService;

    private final KV8000ParamFormulaService kv8000ParamFormulaService;

    private static final String REGEX = "连续([\\s\\S]*?)点超出mean自动调整触发线但不超mean范围线，以第([\\s\\S]*?)点与目标值的差值作为调整量进行自动调整";

    private static final String[] AB_KV_ADDRESS_REPRESENTS = new String[]{"W_A1", "W_B1", "R_A1", "R_B1"};
    private final ABSpecInfo specInfo;
    @Override
    public void checkKV8000ParamCache() {
        List<KV8000ParamPO> paramList = kv8000ParamService.getCacheByEquipId(getEquipment().getId());
        for (String kvAddressRepresent : AB_KV_ADDRESS_REPRESENTS) {
            if (KV8000ParamCache.get(getEquipment().getId()).get(kvAddressRepresent) != null) {
                continue;
            }
            List<KV8000ParamPO> filteredList = paramList.stream().filter(p -> p.getAddressRepresent().equals(kvAddressRepresent)).collect(Collectors.toList());
            if (!filteredList.isEmpty()) {
                KV8000ParamCache.get(getEquipment().getId()).put(kvAddressRepresent, filteredList.get(0));
            }
        }
    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("ExceedControlButInSpecRule");
        if (isDisabled()) {
            return doNextRule(advice);
        }
        String rule = getRulePopulate().getRule();
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(rule);
        if (!matcher.find()) {
            // TODO: log detail
            return doNextRule(advice);
        }
        String continueOverControlLimitStr = matcher.group(1);
        String continueNPointRefStr = matcher.group(2);

        int continueOverControlLimit = Integer.parseInt(continueOverControlLimitStr);
        int continueNPointRef = Integer.parseInt(continueNPointRefStr);
        LocalDateTime nowTIme=LocalDateTime.now();

        // 只有mean表里面有的parameter, 不存在GT
        Map<String, ReachFullDropHalfList<ABLogTrayMeanDTO>> paramDataMap = ABAutoAdjustService.DATA_CACHE.get(getEquipment().getId());
        Map<String, List<LocalDateTime>> adjustDataMap = ABAutoAdjustService.ADJUST_CACHE.get(getEquipment().getId());
        Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(getEquipment().getId());

        paramDataMap.forEach((parameter, parameterDataList) -> {
            List<LocalDateTime> adjustTimeList=adjustDataMap.get(parameter);
            List<ABLogTrayMeanDTO> meanList = parameterDataList.getList();
            BigDecimal specUpperLimit = specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
            BigDecimal specLowerLimit = specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
            BigDecimal controlUpperLimit = specMap.get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
            BigDecimal controlLowerLimit = specMap.get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
            int rmIdx = -1;
            int continueCount = 0;
            boolean isAboveControlUpperLimit = false;
            boolean isBelowControlLowerLimit = false;
            for (int i = 0; i < meanList.size(); i++) {
                ABLogTrayMeanDTO mean = meanList.get(i);
                long millisDiff = ChronoUnit.SECONDS.between(mean.getEndTime(), nowTIme);
                if(millisDiff>300){
                    continueCount = 0;
                    adjustTimeList.clear();
                }else{
                    boolean aboveControlUpperLimit = mean.getMeanValue().compareTo(controlUpperLimit) > 0;
                    boolean belowControlLowerLimit = mean.getMeanValue().compareTo(controlLowerLimit) < 0;

                    if (mean.getMeanValue().compareTo(specLowerLimit) >= 0
                            && mean.getMeanValue().compareTo(specUpperLimit) <= 0
                            && (aboveControlUpperLimit || belowControlLowerLimit)) {
                        if (continueCount == 0) {
                            isAboveControlUpperLimit = aboveControlUpperLimit;
                            isBelowControlLowerLimit = belowControlLowerLimit;
                            adjustTimeList.clear();
                            adjustTimeList.add(mean.getEndTime());
                        }

                        if ((isAboveControlUpperLimit && aboveControlUpperLimit) || (isBelowControlLowerLimit && belowControlLowerLimit)) {
                            if (++continueCount == continueOverControlLimit) {
                                rmIdx = i - continueOverControlLimit + continueNPointRef;
                                adjustTimeList.add(mean.getEndTime());
                                break;
                            }
                        } else {
                            isAboveControlUpperLimit = aboveControlUpperLimit;
                            isBelowControlLowerLimit = belowControlLowerLimit;
                            continueCount = 1;
                            adjustTimeList.clear();
                            adjustTimeList.add(mean.getEndTime());
                        }
                    } else {
                        continueCount = 0;
                        adjustTimeList.clear();
                    }
                }
            }
            boolean isExceedFiftySecond=true;
            if(adjustTimeList.size()==2){
                long millisDiff = ChronoUnit.SECONDS.between(adjustTimeList.get(0), adjustTimeList.get(1));
                if(millisDiff<=50){
                    isExceedFiftySecond=false;
                    parameterDataList.removeBeforeIdxIncluded(rmIdx);
                }
            }
            if (rmIdx >= 0 && isExceedFiftySecond) {
                adjustTimeList.clear();
                ABLogTrayMeanDTO adjustRefMean = parameterDataList.getList().get(rmIdx);
                ABLogTrayMeanDTO lastMean = parameterDataList.getList().get(rmIdx-1);
                BigDecimal MeanValue=adjustRefMean.getMeanValue().add(lastMean.getMeanValue());
                BigDecimal result = MeanValue.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);

                AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice = advice.newRuleAdvice("ABExceedControlButInSpecRule");
                ruleAdvice.setEquipment(getEquipment());

                Map<String, KV8000ParamPO> paramMap = KV8000ParamCache.get(getEquipment().getId());
                KV8000ParamPO wParam = null;
                KV8000ParamPO rParam = null;
                switch (parameter) {
                    case "A1":
                        wParam = paramMap.get(AB_KV_ADDRESS_REPRESENTS[0]);
                        rParam = paramMap.get(AB_KV_ADDRESS_REPRESENTS[2]);
                        break;
                    case "B1":
                        wParam = paramMap.get(AB_KV_ADDRESS_REPRESENTS[1]);
                        rParam = paramMap.get(AB_KV_ADDRESS_REPRESENTS[3]);
                        break;

                }
                if (wParam != null && rParam != null) {
                    KV8000DataPO kv8000Data = kv8000DataService.lambdaQuery()
                            .eq(KV8000DataPO::getParamId, rParam.getId()).orderByDesc(KV8000DataPO::getReadTime).last("limit 1").one();
                    if (kv8000Data == null) {
                        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> advices = advice.getRuleAdvices().get("ABExceedControlButInSpecRule");
                        advices.remove(ruleAdvice);
                        return;
                    }

//                    // TODO 调试用,暂时只修改GT
//                    List<String> banned = Arrays.asList("W_EX1", "W_EX2", "W_EY1", "W_EY2");
//                    if (banned.contains(wParam.getAddressRepresent())) {
//                        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> advices = advice.getRuleAdvices().get("ExceedControlButInSpecRule");
//                        advices.remove(ruleAdvice);
//                        return;
//                    }

                    // 设置用于获取公式的paramId, parameter
                    ruleAdvice.setKv8000Param(BeanUtil.copyProperties(wParam, KV8000ParamDTO.class));
                    ruleAdvice.setAddressRepresent(wParam.getAddressRepresent());

                    // 设置最新的plc值
                    BigDecimal readData = new BigDecimal(kv8000Data.getReadData());
                    ruleAdvice.setLatestReadData(readData);

                    // 先按1:1的比例计算自动设置的值
                    BigDecimal diffValue = ABSpecInfo.getSpec().get(parameter).get("TARGET").subtract(result);
                    ruleAdvice.setDiffValue(diffValue);
                    BigDecimal writeData = readData.add(diffValue);
                    ruleAdvice.setNewValueStr(writeData.toPlainString());
                    ruleAdvice.setNewValue(writeData);

                    // 如果有公式应用, 则公式计算的结果会覆盖上面计算的值
                    kv8000ParamFormulaService.calculateABRuleAdvice(ruleAdvice,adjustRefMean.getHead());

                    ABAdjustLogDTO log = advice.getAdjustLog();
                    log.setAdjustType(getRulePopulate().getRuleType());

                    ABAdjustLogDetailDTO detail = log.newDetail();
                    detail.setApplyId(getRulePopulate().getApplyId());
                    detail.setParamId(wParam.getId());
                    detail.setMeanId(adjustRefMean.getId());
                    detail.setLogLevel(1);
                    detail.setLogMsg(StrUtil.format("{} Follow rule: {}. From: ({}) To: ({})",
                            getEquipment().getEquipCode(),
                            getRulePopulate().getRule(),
                            ruleAdvice.getLatestReadData().toPlainString(),
                            ruleAdvice.getNewValueStr()));
                    detail.setFromValue(ruleAdvice.getLatestReadData().toPlainString());
                    detail.setToValue(ruleAdvice.getNewValueStr());
                    detail.setWriteTime(LocalDateTime.now());
                }
                parameterDataList.removeBeforeIdxIncluded(rmIdx);
            }
        });
//        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> ruleAdvices = advice.getRuleAdvices().get("ExceedSpecThenStopRule");
//        if (ruleAdvices != null) {
//            advice.getAdjustLog().setAdjusted(1);
//            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
//            return advice;
//        }
        return doNextRule(advice);
    }

}
