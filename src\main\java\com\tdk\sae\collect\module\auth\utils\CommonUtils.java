package com.tdk.sae.collect.module.auth.utils;

import cn.hutool.core.util.StrUtil;

import javax.servlet.http.HttpServletRequest;

public class CommonUtils {

    public static String getAccessToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        // 校验是否满足前缀
        if (StrUtil.isNotBlank(token) && token.startsWith("Bearer ")) {
            token = token.replace("Bearer ", "");
        }
        return token;
    }

}
