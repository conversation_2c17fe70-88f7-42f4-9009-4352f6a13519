package com.tdk.sae.collect.module.ivs.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSConfPO;
import com.tdk.sae.collect.module.ivs.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("/client/ivs")
public class IvsDataController {
    private final IVSLogService ivsLogService;
    private final IVSPadTxtService ivsPadTxtService;
    private final IVSTxtService ivsTxtService;
    private final IVSAllLogService ivsAllLogService;
    private final IVSConfService ivsConfService;
    @GetMapping("/info")
    public Response<String> getICSInfo() {
        List<EquipmentDTO> icsLogEquips = SystemInfoService.getEquipmentMap().get(EquipmentTypeEnum.IVS_LOG.getName());
        if (CollectionUtil.isNotEmpty(icsLogEquips)) {
            return Response.ok();
        }
        return Response.ok("empty","empty");
    }

    //获取pad数据
    @GetMapping("/padData")
    public Response<Map<String, Object>> padData(String start_time,String end_time) {
        Map<String, Object> result=ivsPadTxtService.getPadData(start_time,end_time);
        return Response.ok(result);
    }
    //获取ivs数据
    @GetMapping("/ivsData")
    public Response<Map<String, Object>> ivsData(String start_time,String end_time) {
        Map<String, Object> result=ivsTxtService.getIvsData(start_time,end_time);
        return Response.ok(result);
    }

    @GetMapping("/allData")
    public Response<Object> allData(String type) {
        if ("1".equals(type)) {
            return Response.ok(ivsAllLogService.getIvsAllDataByShift());
        }else if ("4".equals(type)) {
            return Response.ok(ivsAllLogService.getIvsAllDataByMonth());
        }else {
            return Response.ok(ivsAllLogService.digestRowData(type));
        }
    }

    @GetMapping("/getIVSAlarmPercent")
    public Response<Map<String, Object>> getIVSAlarmPercent() {
        Map<String, Object> result=new HashMap<>();
        List<IVSConfPO> ConfList=ivsConfService.list();
        ConfList.forEach(conf->{
            result.put(conf.getConfName(),conf.getConfValue());
        });
        return Response.ok(result);
    }
}
