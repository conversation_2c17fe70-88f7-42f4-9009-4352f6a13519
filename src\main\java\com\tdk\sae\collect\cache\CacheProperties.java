package com.tdk.sae.collect.cache;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@EqualsAndHashCode
@ToString
@Configuration
@ConfigurationProperties(prefix = "hds.cache")
public class CacheProperties {

    private String prefix = "hds-admin";

}
