package com.tdk.sae.collect.module.ics.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ics_adjust_log")
public class ICSAdjustLogPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "equip_id")
    private Long equipId;

    @TableField(value = "start_time")
    private LocalDateTime startTime;

    @TableField(value = "end_time")
    private LocalDateTime endTime;

    @TableField(value = "adjusted")
    private Integer adjusted;

    @TableField(value = "adjust_type")
    private Integer adjustType;

    @TableField(value = "adjust_by")
    private Long adjustBy;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "synced")
    private Integer synced = 0;

}
