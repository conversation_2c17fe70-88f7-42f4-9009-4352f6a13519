package com.tdk.sae.collect.aop.annotation;

import com.tdk.sae.collect.aop.aspect.FieldInfoAspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 属性字段
 * @see FieldInfo
 * @see FieldInfoAspect
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(value = ElementType.TYPE)
public @interface FieldClass {

    /**
     * 需要排除的表字段
     *
     * @return 需要排除的表字段
     */
    String[] exclude() default "";
}
