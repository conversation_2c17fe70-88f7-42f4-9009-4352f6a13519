<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 sae Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdk.sae.collect.module.ics.mapper.ICSAdjustLogDetailMapper">

    <select id="getAdjustLogDetailsAscByDate" resultType="com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO">
        select
            l1.log_msg, l1.write_time
        from t_biz_ics_adjust_log_detail l1
        left join t_biz_ics_adjust_log l2 on l1.log_id = l2.id
        where
            l2.equip_id in
            <foreach item="item" collection="eIds" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
            and
            <foreach item="item" collection="times" separator="or" open="(" close=")" index="index">
                l1.write_time &gt;= #{item} and l1.write_time &lt; DATE_ADD(#{item}, INTERVAL 1 DAY)
            </foreach>
        order by l1.write_time asc
    </select>

    <select id="getAdjustLogDetailsAscByTime" resultType="com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO">
        select
            l1.log_msg, l1.write_time
        from t_biz_ics_adjust_log_detail l1
        left join t_biz_ics_adjust_log l2 on l1.log_id = l2.id
        where
            l2.equip_id in
            <foreach item="item" collection="eIds" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
            and
            l1.write_time &gt; #{lastTime}
        order by l1.write_time asc
    </select>

</mapper>
