package com.tdk.sae.collect.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerMappingIntrospector;
import org.springframework.web.servlet.resource.ResourceResolver;
import org.springframework.web.servlet.resource.ResourceResolverChain;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Configuration
public class SpaConfiguration implements WebMvcConfigurer {

    @Autowired
    private HandlerMappingIntrospector handlerMappingIntrospector; // This will be used to check for handlers.

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .resourceChain(true)
                .addResolver(new SpaResourceResolver(handlerMappingIntrospector));
    }

    private static class SpaResourceResolver implements ResourceResolver {
        private final HandlerMappingIntrospector handlerMappingIntrospector;

        public SpaResourceResolver(HandlerMappingIntrospector handlerMappingIntrospector) {
            this.handlerMappingIntrospector = handlerMappingIntrospector;
        }

        @Override
        public Resource resolveResource(HttpServletRequest request, @NotNull String requestPath,
                                        @NotNull List<? extends Resource> locations, @NotNull ResourceResolverChain chain) {
            try {
                if (requestPath.endsWith(".js") || requestPath.endsWith(".css") || requestPath.endsWith(".png") || requestPath.endsWith(".ico")// Add other file extensions as needed.
                ) {
                    // Attempt to resolve the resource as is.
                    return chain.resolveResource(request, requestPath, locations);
                }

                // Check if there is a handler (controller method) for this path.
                if (this.handlerMappingIntrospector.getMatchableHandlerMapping(request) != null) {
                    // If a handler is found, this is an API route, so no need to resolve to index.html.
                    return null;
                }
            } catch (Exception e) {
                // No handler found or an error occurred - assume it's a frontend route.
            }

            // No handler found - this is likely a frontend route, so serve index.html.
            return chain.resolveResource(request, "index.html", locations);
        }

        @Override
        public String resolveUrlPath(@NotNull String resourcePath, @NotNull List<? extends Resource> locations, ResourceResolverChain chain) {
            // This method can remain unchanged as it doesn't affect the routing logic.
            return chain.resolveUrlPath(resourcePath, locations);
        }
    }
}
