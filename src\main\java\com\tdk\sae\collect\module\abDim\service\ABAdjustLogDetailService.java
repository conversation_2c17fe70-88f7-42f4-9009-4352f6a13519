package com.tdk.sae.collect.module.abDim.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.abDim.mapper.ABAdjustLogDetailMapper;
import com.tdk.sae.collect.module.abDim.model.dto.ABAdjustLogDetailDTO;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogDetailPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
@Service
public class ABAdjustLogDetailService extends ServiceImpl<ABAdjustLogDetailMapper, ABAdjustLogDetailPO> {

    private final ABAdjustLogDetailMapper icsAdjustLogDetailMapper;

    @Transactional
    public void saveDetails(List<ABAdjustLogDetailDTO> logDetails) {
        if (CollectionUtil.isEmpty(logDetails)) {
            return;
        }
        List<ABAdjustLogDetailPO> logDetailPOs = BeanUtil.copyToList(logDetails, ABAdjustLogDetailPO.class);
        this.saveBatch(logDetailPOs);
    }

    public List<ABAdjustLogDetailPO> getAdjustLogDetailsAscByTime(Set<String> eIds, LocalDateTime lastTime) {
        return icsAdjustLogDetailMapper.getAdjustLogDetailsAscByTime(eIds, lastTime);
    }

    public List<ABAdjustLogDetailPO> getAdjustLogDetailsAscByDate(Set<String> eIds, List<LocalDateTime> selectedDateTimes) {
        return icsAdjustLogDetailMapper.getAdjustLogDetailsAscByDate(eIds, selectedDateTimes);
    }

}
