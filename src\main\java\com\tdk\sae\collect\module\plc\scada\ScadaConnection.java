package com.tdk.sae.collect.module.plc.scada;

import com.scada.dbcomm.DBComm;

public class ScadaConnection {

    protected static DBComm dbComm = null;

    protected static Long initial = -1L;

    public ScadaConnection(String ip, Integer port) {
        if (dbComm == null) {
            DBComm comm = new DBComm();
            long initial = comm.Initial();
            boolean ConnectRemoteServer = comm.ConnectRemoteServer(initial, ip, port, "", false);
            if(ConnectRemoteServer)
            {
                System.out.println("连接SCADA数据库成功");
            }
            else
            {
                System.out.println("连接SCADA数据库失败");
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ignore) {}
            boolean IsConnected = comm.IsConnected(initial);
            if (!IsConnected) {
                throw new RuntimeException("Connect SCADA failed!");
            }
            ScadaConnection.dbComm = comm;
            ScadaConnection.initial = initial;
        }
    }

    public static boolean isConnected() {
        return dbComm.IsConnected(initial);
    }

    public static void disconnect() {
        dbComm.DisConnect(initial);
    }

}
