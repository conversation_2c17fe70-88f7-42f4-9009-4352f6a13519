package com.tdk.sae.collect.module.abDim.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.abDim.model.po.ABAdjustLogDetailPO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogDetailPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Mapper
public interface ABAdjustLogDetailMapper extends BaseMapper<ABAdjustLogDetailPO> {

    List<ABAdjustLogDetailPO> getAdjustLogDetailsAscByTime(@Param("eIds") Set<String> eIds, @Param("lastTime") LocalDateTime lastTime);

    List<ABAdjustLogDetailPO> getAdjustLogDetailsAscByDate(@Param("eIds") Set<String> eIds, @Param("times") List<LocalDateTime> selectedDateTimes);

}
