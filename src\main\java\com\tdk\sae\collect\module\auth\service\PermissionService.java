package com.tdk.sae.collect.module.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.auth.mapper.PermissionMapper;
import com.tdk.sae.collect.module.auth.po.PermissionPO;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
public class PermissionService extends ServiceImpl<PermissionMapper, PermissionPO> {

    private final PermissionMapper permissionMapper;

    public List<PermissionPO> getEnabledPermissionByIds(@NotNull Collection<Long> permissionIds) {
        Assert.notNull(permissionIds, "权限id为空");
        LambdaQueryWrapper<PermissionPO> queryWrapper = Wrappers.lambdaQuery(PermissionPO.class)
                .in(PermissionPO::getId, permissionIds)
                .select(PermissionPO::getId, PermissionPO::getValue);
        return permissionMapper.selectList(queryWrapper);
    }
}
