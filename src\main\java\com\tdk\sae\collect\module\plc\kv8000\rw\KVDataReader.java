package com.tdk.sae.collect.module.plc.kv8000.rw;

import com.tdk.sae.collect.module.plc.kv8000.util.KV8000Utils;
import com.tdk.sae.collect.module.plc.kv8000.KVDataType;
import com.tdk.sae.collect.module.plc.kv8000.KVTypeFormat;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.kv8000.util.KVInstructBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 只限读取出长度不超过单个类型的数据,如需读取超过的要自行处理。
 * 如:读长度为4即64位,超过了int,float,需要自行处理。读长度为2即32位,满足int,float则有转换数据
 */
public class KVDataReader extends KVDataStream {

    private static final Logger logger = LoggerFactory.getLogger(KVDataReader.class);

    private String address;
    private KVTypeFormat type;
    // 一个单位表示16位=2字节, 如dataLength=2,则表示32位长度

    private KVDataReader(String ip, int port) throws IOException {
        super(ip, port);
    }

    public static KVDataReader getInstance(String ip, int port) throws IOException {
        return new KVDataReader(ip, port);
    }

    public KVDataReader setAddress(String address) {
        this.address = address;
        return this;
    }

    public KVDataReader setType(KVTypeFormat type) {
        this.type = type;
        return this;
    }

    public KVTypeFormat getType() {
        return this.type;
    }

    public KVDataReader setDataLength(int dataLength) {
        this.dataLength = dataLength;
        return this;
    }

    public KVData read() {
        if (address == null || type == null) {
            throw new RuntimeException("address or type not set!");
        }
        if (dataLength <= 0) {
            this.dataLength = type.getDefaultLength();
        }
        return doRead();
    }

    private KVData doRead() {
        KVDataType dataType = getTypeByFormat(type);
        try {
            String instruct = KVInstructBuilder.getInstance()
                    .read(dataLength, dataType)
                    .address(address)
                    .build();
            KVData kvData = KV8000Utils.executeInstruct(socket, instruct);
            if (kvData != null) {
                return translateData(kvData);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return null;
    }

    private KVData translateData(KVData kvData) {
        String rawData = kvData.getRawData();
        String translatedData = null;
        try {
            switch (type) {
                case BOOL:
                case UINT:
                    int uInt = Integer.parseInt(rawData);
                    short sInt = (short) uInt;
                    translatedData = Short.toString(sInt);
                    break;
                case DINT:
                    int dInt = Integer.parseUnsignedInt(rawData, 10);
                    translatedData = Integer.toString(dInt);
                    break;
                case REAL:
                    float floatVal = translateFloat(rawData);
                    translatedData = new BigDecimal(floatVal + "").toPlainString();
                    break;
                case STRING:
                    String[] uIntStrArr = rawData.split(" ");
                    List<Byte> byteList = new ArrayList<>();
                    for (String s : uIntStrArr) {
                        String bv = Integer.toBinaryString(Integer.parseInt(s));
                        bv = StringUtils.leftPad(bv, 16, '0');
                        byte b1 = Byte.valueOf(bv.substring(0, 8), 2);
                        if (b1 >= 32 && b1 <= 126) {
                            byteList.add(b1);
                        }
                        byte b2 = Byte.valueOf(bv.substring(8, 16), 2);
                        if (b2 >= 32 && b2 <= 126) {
                            byteList.add(b2);
                        }
                    }
                    Byte[] barr = byteList.toArray(new Byte[0]);
                    byte[] bytes = new byte[barr.length];
                    for(int i = 0; i < barr.length; i++) {
                        bytes[i] = barr[i];
                    }
                    translatedData = new String(bytes, StandardCharsets.US_ASCII);
                    break;
            }
            kvData.setTranslatedData(translatedData);
        } catch (Exception e) {
//            logger.error(e.getMessage());
        }
        return kvData;
    }

    private float translateFloat(String rawData){
        if(StringUtils.isBlank(rawData)){
            return 0.0f;
        }
        long a = Long.parseLong(rawData);//转成整型
        String b = Long.toBinaryString(a);//转二进制
        boolean c = false;//是否负数
        if (b.length() == 32) {
            b = b.substring(1); //去掉符号位
            c = true;
        }
        float d = Float.intBitsToFloat(Integer.parseInt(b, Character.MIN_RADIX));//转成float
        if (c) {
            d = 0 - d; //还原符号
        }
        return d;
    }

}
