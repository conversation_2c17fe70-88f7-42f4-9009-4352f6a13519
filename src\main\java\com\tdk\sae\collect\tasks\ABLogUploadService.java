package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import com.tdk.sae.collect.module.abDim.model.po.ABLogTrayMeanPO;
import com.tdk.sae.collect.module.abDim.service.ABLogService;
import com.tdk.sae.collect.module.abDim.service.ABLogTrayMeanService;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.module.ics.service.ICSLogService;
import com.tdk.sae.collect.module.ics.service.ICSLogTrayMeanService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class ABLogUploadService {

    private static final Logger logger = LoggerFactory.getLogger(ABLogUploadService.class);

    private final ABLogService icsLogService;

    private final ABLogTrayMeanService icsLogTrayMeanService;

    private final MQProducerService mqProducerService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/3 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<ABLogPO> logs = icsLogService.list(Wrappers.lambdaQuery(ABLogPO.class)
                .eq(ABLogPO::getSynced, 0)
                .orderByAsc(ABLogPO::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("AB_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/25 * * * * ?")
    private synchronized void uploadUnSyncLogTrayMeans() {
        if (!enableUpload) {
            return;
        }
        List<ABLogTrayMeanPO> logs = icsLogTrayMeanService.list(Wrappers.lambdaQuery(ABLogTrayMeanPO.class)
                .eq(ABLogTrayMeanPO::getSynced, 0)
                .orderByAsc(ABLogTrayMeanPO::getEndTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("AB_LOG_TRAY_MEAN", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                icsLogTrayMeanService.saveOrUpdateBatch(logs);
            }
        }
    }

}
