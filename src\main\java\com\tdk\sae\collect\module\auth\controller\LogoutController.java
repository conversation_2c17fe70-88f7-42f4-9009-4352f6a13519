package com.tdk.sae.collect.module.auth.controller;

import com.tdk.sae.collect.domain.result.Response;
import com.tdk.sae.collect.module.auth.cache.LoggedInUserCache;
import com.tdk.sae.collect.module.auth.dto.LoggedInUser;
import com.tdk.sae.collect.module.auth.utils.CommonUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RequiredArgsConstructor
@RestController
public class LogoutController {

    private final LoggedInUserCache loggedInUserCache;

    @PostMapping("/user/logout")
    public Response<LoggedInUser> logout(HttpServletRequest request) {
        String empNo = CommonUtils.getAccessToken(request);
        loggedInUserCache.del(empNo);
        return Response.ok("ok");
    }

}
