package com.tdk.sae.collect.module.auth.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_role")
public class RolePO extends DomainPO {

    private static final long serialVersionUID = 1L;

    @TableField(value = "role_name")
    private String roleName;

    @TableField(value = "role")
    private String role;

    @TableField(value = "sort")
    private Integer sort;

    public static final String ROLE_NAME = "role_name";
    public static final String ROLE = "role";
    public static final String SORT = "sort";
    public static final String ROLE_ID = "role_id";

}
