package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.adjust.utils.ReachFullDropHalfList;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSLogTrayMeanDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogPO;
import com.tdk.sae.collect.module.ics.service.ICSAutoAdjustService;
import com.tdk.sae.collect.module.plc.model.dto.KV8000ParamDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000DataService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamFormulaService;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component("ExceedControlButInSpecRule")
public class ExceedControlButInSpecRule extends AbstractRulesHandler {

    private final KV8000ParamService kv8000ParamService;

    private final KV8000DataService kv8000DataService;

    private final KV8000ParamFormulaService kv8000ParamFormulaService;

    private final SpecInfo specInfo;

    private static final String REGEX = "连续([\\s\\S]*?)点超出mean自动调整触发线但不超mean范围线，以第([\\s\\S]*?)点与目标值的差值作为调整量进行自动调整";

    private static final String[] KV_ADDRESS_REPRESENTS = new String[]{"W_EX1", "W_EX2",
            "W_EY1", "W_EY2",
            "W_GT1", "W_GT2",
            "R_EX1", "R_EX2",
            "R_EY1", "R_EY2",
            "R_GT1", "R_GT2",
            "W_EX3", "W_EY3", "W_GT3", "R_EX3", "R_EY3", "R_GT3"};

    @Override
    public void checkKV8000ParamCache() {
        List<KV8000ParamPO> paramList = kv8000ParamService.getCacheByEquipId(getEquipment().getId());
        for (String kvAddressRepresent : KV_ADDRESS_REPRESENTS) {
            if (KV8000ParamCache.get(getEquipment().getId()).get(kvAddressRepresent) != null) {
                continue;
            }
            List<KV8000ParamPO> filteredList = paramList.stream().filter(p -> p.getAddressRepresent().equals(kvAddressRepresent)).collect(Collectors.toList());
            if (!filteredList.isEmpty()) {
                KV8000ParamCache.get(getEquipment().getId()).put(kvAddressRepresent, filteredList.get(0));
            }
        }
    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("ExceedControlButInSpecRule");
        if (isDisabled()) {
            return doNextRule(advice);
        }
        String rule = getRulePopulate().getRule();
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(rule);
        if (!matcher.find()) {
            // TODO: log detail
            return doNextRule(advice);
        }
        String continueOverControlLimitStr = matcher.group(1);
        String continueNPointRefStr = matcher.group(2);

        int continueOverControlLimit = Integer.parseInt(continueOverControlLimitStr);
        int continueNPointRef = Integer.parseInt(continueNPointRefStr);
        LocalDateTime nowTIme=LocalDateTime.now();
        // 只有mean表里面有的parameter, 不存在GT
        Map<String, ReachFullDropHalfList<ICSLogTrayMeanDTO>> paramDataMap = ICSAutoAdjustService.DATA_CACHE.get(getEquipment().getId());
        Map<String,Integer> continueMap = ICSAutoAdjustService.CONTINUE_CACHE.get(getEquipment().getId());
        Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(getEquipment().getId());
        paramDataMap.forEach((parameter, parameterDataList) -> {
            Integer num=continueMap.get(parameter);
            // 如果设置的值不是1, 则不执行该参数的调整
            if (!BigDecimal.ONE.equals(specMap.get(parameter).get("ENABLE"))) {
                return;
            }
            List<ICSLogTrayMeanDTO> meanList = parameterDataList.getList();
            BigDecimal specUpperLimit = specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
            BigDecimal specLowerLimit = specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
            BigDecimal controlUpperLimit = specMap.get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
            BigDecimal controlLowerLimit = specMap.get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
//            BigDecimal specUpperLimit = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
//            BigDecimal specLowerLimit = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
//            BigDecimal controlUpperLimit = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
//            BigDecimal controlLowerLimit = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
            int rmIdx = -1;
            int continueCount = 0;
            boolean isAboveControlUpperLimit = false;
            boolean isBelowControlLowerLimit = false;
            int TraversePointNum=0;
            for (int i = 0; i < meanList.size(); i++) {
                TraversePointNum++;
                ICSLogTrayMeanDTO mean = meanList.get(i);
                long millisDiff = ChronoUnit.SECONDS.between(mean.getEndTime(), nowTIme);
                if(millisDiff>300){
                    continueCount = 0;
                }else{
                    boolean aboveControlUpperLimit = mean.getMeanValue().compareTo(controlUpperLimit) > 0;
                    boolean belowControlLowerLimit = mean.getMeanValue().compareTo(controlLowerLimit) < 0;

                    if (mean.getMeanValue().compareTo(specLowerLimit) >= 0
                            && mean.getMeanValue().compareTo(specUpperLimit) <= 0
                            && (aboveControlUpperLimit || belowControlLowerLimit)) {
                        if (continueCount == 0) {
                            isAboveControlUpperLimit = aboveControlUpperLimit;
                            isBelowControlLowerLimit = belowControlLowerLimit;
                        }

                        if ((isAboveControlUpperLimit && aboveControlUpperLimit) || (isBelowControlLowerLimit && belowControlLowerLimit)) {
                            if (++continueCount == continueOverControlLimit) {
                                rmIdx = i - continueOverControlLimit + continueNPointRef;
                                break;
                            }
                        } else {
                            isAboveControlUpperLimit = aboveControlUpperLimit;
                            isBelowControlLowerLimit = belowControlLowerLimit;
                            continueCount = 1;

                        }
                    } else {
                        continueCount = 0;
                    }
                }
            }
            if (rmIdx >= 0) {
                num=num+1;
                continueMap.put(parameter,num);
                ICSLogTrayMeanDTO adjustRefMean = parameterDataList.getList().get(rmIdx);
                AutoAdjustAdvice.AutoAdjustRuleAdvice ruleAdvice = advice.newRuleAdvice("ExceedControlButInSpecRule");
                ruleAdvice.setEquipment(getEquipment());

                Map<String, KV8000ParamPO> paramMap = KV8000ParamCache.get(getEquipment().getId());
                KV8000ParamPO wParam = null;
                KV8000ParamPO rParam = null;
                switch (parameter) {
                    case "ED1":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[4]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[10]);
                        break;
                    case "ED2":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[5]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[11]);
                        break;
                    case "EX1":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[0]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[6]);
                        break;
                    case "EX2":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[1]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[7]);
                        break;
                    case "EY1":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[2]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[8]);
                        break;
                    case "EY2":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[3]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[9]);
                        break;
                    case "ED3":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[14]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[17]);
                        break;
                    case "EX3":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[12]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[15]);
                        break;
                    case "EY3":
                        wParam = paramMap.get(KV_ADDRESS_REPRESENTS[13]);
                        rParam = paramMap.get(KV_ADDRESS_REPRESENTS[16]);
                        break;
                }
                if (wParam != null && rParam != null) {
                    KV8000DataPO kv8000Data = kv8000DataService.lambdaQuery()
                            .eq(KV8000DataPO::getParamId, rParam.getId()).orderByDesc(KV8000DataPO::getReadTime).last("limit 1").one();
                    if (kv8000Data == null) {
                        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> advices = advice.getRuleAdvices().get("ExceedControlButInSpecRule");
                        advices.remove(ruleAdvice);
                        return;
                    }

                    // 设置用于获取公式的paramId, parameter
                    ruleAdvice.setKv8000Param(BeanUtil.copyProperties(wParam, KV8000ParamDTO.class));
                    ruleAdvice.setAddressRepresent(wParam.getAddressRepresent());

                    // 设置最新的plc值
                    BigDecimal readData = new BigDecimal(kv8000Data.getReadData());
                    ruleAdvice.setLatestReadData(readData);

                    // 先按1:1的比例计算自动设置的值
//                    BigDecimal diffValue = SpecInfo.getSpec().get(parameter).get("TARGET").subtract(adjustRefMean.getMeanValue());
                    BigDecimal diffValue = specMap.get(parameter).get("TARGET").subtract(adjustRefMean.getMeanValue());

                    ruleAdvice.setDiffValue(diffValue);
                    BigDecimal writeData = readData.add(diffValue);
                    ruleAdvice.setNewValueStr(writeData.toPlainString());
                    ruleAdvice.setNewValue(writeData);

                    // 如果有公式应用, 则公式计算的结果会覆盖上面计算的值
                    kv8000ParamFormulaService.calculateRuleAdvice(ruleAdvice);

                    ICSAdjustLogDTO log = advice.getAdjustLog();
                    log.setAdjustType(getRulePopulate().getRuleType());

                    ICSAdjustLogDetailDTO detail = log.newDetail();
                    detail.setApplyId(getRulePopulate().getApplyId());
                    detail.setParamId(wParam.getId());
                    detail.setMeanId(adjustRefMean.getId());
                    detail.setLogLevel(1);
                    detail.setLogMsg(StrUtil.format("{} Follow rule: {}. From: ({}) To: ({})",
                            getEquipment().getEquipCode(),
                            getRulePopulate().getRule(),
                            ruleAdvice.getLatestReadData().toPlainString(),
                            ruleAdvice.getNewValueStr()));
                    detail.setFromValue(ruleAdvice.getLatestReadData().toPlainString());
                    detail.setToValue(ruleAdvice.getNewValueStr());
                    detail.setWriteTime(LocalDateTime.now());
                }
                parameterDataList.removeBeforeIdxIncluded(rmIdx);
            }else{
                if(TraversePointNum>=2){
                    num=0;
                    continueMap.put(parameter,num);
                }
            }
        });
        List<AutoAdjustAdvice.AutoAdjustRuleAdvice> ruleAdvices = advice.getRuleAdvices().get("ExceedSpecThenStopRule");
        if (ruleAdvices != null) {
            advice.getAdjustLog().setAdjusted(1);
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
            return advice;
        }
        return doNextRule(advice);
    }

}
