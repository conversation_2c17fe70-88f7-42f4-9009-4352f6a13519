package com.tdk.sae.collect.module.ics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.ics.model.adjust.ICSAdjustRulesPopulate;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustRulesApplyPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ICSAdjustRulesApplyMapper extends BaseMapper<ICSAdjustRulesApplyPO> {

    List<ICSAdjustRulesPopulate> getRulesPopulate(@Param("equipId") Long equipId);

    List<ICSAdjustRulesApplyPO> getRulesApplyByType(@Param("equipId") Long equipId, @Param("ruleType") Integer ruleType);

}
