package com.tdk.sae.collect.module.ics.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
@EqualsAndHashCode
public class ICSPidLogDto {
    private Long id;

    private Long equipId;

    private String parameter;

    private BigDecimal logValue;

    private LocalDateTime logTime;

    private LocalDateTime writeTime;

    private String logMsg;

    private String fromValue;

    private String toValue;

    private Long paramId;

    private Integer isDeleted = 0;

    private Integer synced = 0;
}
