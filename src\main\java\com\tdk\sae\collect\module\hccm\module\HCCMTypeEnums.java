package com.tdk.sae.collect.module.hccm.module;

public enum HCCMTypeEnums {

    SUSP_X("suspX"),
    SUSP_Y("suspY"),
    SUSP_ANGLE("suspAngle"),
    AF_SDX("AFSDX"),
    AF_SDY("AFSDY"),
    BFSDX_BFSDY("BFSDX&BFSDY"),
    SUSP_X_Y_BY_POS("SuspX&YByPos");

    private String displayName;

    // 构造方法，设置显示名称
    HCCMTypeEnums(String displayName) {
        this.displayName = displayName;
    }

    // 获取枚举值的显示名称
    public String getDisplayName() {
        return displayName;
    }

    // 根据枚举名称获取显示名称
    public static String getDisplayNameByKey(String key) {
        for (HCCMTypeEnums type : HCCMTypeEnums.values()) {
            if (type.getDisplayName().equals(key)) {
                return type.getDisplayName();
            }
        }
        return null;
    }
}
