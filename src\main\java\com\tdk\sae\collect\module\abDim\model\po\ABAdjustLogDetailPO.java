package com.tdk.sae.collect.module.abDim.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ab_adjust_log_detail")
public class ABAdjustLogDetailPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "log_id")
    private Long logId;

    @TableField(value = "apply_id")
    private Long applyId;

    @TableField(value = "param_id")
    private Long paramId;

    @TableField(value = "mean_id")
    private Long meanId;

    @TableField(value = "log_msg")
    private String logMsg;

    @TableField(value = "log_level")
    private Integer logLevel;

    @TableField(value = "from_value")
    private String fromValue;

    @TableField(value = "to_value")
    private String toValue;

    @TableField(value = "write_time")
    private LocalDateTime writeTime;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "synced")
    private Integer synced = 0;

}
