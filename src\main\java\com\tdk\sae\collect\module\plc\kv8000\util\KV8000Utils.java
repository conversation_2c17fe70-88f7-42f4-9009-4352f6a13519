package com.tdk.sae.collect.module.plc.kv8000.util;

import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Scanner;

@UtilityClass
public class KV8000Utils {

    private final Logger logger = LoggerFactory.getLogger(KV8000Utils.class);

    public KVData executeInstruct(Socket socket, String instruct) {
        if (socket == null || socket.isClosed()) {
            return null;
        }
        try {
            InputStream inputStream = socket.getInputStream();
            Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name());

            OutputStream outputStream = socket.getOutputStream();
            Writer writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
            PrintWriter printWriter = new PrintWriter(writer);
            printWriter.println(instruct);
            printWriter.flush();

            String rawData = scanner.nextLine();
            return new KVData(instruct, rawData, LocalDateTime.now());
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }


}
