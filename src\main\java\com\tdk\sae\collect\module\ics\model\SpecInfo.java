package com.tdk.sae.collect.module.ics.model;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ics.mapper.ICSConfMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSConfPO;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SpecInfo extends ServiceImpl<ICSConfMapper, ICSConfPO> {

    private static final String PROJECT_CODE = SpringUtil.getProperty("system.projectCode");


    private static final Map<String, Map<String, BigDecimal>> btsMap = new HashMap<>(16);
    private static final Map<String, Map<String, BigDecimal>> tsdMap = new HashMap<>(16);
    static {

        Map<String, BigDecimal> tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(300));
        tmp.put("TARGET", new BigDecimal(280));
        tmp.put("LSL", new BigDecimal(230));
        btsMap.put("ED1", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(185));
        tmp.put("TARGET", new BigDecimal(145));
        tmp.put("LSL", new BigDecimal(115));
        btsMap.put("ED2", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(40));
        tmp.put("TARGET", new BigDecimal(0));
        tmp.put("LSL", new BigDecimal(-40));
        btsMap.put("EX1", tmp);
        btsMap.put("EX2", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(170));
        tmp.put("TARGET", new BigDecimal(130));
        tmp.put("LSL", new BigDecimal(90));
        btsMap.put("EY1", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(450));
        tmp.put("TARGET", new BigDecimal(410));
        tmp.put("LSL", new BigDecimal(370));
        btsMap.put("EY2", tmp);

        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(350));
        tmp.put("LSL", new BigDecimal(250));
        tsdMap.put("ED1", tmp);
        tsdMap.put("ED3", tmp);
        tmp = new HashMap<>(2);
        tmp.put("USL", new BigDecimal(400));
        tmp.put("LSL", new BigDecimal(300));
        tsdMap.put("ED2", tmp);
    }

    public static Map<String, Map<String, BigDecimal>> getSpec() {
        if (PROJECT_CODE.startsWith("BTS")) {
            return btsMap;
        }
        if (PROJECT_CODE.startsWith("TSD")) {
            return tsdMap;
        }
        return new HashMap<>(2);
    }

    // 根据equipId获取对应设备的相关配置
    public Map<String, Map<String, BigDecimal>> getSpecInDbFirst(String equipId) {
        List<ICSConfPO> confList = lambdaQuery().eq(ICSConfPO::getEquipId, equipId).list();
        if (CollectionUtil.isEmpty(confList)) {
            return getSpec();
        }
        // 0: confName: ED1, key: USL, value: 300
        // 1: confName: ED2, key: LSL, value: 110
        // ......
        Map<String, Map<String, BigDecimal>> result = new HashMap<>(8);
        confList.forEach(conf -> {
            Map<String, BigDecimal> confMap = result.get(conf.getConfName());
            if (CollectionUtil.isEmpty(confMap)) {
                confMap = new HashMap<>(4);
            }
            if ("USL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_UPPER_SPEC_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("LSL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_LOWER_SPEC_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("UCL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_UPPER_CONTROL_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("LCL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_LOWER_CONTROL_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("UIL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_INNER_UPPER_CONTROL_LIMIT", new BigDecimal(conf.getConfValue()));
            } else if ("LIL".equals(conf.getConfKey())) {
                confMap.put("ADJUST_INNER_LOWER_CONTROL_LIMIT", new BigDecimal(conf.getConfValue()));
            } else {
                // TARGET, ENABLE...
                confMap.put(conf.getConfKey(), new BigDecimal(conf.getConfValue()));
            }
            result.put(conf.getConfName(), confMap);
        });
        return result;
    }

}
