package com.tdk.sae.collect.module.abDim.adjust;

import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.abDim.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.abDim.model.adjust.ABAdjustRulesPopulate;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class AbstractRulesHandler {

    // <variable name, KVParamPO>
    protected static final Map<String, Map<String, KV8000ParamPO>> KV8000ParamCache = new ConcurrentHashMap<>(2);

    public abstract void checkKV8000ParamCache();

    private EquipmentDTO equipmentDTO;

    private ABAdjustRulesPopulate rulePopulate;

    protected ABAdjustRulesPopulate getRulePopulate() {
        return rulePopulate;
    }
    protected void setRulePopulate(ABAdjustRulesPopulate rulePopulate) {
        this.rulePopulate = rulePopulate;
    }

    protected void setEquipment(EquipmentDTO equip) {
        this.equipmentDTO = equip;
    }

    protected EquipmentDTO getEquipment() {
        return equipmentDTO;
    }

    public boolean isEnabled() {
        return rulePopulate.getEnabled() == 1;
    }

    public boolean isDisabled() {
        return rulePopulate.getEnabled() == 0;
    }

    public AutoAdjustAdvice doNextRule(AutoAdjustAdvice advice) {
        return nextRule != null ? nextRule.doHandler(advice) : advice;
    }


    /** ------------------------------规则链------------------------------ **/

    protected AbstractRulesHandler nextRule = null;

    public abstract AutoAdjustAdvice doHandler(AutoAdjustAdvice advice);

    public void doNext(AbstractRulesHandler handler) {
        this.nextRule = handler;
    }

    public static class Builder {
        private AbstractRulesHandler head;
        private AbstractRulesHandler tail;

        public void addHandler(AbstractRulesHandler handler) {
            handler.checkKV8000ParamCache();
            if (this.head == null) {
                this.head = handler;
            } else {
                this.tail.doNext(handler);
            }
            this.tail = handler;
        }

        public AbstractRulesHandler build() {
            return this.head;
        }
    }

}