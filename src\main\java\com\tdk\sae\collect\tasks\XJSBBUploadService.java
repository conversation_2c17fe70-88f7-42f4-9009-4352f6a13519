package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBDataPO;
import com.tdk.sae.collect.module.xjsbb.model.po.XJSBBLogPO;
import com.tdk.sae.collect.module.xjsbb.service.XJSBBDataService;
import com.tdk.sae.collect.module.xjsbb.service.XJSBBLogService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class XJSBBUploadService {

    private final MQProducerService mqProducerService;

    private final XJSBBLogService xjsbbLogService;

    private final XJSBBDataService xjsbbDataService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void uploadUnSyncLogs() {
        if (!enableUpload) {
            return;
        }
        List<XJSBBLogPO> logs = xjsbbLogService.list(Wrappers.lambdaQuery(XJSBBLogPO.class)
                .eq(XJSBBLogPO::getSynced, 0)
                .orderByAsc(XJSBBLogPO::getLogTime).last("limit 1000"));
        if (!logs.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("XJSBB_LOG", logs);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                logs.forEach(l -> l.setSynced(1));
                xjsbbLogService.saveOrUpdateBatch(logs);
            }
        }
    }

    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void uploadUnSyncData() {
        if (!enableUpload) {
            return;
        }
        List<XJSBBDataPO> data = xjsbbDataService.list(Wrappers.lambdaQuery(XJSBBDataPO.class)
                .eq(XJSBBDataPO::getSynced, 0)
                .orderByAsc(XJSBBDataPO::getReadTime).last("limit 1000"));
        if (!data.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("XJSBB_DATA", data);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                data.forEach(l -> l.setSynced(1));
                xjsbbDataService.saveOrUpdateBatch(data);
            }
        }
    }

}
