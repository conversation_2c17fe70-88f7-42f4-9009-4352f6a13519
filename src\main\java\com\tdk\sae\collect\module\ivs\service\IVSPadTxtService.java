package com.tdk.sae.collect.module.ivs.service;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ivs.mapper.IVSPadTxtMapper;
import com.tdk.sae.collect.module.ivs.module.dto.IVSPadTxtDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSLogPo;
import com.tdk.sae.collect.module.ivs.module.po.IVSPadTxtPo;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
public class IVSPadTxtService extends ServiceImpl<IVSPadTxtMapper, IVSPadTxtPo> {

    private final IVSPadTxtMapper ivsPadTxtMapper;

    private static final Logger logger = LoggerFactory.getLogger(IVSPadTxtService.class);

    public void parseIVSPadTxt(IVSLogPo ivsLogPo, Long id, String direction) {
        String input = ivsLogPo.getRawData();
        // 解析字符串
        String[] parts = input.split(",");

        if (parts.length < 4) {
            logger.error("IVSPad数据长度不正确，pad数据不入库: {}", input);
            return;
        }

        // 解析采集时间
        LocalDateTime collectTime = LocalDateTimeUtil.parse(parts[0], "yyyy/MM/dd HH:mm:ss");

        // 创建 IVSPadTxt 实体
        IVSPadTxtPo ivsPadTxt = new IVSPadTxtPo();
        ivsPadTxt.setCollectTime(collectTime);

        // 设置 result 字段
        ivsPadTxt.setResult(parts[2]); // result (OK)

        // 解析 HGA5Pad1 和 pos 字段
        String posInfo = parts[3].split("\\s+")[0]; // 提取 HGA5Pad1
        // 使用正则表达式提取数字（pos）
        Pattern pattern = Pattern.compile("HGA\\d+Pad(\\d+)");
        Matcher matcher = pattern.matcher(posInfo);
        int pos = -1;
        if (matcher.find()) {
            pos = Integer.parseInt(matcher.group(1)); // 提取并解析数字
        }
        //0-13才入库
        if (pos < 0 || pos > 13) {
            return;
        }
        ivsPadTxt.setPos(pos);
        ivsPadTxt.setCheckId(id);
        ivsPadTxt.setEquipId(ivsLogPo.getEquipId());
        ivsPadTxt.setDirection(direction);

        // 保存
        save(ivsPadTxt);
    }

    public Map<String,Object> getPadData(String start_time,String end_time){
        Map<String,Object> resultMap=new HashMap<>();
        Integer[] Lcount=new Integer[13];
        Arrays.fill(Lcount,0);
        Integer[] Rcount=new Integer[13];
        Arrays.fill(Rcount,0);
        resultMap.put("L-PAD",Lcount);
        resultMap.put("R-PAD",Rcount);
        LocalDateTime start=LocalDateTimeUtil.parse(start_time, DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime end=LocalDateTimeUtil.parse(end_time, DatePattern.NORM_DATETIME_PATTERN);
        List< IVSPadTxtDTO> padTxtDTOList = ivsPadTxtMapper.getPadData(start,end);
        padTxtDTOList.forEach(pad->{
            String direction=pad.getDirection();
            Integer pos=pad.getPos();
            if(direction.equals("L")){
                Lcount[pos]=pad.getBadCount();
            }else{
                Rcount[pos]=pad.getBadCount();
            }
        });
        return resultMap;
    }
}
