package com.tdk.sae.collect.module.ivs.module.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
@TableName("t_biz_ivs_conf")
public class IVSConfPO implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "conf_name")
    private String confName;


    @TableField(value = "conf_value")
    private String confValue;

    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted = 0;

    @TableField(value = "remark")
    private String remark;

}
