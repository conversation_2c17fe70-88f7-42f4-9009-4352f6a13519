package com.tdk.sae.collect.module.ivs.mapper;

import com.tdk.sae.collect.module.ivs.module.dto.IVSPadTxtDTO;
import com.tdk.sae.collect.module.ivs.module.dto.IVSTxtDTO;
import com.tdk.sae.collect.module.ivs.module.po.IVSTxtPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IVSTxtMapper extends BaseMapper<IVSTxtPo> {
    List<IVSTxtDTO> getIvsTxtData(@Param("start") LocalDateTime start, @Param("end")LocalDateTime end);

}
