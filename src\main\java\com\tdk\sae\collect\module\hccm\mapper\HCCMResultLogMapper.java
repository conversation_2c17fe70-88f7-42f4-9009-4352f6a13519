package com.tdk.sae.collect.module.hccm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.hccm.module.po.HCCMResultPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface HCCMResultLogMapper extends BaseMapper<HCCMResultPO> {

  public List<HCCMResultPO> getHCCMResultData(@Param("equipId")String eId,@Param("times") List<LocalDateTime> selectedDateTimes);

}
