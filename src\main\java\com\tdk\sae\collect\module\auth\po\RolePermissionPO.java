package com.tdk.sae.collect.module.auth.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tdk.sae.collect.domain.po.DomainPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_role_perm")
public class RolePermissionPO extends DomainPO {

    private static final long serialVersionUID = 1L;

    @TableField(value = "role_id")
    private Long roleId;

    @TableField(value = "perm_id")
    private Long permId;

}