package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.lang.Console;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustLogDetailDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("ICSAutoAdjustOnOffRule")
public class ICSAutoAdjustOnOffRule extends AbstractRulesHandler {

    @Override
    public void checkKV8000ParamCache() {
        KV8000ParamCache.computeIfAbsent(getEquipment().getId(), k -> new HashMap<>());
    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("ICSAutoAdjustOnOffRule");
        if (isDisabled()) {
            advice.getAdjustLog().setAdjustType(getRulePopulate().getRuleType());
            ICSAdjustLogDetailDTO detail = advice.getAdjustLog().newDetail();
            detail.setApplyId(getRulePopulate().getApplyId());
            detail.setLogMsg("Auto adjust stopped. Cause: Auto adjust is disabled");
            detail.setLogLevel(0);
            return advice;
        }
        advice.getAdjustLog().setEquipId(getRulePopulate().getEquipId());
        return doNextRule(advice);
    }
}
