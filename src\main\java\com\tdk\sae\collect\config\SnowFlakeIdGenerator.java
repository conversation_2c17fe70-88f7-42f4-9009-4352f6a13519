package com.tdk.sae.collect.config;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.tdk.sae.collect.domain.common.SystemProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class SnowFlakeIdGenerator implements IdentifierGenerator {

    private final SystemProperties systemProperties;

    @Override
    public Number nextId(Object entity) {
        return getNextId();
    }

    public Long nextId() {
        return getNextId().longValue();
    }

    private Number getNextId() {
        Long workerId = systemProperties.getMachineId();
        Long datacenterId = systemProperties.getDatacenterId();
        Snowflake snowflake = IdUtil.getSnowflake(workerId, datacenterId);
        return snowflake.nextId();
    }

}