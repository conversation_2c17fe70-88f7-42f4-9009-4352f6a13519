package com.tdk.sae.collect.module.ics.adjust.rules;

import cn.hutool.core.lang.Console;
import com.tdk.sae.collect.module.ics.adjust.AbstractRulesHandler;
import com.tdk.sae.collect.module.ics.model.adjust.AutoAdjustAdvice;
import org.springframework.stereotype.Component;

@Component("PosKeepExceedSpecNTimesRule")
public class PosKeepExceedSpecNTimesRule extends AbstractRulesHandler {
    @Override
    public void checkKV8000ParamCache() {

    }

    @Override
    public AutoAdjustAdvice doHandler(AutoAdjustAdvice advice) {
//        Console.log("PosKeepExceedSpecNTimesRule");
        return doNextRule(advice);
    }
}
