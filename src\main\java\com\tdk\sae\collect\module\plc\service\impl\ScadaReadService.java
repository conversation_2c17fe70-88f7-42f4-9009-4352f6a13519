package com.tdk.sae.collect.module.plc.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.kv8000.entity.KVData;
import com.tdk.sae.collect.module.plc.model.dto.KV8000DataDTO;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.model.po.KV8000ParamPO;
import com.tdk.sae.collect.module.plc.scada.ScadaDataReader;
import com.tdk.sae.collect.module.plc.service.IReadPlcService;
import com.tdk.sae.collect.module.plc.service.IReadPlcService2;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service("scadaread")
public class ScadaReadService implements IReadPlcService {

    private final Logger logger = LoggerFactory.getLogger(ScadaReadService.class);

    private final KV8000ParamService scadaParamService;

    private final KV8000DataService scadaDataService;

    // 上次读取的数据缓存
    private final TimedCache<String, List<KV8000DataDTO>> scadaDataCache = CacheUtil.newTimedCache(30000);

    @Override
    public void readParams(EquipmentDTO equip) {
        String scadaAddress = equip.getAddress();
        if (StrUtil.isEmpty(scadaAddress)) {
            return;
        }
        List<KV8000ParamPO> params = scadaParamService.getCacheByKvId(equip.getId());
        if (CollectionUtil.isEmpty(params)) {
            return;
        }
        String[] ipPort = scadaAddress.split(":");
        String ip = ipPort[0];
        int port = Integer.parseInt(ipPort[1]);
        try {
            Set<String> paramAddress = params.stream().map(KV8000ParamPO::getAddress).collect(Collectors.toSet());
            String[] addressArr = ArrayUtil.toArray(paramAddress, String.class);
            Map<String, KV8000ParamPO> paramMap = new HashMap<>();
            params.forEach(p -> {
                // 写的地址跳过, 防止读被覆盖
                if (p.getVariable().contains("_W")) {
                    return;
                }
                paramMap.put(p.getAddress(), p);
            });

            Map<String, KV8000DataPO> dataMap = new HashMap<>();
            List<KVData> dataList = ScadaDataReader.read(ip, port, paramMap, addressArr);
            if (!CollectionUtil.isEmpty(dataList)) {
                dataList.forEach(d -> dataMap.put(d.getInstruct(), d.convert(paramMap.get(d.getInstruct()))));
            }

            if (CollectionUtil.isNotEmpty(dataMap)) {
                List<KV8000DataPO> newDataList = new ArrayList<>();
                List<KV8000DataDTO> oldData = scadaDataCache.get(equip.getId());
                if (CollectionUtil.isEmpty(oldData)) {
                    oldData = scadaDataService.getLatestParamDataByEquipId(equip.getId());
                }
                List<KV8000DataDTO> finalOldData = oldData;
                Map<String, KV8000DataDTO> memo = new HashMap<>();
                List<KV8000DataDTO> tmpCache = new ArrayList<>();
                dataMap.forEach((address, value) -> {
                    // 记忆循环历史, 降复杂度
                    KV8000DataDTO odc = memo.get(address);
                    if (odc == null) {
                        // 查找对应的oldData
                        for (KV8000DataDTO od : finalOldData) {
                            String addr = od.getAddress();
                            // 警告类型地址去重,只需要保存一个.0的位置即可
                            if (od.getAddress().contains(".")) {
                                if (od.getAddress().contains(".0")) {
                                    addr = od.getAddress().substring(0, od.getAddress().indexOf("."));
                                } else {
                                    continue;
                                }
                            }
                            // 命中使用
                            if (addr.equals(address)) {
                                odc = od;
                            }
                            memo.put(addr, od);
                        }
                    }
                    // 缓存此次读取值
                    KV8000DataDTO cacheData = BeanUtil.toBean(value, KV8000DataDTO.class);
                    cacheData.setAddress(address);
                    tmpCache.add(cacheData);
                    if (odc == null || !value.getRawData().equals(odc.getRawData())) {
                        newDataList.add(value);
                    }
                });
                scadaDataCache.put(equip.getId(), tmpCache);
                if (!newDataList.isEmpty()) {
                    scadaDataService.saveBatch(newDataList);
                }
            }
        } catch (Exception e) {
            logger.error("Error reading Scada params! error:{}", e.getMessage(), e);
        }
    }

    @Override
    public KV8000DataPO readParam(EquipmentDTO equip, KV8000ParamPO param) {
        String scadaAddress = equip.getAddress();
        String[] ipPort = scadaAddress.split(":");
        String ip = ipPort[0];
        int port = Integer.parseInt(ipPort[1]);
        ScadaDataReader reader = new ScadaDataReader(ip, port);
        try {
            KVData data = reader.setDataPoint(param.getAddress())
                    .setDataType(param.getDataType())
                    .setScale(param.getCommCycle())
                    .read();
            if (data != null) {
                return data.convert(param);
            }
        } catch (Exception e) {
            logger.error("Error reading Scada params! error:{}", e.getMessage(), e);
        }
        return null;
    }
    public List<KV8000DataDTO> getDataCacheByKvId(String kvId){
        List<KV8000DataDTO> data=scadaDataCache.get(kvId);
        return data;
    }

}
