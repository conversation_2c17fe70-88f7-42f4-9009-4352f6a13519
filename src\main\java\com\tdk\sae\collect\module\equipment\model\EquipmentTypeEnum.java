package com.tdk.sae.collect.module.equipment.model;

public enum EquipmentTypeEnum {

    IPC(1,"IPC"),
    IVS_IMAGE(2, "IVS-IMAGE"),
    IVS_LOG(3,"IVS-LOG"),
    KEYENCE_KV8000(4,"KV8000"),
    ICS_LOG(5,"ICS-LOG"),
    ICS_IMAGE(6,"ICS-IMAGE"),
    OCR_ICS(7,"OCR-ICS"),
    XJSBB_LOG(8, "XJSBB-LOG"),
    AB_LOG(9, "AB-LOG"),
    SCADA(10,"SCADA"),
    ICS_TREND(11,"TREND"),
    HCCM_RESULT(12,"HCCM-RESULT-LOG"),
    HCCM_SLIDER(13,"HCCM-SLIDER-LOG");

    private int type;
    private String name;

    public static boolean isEquipment(int type){

        for (EquipmentTypeEnum value : EquipmentTypeEnum.values()) {
            if(value.getType()==type) return true;
        }

        return false;
    }

    public static boolean isEquipment(String name){

        for (EquipmentTypeEnum value : EquipmentTypeEnum.values()) {
            if(value.getName().equals(name)) return true;
        }

        return false;
    }

    EquipmentTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public static int getType(String name) {
        for (EquipmentTypeEnum value : EquipmentTypeEnum.values()) {
            if(value.getName().equals(name)) return value.getType();
        }
        return 0;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public static String getName(int type) {
        for (EquipmentTypeEnum value : EquipmentTypeEnum.values()) {
            if(value.getType()==type) return value.getName();
        }
        return null;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static class Constants {
        public static final int IPC = 1;
        public static final int IVS_IMAGE = 2;
        public static final int IVS_LOG = 3;
        public static final int KEYENCE_KV8000 = 4;
        public static final int ICS_LOG = 5;
        public static final int ICS_IMAGE = 6;
        public static final int OCR_ICS = 7;
        public static final int XJSBB_LOG = 8;
        public static final int AB_LOG = 9;
        public static final int SCADA = 10;
        public static final int ICS_TREND = 11;
        public static final int HCCM_RESULT = 12;
        public static final int HCCM_SLIDER = 13;

    }

}