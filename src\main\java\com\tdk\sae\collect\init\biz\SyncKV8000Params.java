package com.tdk.sae.collect.init.biz;

import cn.hutool.core.collection.CollectionUtil;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.init.SystemInfoService;
import com.tdk.sae.collect.module.equipment.model.EquipmentTypeEnum;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000ParamService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@RequiredArgsConstructor
@Component
@DependsOn("systemInfoService")
public class SyncKV8000Params {

    private static final Logger logger = LoggerFactory.getLogger(SyncKV8000Params.class);

    private final KV8000ParamService kv8000ParamService;
    private final SystemProperties systemProperties;

    //@PostConstruct
    public void init() {
        if (CollectionUtil.isEmpty(SystemInfoService.getEquipmentMap())) {
            return;
        }
        List<EquipmentDTO> kv8000s = SystemInfoService.getEquipmentMap().get(systemProperties.getType());
        if (CollectionUtil.isEmpty(kv8000s)) {
            return;
        }
        kv8000s.forEach(kv8000ParamService::trySyncParams);
    }



}
