package com.tdk.sae.collect.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tdk.sae.collect.module.common.service.impl.MQProducerService;
import com.tdk.sae.collect.module.plc.model.po.KV8000DataPO;
import com.tdk.sae.collect.module.plc.service.impl.KV8000DataService;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class PlcDataUploadService {

    private static final Logger logger = LoggerFactory.getLogger(PlcDataUploadService.class);

    private final KV8000DataService kv8000DataService;

    private final MQProducerService mqProducerService;

    @Value("${system.enable-upload}")
    private Boolean enableUpload;

    @Scheduled(cron = "0/5 * * * * ?")
    private synchronized void uploadUnSyncPlcData() {
        if (!enableUpload) {
            return;
        }
        List<KV8000DataPO> data = kv8000DataService.list(Wrappers.lambdaQuery(KV8000DataPO.class)
                .eq(KV8000DataPO::getSynced, 0)
                .orderByAsc(KV8000DataPO::getReadTime).last("limit 1000"));
        if (!data.isEmpty()) {
            SendResult sendResult = mqProducerService.sendSyncPayload("KV8000_PARAM_DATA", data);
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                data.forEach(l -> l.setSynced(1));
                kv8000DataService.saveOrUpdateBatch(data);
            }
        }
    }

}
