CREATE DATABASE collect CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE collect;

CREATE  TABLE collect.hds_file_scan (
	id                   BIGINT  NOT NULL     PRIMARY KEY,
	equipment_id         BIGINT       ,
	is_file              INT       ,
	file_name            <PERSON><PERSON><PERSON><PERSON>(2000)       ,
	parent_file_name     <PERSON><PERSON><PERSON><PERSON>(450)       ,
	file_path            VARCHAR(2000)       ,
	file_update_date     DATETIME       ,
	created_date         DATETIME       ,
	save_path            VARCHAR(1000)       ,
	is_upload            INT       ,
	remote_path          VARCHAR(1000)       ,
	file_size            BIGINT       ,
	state                TINYINT   DEFAULT (1)    ,
	is_deleted           INT   DEFAULT (0)    ,
	created_by           BIGIN<PERSON>       ,
	created_time         DATETIME       ,
	updated_by           BIGINT       ,
	updated_time         DATETIME       ,
	description          VARCHAR(256)
 ) engine=InnoDB;

CREATE  TABLE collect.t_biz_ivs_txt (
	id                   BIGINT  NOT NULL     PRIMARY KEY,
	collect_time         DATETIME       ,
	pad_result           VARCHAR(40)       ,
	result_0             VARCHAR(40)       ,
	result_1             VARCHAR(40)       ,
	result_2             <PERSON>RC<PERSON><PERSON>(40)       ,
	result_3             VA<PERSON>HA<PERSON>(40)       ,
	result_4             VA<PERSON><PERSON><PERSON>(40)       ,
	result_5             VARCHAR(40)       ,
	result_6             VARCHAR(40)       ,
	result_7             VARCHAR(40)       ,
	result_8             VARCHAR(40)       ,
	result_9             VARCHAR(40)       ,
	equipment_id         BIGINT       ,
	file_name            VARCHAR(100)       ,
	state                TINYINT   DEFAULT (1)    ,
	is_deleted           TINYINT   DEFAULT (0)    ,
	created_by           BIGINT       ,
	created_time         DATETIME       ,
	updated_by           BIGINT       ,
	updated_time         DATETIME       ,
	description          VARCHAR(256)
 ) engine=InnoDB;

CREATE  TABLE collect.t_biz_txt_upload (
	id                   BIGINT  NOT NULL     PRIMARY KEY,
	equipment_id         BIGINT       ,
	file_name            VARCHAR(56)       ,
	remote_path          VARCHAR(256)       ,
	upload_time          DATETIME       ,
	file_updated_time    DATETIME       ,
	file_size            BIGINT       ,
	is_upload            INT       ,
	state                TINYINT   DEFAULT (1)    ,
	is_deleted           TINYINT   DEFAULT (0)    ,
	created_by           BIGINT       ,
	created_time         DATETIME       ,
	updated_by           BIGINT       ,
	updated_time         DATETIME       ,
	description          VARCHAR(256)
 ) engine=InnoDB;


CREATE  TABLE t_biz_ics_log (
	id                   BIGINT  NOT NULL DEFAULT 0    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT 0    ,
	file_path            VARCHAR(256)  NOT NULL DEFAULT ''    ,
	raw_data             VARCHAR(512)  NOT NULL DEFAULT ''    ,
	pos                  VARCHAR(20)  NOT NULL DEFAULT ''    ,
	master               VARCHAR(20)  NOT NULL DEFAULT ''    ,
	result               VARCHAR(20)  NOT NULL DEFAULT ''    ,
	log_value           VARCHAR(512)  NOT NULL DEFAULT ''    ,
	synced               TINYINT  NOT NULL DEFAULT 0    ,
	log_time             TIMESTAMP(3)       ,
	is_deleted           TINYINT  NOT NULL DEFAULT 0    ,
	created_by           BIGINT  NOT NULL DEFAULT 0    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT 0    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ''    ,

	CONSTRAINT idx_t_biz_ics_log_uni UNIQUE ( file_path, raw_data )
 ) engine=InnoDB;

CREATE INDEX idx_t_biz_ics_log_eid ON t_biz_ics_log ( equip_id );
CREATE INDEX idx_t_biz_ics_log_ltime ON t_biz_ics_log ( log_time );
CREATE INDEX idx_t_biz_ics_log_sync ON t_biz_ics_log ( synced );


CREATE  TABLE t_biz_equip (
	id                   BIGINT  NOT NULL DEFAULT '0'    PRIMARY KEY,
	equip_name           VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	equip_code           VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	group_id             BIGINT  NOT NULL DEFAULT '0'    ,
	client_code          VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	address              VARCHAR(256)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	equip_type           INT  NOT NULL DEFAULT '0'    ,
	file_save_day        INT  NOT NULL DEFAULT '0'    ,
	file_user_domain     VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	file_user_account    VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	file_user_pwd        VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	file_updated_time    TIMESTAMP       ,
	is_deleted           TINYINT  NOT NULL DEFAULT '0'    ,
	created_by           BIGINT  NOT NULL DEFAULT '0'    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT '0'    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_equip_gid ON t_biz_equip ( group_id );
CREATE INDEX idx_t_biz_equip_cd ON t_biz_equip ( client_code );


CREATE  TABLE t_biz_kv8000_param (
	id                   BIGINT  NOT NULL DEFAULT '0'    PRIMARY KEY,
	kv_id                BIGINT  NOT NULL DEFAULT 0    ,
	equip_id             BIGINT  NOT NULL DEFAULT '0'    ,
	variable             VARCHAR(200)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	data_type            VARCHAR(50)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	data_length          INT       ,
	address              VARCHAR(100)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	address_represent    VARCHAR(50)  NOT NULL DEFAULT ''    ,
	variable_name        VARCHAR(200)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	comm_cycle           VARCHAR(50)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	is_deleted           TINYINT  NOT NULL DEFAULT '0'    ,
	created_by           BIGINT  NOT NULL DEFAULT '0'    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT '0'    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_kv8000_param_eid ON t_biz_kv8000_param ( equip_id );
CREATE INDEX idx_t_biz_kv8000_param_var ON t_biz_kv8000_param ( variable );
CREATE INDEX idx_t_biz_kv8000_param_kid ON t_biz_kv8000_param ( kv_id );


CREATE  TABLE t_biz_kv8000_data (
	id                   BIGINT  NOT NULL DEFAULT 0    PRIMARY KEY,
	param_id             BIGINT  NOT NULL DEFAULT 0    ,
	command              VARCHAR(200)  NOT NULL DEFAULT ''    ,
	raw_data             VARCHAR(512)  NOT NULL DEFAULT ''    ,
	read_data            VARCHAR(512)  NOT NULL DEFAULT ''    ,
	read_time            TIMESTAMP(3)       ,
	is_deleted           TINYINT  NOT NULL DEFAULT 0,
	synced               TINYINT  NOT NULL DEFAULT 0
 ) engine=InnoDB;

CREATE INDEX idx_t_biz_kv8000_data_pid ON t_biz_kv8000_data ( param_id );
CREATE INDEX idx_t_biz_kv8000_data_rt ON t_biz_kv8000_data ( read_time );
CREATE INDEX idx_t_biz_ics_log_sync ON t_biz_kv8000_data ( synced );


CREATE  TABLE t_biz_equip_status (
	id                   BIGINT  NOT NULL DEFAULT 0    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT 0    ,
	equip_status         TINYINT  NOT NULL DEFAULT 0    ,
	equip_msg            VARCHAR(500)  NOT NULL DEFAULT ''    ,
	check_time           TIMESTAMP(3)       ,
	synced               TINYINT  NOT NULL DEFAULT 0    ,
	is_deleted           TINYINT  NOT NULL DEFAULT 0    ,
	created_by           BIGINT  NOT NULL DEFAULT 0    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT 0    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ''
 ) engine=InnoDB;

CREATE INDEX idx_t_biz_equip_status_eid ON t_biz_equip_status ( equip_id );
CREATE INDEX idx_t_biz_equip_status_ct ON t_biz_equip_status ( check_time );
CREATE INDEX idx_t_biz_equip_status_sync ON t_biz_equip_status ( synced );


CREATE  TABLE t_biz_ics_adjust_rules (
	id                   BIGINT  NOT NULL DEFAULT '0'    PRIMARY KEY,
	rule                 VARCHAR(1000)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	rule_type            TINYINT  NOT NULL     ,
	rule_weight          TINYINT  NOT NULL DEFAULT '0'    ,
	is_deleted           TINYINT  NOT NULL DEFAULT '0'    ,
	created_by           BIGINT  NOT NULL DEFAULT '0'    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT '0'    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE  TABLE t_biz_ics_adjust_rules_apply (
	id                   BIGINT  NOT NULL DEFAULT '0'    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT '0'    ,
	rule_id              BIGINT  NOT NULL DEFAULT '0'    ,
	rule                 VARCHAR(1000)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	enabled              TINYINT  NOT NULL DEFAULT '0'    ,
	is_deleted           TINYINT  NOT NULL DEFAULT '0'    ,
	created_by           BIGINT  NOT NULL DEFAULT '0'    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT '0'    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_adjust_rules_apply_eid ON t_biz_ics_adjust_rules_apply ( equip_id );
CREATE INDEX idx_t_biz_ics_adjust_rules_apply_rid ON t_biz_ics_adjust_rules_apply ( rule_id );


CREATE  TABLE t_biz_ics_log_tray_mean (
	id                   BIGINT  NOT NULL DEFAULT '0'    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT '0'    ,
	parameter            VARCHAR(20)  NOT NULL DEFAULT '' COLLATE utf8mb4_general_ci   ,
	mean_value           DECIMAL(10,2)  NOT NULL     ,
	start_time           TIMESTAMP(3)  NOT NULL     ,
	end_time             TIMESTAMP(3)  NOT NULL     ,
	calculated_time      TIMESTAMP(3)  NOT NULL     ,
	is_deleted           TINYINT  NOT NULL DEFAULT '0',
	synced               TINYINT  NOT NULL DEFAULT '0'
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE INDEX idx_t_biz_ics_log_mean_eid ON t_biz_ics_log_tray_mean ( equip_id );
CREATE INDEX idx_t_biz_ics_log_mean_et ON t_biz_ics_log_tray_mean ( end_time );
CREATE INDEX idx_t_biz_ics_log_mean_ct ON t_biz_ics_log_tray_mean ( calculated_time );
CREATE INDEX idx_t_biz_ics_log_mean_sync ON t_biz_ics_log_tray_mean ( synced );
CREATE INDEX idx_t_biz_ics_log_mean_param ON t_biz_ics_log_tray_mean ( parameter );


CREATE  TABLE t_biz_ics_adjust_log (
	id                   BIGINT  NOT NULL DEFAULT 0    PRIMARY KEY,
	equip_id             BIGINT  NOT NULL DEFAULT 0    ,
	start_time           TIMESTAMP(3)  NOT NULL     ,
	end_time             TIMESTAMP(3)  NOT NULL     ,
	adjusted             TINYINT  NOT NULL DEFAULT 0    ,
	adjust_type          TINYINT  NOT NULL DEFAULT 0    ,
	adjust_by            BIGINT  NOT NULL DEFAULT 0    ,
	is_deleted           TINYINT  NOT NULL DEFAULT 0    ,
	synced               TINYINT  NOT NULL DEFAULT 0
 ) engine=InnoDB;

CREATE INDEX idx_t_biz_ics_adjust_log_eid ON t_biz_ics_adjust_log ( equip_id );
CREATE INDEX idx_t_biz_ics_adjust_log_et ON t_biz_ics_adjust_log ( end_time );
CREATE INDEX idx_t_biz_ics_adjust_log_adjf ON t_biz_ics_adjust_log ( adjusted );
CREATE INDEX idx_t_biz_ics_adjust_log_sync ON t_biz_ics_adjust_log ( synced );


CREATE  TABLE t_biz_ics_adjust_log_detail (
	id                   BIGINT  NOT NULL DEFAULT 0    PRIMARY KEY,
	log_id               BIGINT  NOT NULL DEFAULT 0    ,
	apply_id             BIGINT  NOT NULL DEFAULT 0    ,
	param_id             BIGINT       ,
	mean_id              BIGINT       ,
	log_msg              VARCHAR(200)  NOT NULL DEFAULT ''    ,
	log_level            TINYINT  NOT NULL DEFAULT 0    ,
	from_value           VARCHAR(100)       ,
	to_value             VARCHAR(100)       ,
	write_time           TIMESTAMP(3)       ,
	is_deleted           TINYINT  NOT NULL DEFAULT 0    ,
	synced               TINYINT  NOT NULL DEFAULT 0
 ) engine=InnoDB;

CREATE INDEX idx_t_biz_ics_adjust_log_detail_lid ON t_biz_ics_adjust_log_detail ( log_id );
CREATE INDEX idx_t_biz_ics_adjust_log_detail_aid ON t_biz_ics_adjust_log_detail ( apply_id );
CREATE INDEX idx_t_biz_ics_adjust_log_detail_wt ON t_biz_ics_adjust_log_detail ( write_time );
CREATE INDEX idx_t_biz_ics_adjust_log_detail_mid ON t_biz_ics_adjust_log_detail ( mean_id );
CREATE INDEX idx_t_biz_ics_adjust_log_detail_sync ON t_biz_ics_adjust_log_detail ( synced );



CREATE  TABLE t_biz_kv8000_param_formula (
	id                   BIGINT  NOT NULL DEFAULT 0    PRIMARY KEY,
	param_id             BIGINT  NOT NULL DEFAULT 0    ,
	address_represent    VARCHAR(50)  NOT NULL DEFAULT ''    ,
	formula              VARCHAR(100)  NOT NULL DEFAULT ''    ,
	is_deleted           TINYINT  NOT NULL DEFAULT 0    ,
	created_by           BIGINT  NOT NULL DEFAULT 0    ,
	created_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	updated_by           BIGINT  NOT NULL DEFAULT 0    ,
	updated_at           TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP    ,
	description          VARCHAR(128)  NOT NULL DEFAULT ''
 ) engine=InnoDB;

CREATE INDEX idx_t_biz_kv8000_param_formula_pid ON t_biz_kv8000_param_formula ( param_id );

ALTER TABLE t_biz_ics_adjust_log_detail ADD INDEX idx_tbiald_to_value (to_value);

CREATE TABLE `t_biz_xjsbb_log` (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `equip_id` bigint(20) NOT NULL DEFAULT 0,
  `file_path` varchar(256) NOT NULL DEFAULT '',
  `raw_data` varchar(512) NOT NULL DEFAULT '',
  `var_name` varchar(100) NOT NULL DEFAULT '',
  `var_value` varchar(100) NOT NULL DEFAULT '',
  `log_time` timestamp(3) NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT 0,
  `synced` TINYINT  NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_xjsbb_log_eid` (`equip_id`),
  KEY `idx_t_biz_xjsbb_log_ltime` (`log_time`),
  KEY `idx_t_biz_xjsbb_log_vname` (`var_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `t_biz_xjsbb_param` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `equip_id` bigint(20) NOT NULL DEFAULT '0',
  `variable` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `variable_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `data_type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `data_unit` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint(20) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint(20) NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_xjsbb_param_var` (`variable`),
  KEY `idx_t_biz_xjsbb_param_eid` (`equip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `t_biz_xjsbb_data` (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `equip_id` bigint(20) NOT NULL DEFAULT 0,
  `param_id` bigint(20) NOT NULL DEFAULT 0,
  `read_data` varchar(256) NOT NULL DEFAULT '',
  `read_time` timestamp(3) NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT 0,
  `synced` TINYINT  NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_xjsbb_data_eid` (`equip_id`),
  KEY `idx_t_biz_xjsbb_data_rtime` (`read_time`),
  KEY `idx_t_biz_xjsbb_data_pid` (`param_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `t_biz_ab_log` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `file_path` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `raw_data` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `pos` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `master` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `log_value` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `synced` tinyint NOT NULL DEFAULT '0',
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ics_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ics_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_log_ltime` (`log_time`),
  KEY `idx_t_biz_ics_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `t_biz_ab_log_tray_mean` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `parameter` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `mean_value` decimal(10,2) NOT NULL,
  `start_time` timestamp(3) NOT NULL,
  `end_time` timestamp(3) NOT NULL,
  `calculated_time` timestamp(3) NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `synced` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_log_mean_eid` (`equip_id`),
  KEY `idx_t_biz_ics_log_mean_et` (`end_time`),
  KEY `idx_t_biz_ics_log_mean_ct` (`calculated_time`),
  KEY `idx_t_biz_ics_log_mean_sync` (`synced`),
  KEY `idx_t_biz_ics_log_mean_param` (`parameter`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `t_biz_ab_adjust_log` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `start_time` timestamp(3) NOT NULL,
  `end_time` timestamp(3) NOT NULL,
  `adjusted` tinyint NOT NULL DEFAULT '0',
  `adjust_type` tinyint NOT NULL DEFAULT '0',
  `adjust_by` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `synced` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_adjust_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_adjust_log_et` (`end_time`),
  KEY `idx_t_biz_ics_adjust_log_adjf` (`adjusted`),
  KEY `idx_t_biz_ics_adjust_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `t_biz_ab_adjust_log_detail` (
  `id` bigint NOT NULL DEFAULT '0',
  `log_id` bigint NOT NULL DEFAULT '0',
  `apply_id` bigint NOT NULL DEFAULT '0',
  `param_id` bigint DEFAULT NULL,
  `mean_id` bigint DEFAULT NULL,
  `log_msg` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `log_level` tinyint NOT NULL DEFAULT '0',
  `from_value` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `to_value` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `write_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `synced` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_adjust_log_detail_lid` (`log_id`),
  KEY `idx_t_biz_ics_adjust_log_detail_aid` (`apply_id`),
  KEY `idx_t_biz_ics_adjust_log_detail_wt` (`write_time`),
  KEY `idx_t_biz_ics_adjust_log_detail_mid` (`mean_id`),
  KEY `idx_t_biz_ics_adjust_log_detail_sync` (`synced`),
  KEY `idx_tbiald_to_value` (`to_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;




CREATE TABLE `t_biz_ab_adjust_rules` (
  `id` bigint NOT NULL DEFAULT '0',
  `rule` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `rule_type` tinyint NOT NULL,
  `rule_weight` tinyint NOT NULL DEFAULT '0',
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `t_biz_ab_adjust_rules_apply` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `rule_id` bigint NOT NULL DEFAULT '0',
  `rule` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `enabled` tinyint NOT NULL DEFAULT '0',
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_adjust_rules_apply_eid` (`equip_id`),
  KEY `idx_t_biz_ics_adjust_rules_apply_rid` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



INSERT INTO `t_biz_kv8000_param` (`id`, `kv_id`, `equip_id`, `variable`, `data_type`, `data_length`, `address`, `address_represent`, `variable_name`, `comm_cycle`, `is_deleted`, `created_by`, `created_at`, `updated_by`, `updated_at`, `description`) VALUES (1767357573968826369, 17, 18, 'yb101_PC_VSBM_Potting_PUT_SLD_ADIM_OFFSET', 'dint', 2, 'DM10020', 'W_A1', 'A数offset', '', 0, 0, '2024-03-12 09:10:56', 0, '2024-03-12 09:10:56', 'A1');
INSERT INTO `t_biz_kv8000_param` (`id`, `kv_id`, `equip_id`, `variable`, `data_type`, `data_length`, `address`, `address_represent`, `variable_name`, `comm_cycle`, `is_deleted`, `created_by`, `created_at`, `updated_by`, `updated_at`, `description`) VALUES (1767357573968826370, 17, 18, 'yb101_PC_VSBM_Potting_PUT_SLD_BDIM_OFFSET', 'dint', 2, 'DM10022', 'W_B1', 'B数offset', '', 0, 0, '2024-03-12 09:10:56', 0, '2024-03-12 09:10:56', 'B1');
INSERT INTO `t_biz_kv8000_param` (`id`, `kv_id`, `equip_id`, `variable`, `data_type`, `data_length`, `address`, `address_represent`, `variable_name`, `comm_cycle`, `is_deleted`, `created_by`, `created_at`, `updated_by`, `updated_at`, `description`) VALUES (1767357574132404224, 17, 18, 'yb102_PC_VSBM_Potting_PUT_SLD_ADIM_OFFSET', 'dint', 2, 'DM12020', 'R_A1', 'A数offset', '', 0, 0, '2024-03-12 09:10:56', 0, '2024-03-12 09:10:56', 'A1');
INSERT INTO `t_biz_kv8000_param` (`id`, `kv_id`, `equip_id`, `variable`, `data_type`, `data_length`, `address`, `address_represent`, `variable_name`, `comm_cycle`, `is_deleted`, `created_by`, `created_at`, `updated_by`, `updated_at`, `description`) VALUES (1767357574132404225, 17, 18, 'yb102_PC_VSBM_Potting_PUT_SLD_BDIM_OFFSET', 'dint', 2, 'DM12022', 'R_B1', 'B数offset', '', 0, 0, '2024-03-12 09:10:56', 0, '2024-03-12 09:10:56', 'B1');
INSERT INTO `t_biz_kv8000_param_formula` (`id`, `param_id`, `address_represent`, `formula`, `is_deleted`, `created_by`, `created_at`, `updated_by`, `updated_at`, `description`) VALUES (7, 1767357573968826369, 'W_A1', '-0.3*x+0', 0, 0, '2023-06-28 22:22:46', 0, '2023-06-28 22:22:46', '');
INSERT INTO `t_biz_kv8000_param_formula` (`id`, `param_id`, `address_represent`, `formula`, `is_deleted`, `created_by`, `created_at`, `updated_by`, `updated_at`, `description`) VALUES (8, 1767357573968826370, 'W_B1', '0.3*x+0', 0, 0, '2023-06-28 22:26:16', 0, '2023-06-28 22:26:16', '');


ALTER TABLE hds.t_biz_ics_log_tray_mean ADD head varchar(10) NULL;

-- `collect`.t_biz_ics_log_tray_predict_mean definition


-- `collect`.t_biz_ivs_log definition

CREATE TABLE `t_biz_ics_trend_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `barcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `N2_purity` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT null,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_trend_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_trend_log_barcode` (`barcode`),
  KEY `idx_t_biz_ics_trend_log_time` (`created_at`),
  KEY `idx_t_biz_ics_trend_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `t_biz_ivs_all_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ivs_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ivs_log_eid` (`equip_id`),
  KEY `idx_t_biz_ivs_log_ltime` (`log_time`),
  KEY `idx_t_biz_ivs_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- `collect`.t_biz_ics_trend_log definition

CREATE TABLE `t_biz_ics_lncm_hot_laser_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `temperature1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature6` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature7` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature8` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature9` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature10` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_trend_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_trend_log_time` (`created_at`),
  KEY `idx_t_biz_ics_trend_log_sync` (`synced`),
  KEY `t_biz_ics_trend_log_description_IDX` (`description`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- hdslocal.t_biz_ab_conf definition

CREATE TABLE `t_biz_ab_conf` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `conf_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `conf_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `conf_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ab_conf_eid` (`equip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- `collect`.t_biz_ics_pid_log definition

CREATE TABLE `t_biz_ics_pid_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `parameter` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `log_value` decimal(10,2) NOT NULL,
  `log_time` timestamp(3) NOT NULL,
  `write_time` timestamp(3) NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `from_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `to_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `log_msg` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `param_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  PRIMARY KEY (`id`),
  KEY `t_biz_ics_pid_log_equip_id_IDX` (`equip_id`) USING BTREE,
  KEY `t_biz_ics_pid_log_parameter_IDX` (`parameter`) USING BTREE,
  KEY `t_biz_ics_pid_log_log_time_IDX` (`log_time`) USING BTREE,
  KEY `t_biz_ics_pid_log_synced_IDX` (`synced`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `t_biz_hccm_result_log` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `file_path` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `raw_data` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `pos` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `master` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `log_value` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `synced` tinyint NOT NULL DEFAULT '0',
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ics_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ics_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_log_ltime` (`log_time`),
  KEY `idx_t_biz_ics_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `t_biz_hccm_slider_log` (
  `id` bigint NOT NULL DEFAULT '0',
  `equip_id` bigint NOT NULL DEFAULT '0',
  `file_path` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `raw_data` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `pos` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `master` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `log_value` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `synced` tinyint NOT NULL DEFAULT '0',
  `log_time` timestamp(3) NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_by` bigint NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_biz_ics_log_uni` (`file_path`,`raw_data`),
  KEY `idx_t_biz_ics_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_log_ltime` (`log_time`),
  KEY `idx_t_biz_ics_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- `collect`.t_biz_ics_conf definition

CREATE TABLE `t_biz_ivs_conf` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `conf_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `conf_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;