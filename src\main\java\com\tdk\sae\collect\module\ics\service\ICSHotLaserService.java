package com.tdk.sae.collect.module.ics.service;


import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.module.ics.mapper.ICSHotLaserLogMapper;
import com.tdk.sae.collect.module.ics.model.po.ICSHotLaserLog;
import com.tdk.sae.collect.tasks.enums.LncmHotLaserEnum;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class ICSHotLaserService extends ServiceImpl<ICSHotLaserLogMapper, ICSHotLaserLog> {

    public Map<String, Object> trendData(String startDate, String endDate) {
        // 如果 selected 为空，则使用当前日期
        if (StrUtil.isEmpty(endDate)) {
            endDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
        }
        LocalDateTime today =  LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
        if(StrUtil.isEmpty(startDate)){
            startDate = LocalDateTimeUtil.format(today.minusDays(1), "yyyy-MM-dd HH:mm:ss");
        }
        LocalDateTime nextDay= LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");

        // 查询符合条件的数据
        List<ICSHotLaserLog> LLaserLogs = this.lambdaQuery()
                .eq(ICSHotLaserLog::getDescription, LncmHotLaserEnum.GroupType.MARK_LASER_L.getValue())
                .ge(ICSHotLaserLog::getCreatedAt, nextDay)
                .le(ICSHotLaserLog::getCreatedAt, today)
                .orderByAsc(ICSHotLaserLog::getCreatedAt)
                .list();
        List<ICSHotLaserLog> RLaserLogs = this.lambdaQuery()
                .eq(ICSHotLaserLog::getDescription, LncmHotLaserEnum.GroupType.MARK_LASER_R.getValue())
                .ge(ICSHotLaserLog::getCreatedAt, nextDay)
                .le(ICSHotLaserLog::getCreatedAt, today)
                .orderByAsc(ICSHotLaserLog::getCreatedAt)
                .list();
        List<ICSHotLaserLog> LHotAirLogs = this.lambdaQuery()
                .eq(ICSHotLaserLog::getDescription, LncmHotLaserEnum.GroupType.MARK_HOT_AIR_L.getValue())
                .ge(ICSHotLaserLog::getCreatedAt, nextDay)
                .le(ICSHotLaserLog::getCreatedAt, today)
                .orderByAsc(ICSHotLaserLog::getCreatedAt)
                .list();
        List<ICSHotLaserLog> RHotAirLogs = this.lambdaQuery()
                .eq(ICSHotLaserLog::getDescription, LncmHotLaserEnum.GroupType.MARK_HOT_AIR_R.getValue())
                .ge(ICSHotLaserLog::getCreatedAt, nextDay)
                .le(ICSHotLaserLog::getCreatedAt, today)
                .orderByAsc(ICSHotLaserLog::getCreatedAt)
                .list();

        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();
        Map<String, List<String>> temperatureHotAirL = initializeTemperatureData();
        Map<String, List<String>> temperatureLaserL = initializeFlowData();
        Map<String, List<String>> temperatureHotAirR = initializeTemperatureData();
        Map<String, List<String>> temperatureLaserR = initializeFlowData();

        // 单独存储 xAxis 数据
        List<String> hotAirDataL = new ArrayList<>();
        List<String> laserDataL = new ArrayList<>();
        List<String> hotAirDataR = new ArrayList<>();
        List<String> laserDataR = new ArrayList<>();

        // 遍历查询结果
        LLaserLogs.forEach(log -> {
            laserDataL.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
            fillLaserData(log, temperatureLaserL);
        });
        RLaserLogs.forEach(log -> {
            laserDataR.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
            fillLaserData(log, temperatureLaserR);
        });
        RHotAirLogs.forEach(log -> {
            hotAirDataR.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
            fillHotAirData(log, temperatureHotAirR);
        });
        LHotAirLogs.forEach(log -> {
            hotAirDataL.add(LocalDateTimeUtil.format(log.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
            fillHotAirData(log, temperatureHotAirL);
        });


        // 将数据组织成需要的格式
        resultData.put("ICS-HotAir-L", formatDataForDevice(temperatureHotAirL, hotAirDataL));
        resultData.put("ICS-Laser-L", formatDataForDevice(temperatureLaserL, laserDataL));
        resultData.put("ICS-HotAir-R", formatDataForDevice(temperatureHotAirR, hotAirDataR));
        resultData.put("ICS-Laser-R", formatDataForDevice(temperatureLaserR, laserDataR));


        return resultData;
    }

    // 初始化温度数据
    private Map<String, List<String>> initializeTemperatureData() {
        Map<String, List<String>> temperatureData = new LinkedHashMap<>();
        temperatureData.put("T1", new ArrayList<>());
        temperatureData.put("T2", new ArrayList<>());
        temperatureData.put("T3", new ArrayList<>());
        temperatureData.put("T4", new ArrayList<>());
        temperatureData.put("T5", new ArrayList<>());
        return temperatureData;
    }

    // 初始化流量数据
    private Map<String, List<String>> initializeFlowData() {
        Map<String, List<String>> temperatureData = new LinkedHashMap<>();
        temperatureData.put("T1", new ArrayList<>());
        temperatureData.put("T2", new ArrayList<>());
        temperatureData.put("T3", new ArrayList<>());
        temperatureData.put("T4", new ArrayList<>());
        temperatureData.put("T5", new ArrayList<>());
        temperatureData.put("T6", new ArrayList<>());
        temperatureData.put("T7", new ArrayList<>());
        temperatureData.put("T8", new ArrayList<>());
        temperatureData.put("T9", new ArrayList<>());
        temperatureData.put("T10", new ArrayList<>());
        return temperatureData;
    }


    // 填充数据
    private void fillHotAirData(ICSHotLaserLog log, Map<String, List<String>> temperatureData) {
        temperatureData.get("T1").add(log.getTemperature1());
        temperatureData.get("T2").add(log.getTemperature2());
        temperatureData.get("T3").add(log.getTemperature3());
        temperatureData.get("T4").add(log.getTemperature4());
        temperatureData.get("T5").add(log.getTemperature5());
    }

    // 填充数据
    private void fillLaserData(ICSHotLaserLog log, Map<String, List<String>> temperatureData) {
        temperatureData.get("T1").add(log.getTemperature1());
        temperatureData.get("T2").add(log.getTemperature2());
        temperatureData.get("T3").add(log.getTemperature3());
        temperatureData.get("T4").add(log.getTemperature4());
        temperatureData.get("T5").add(log.getTemperature5());
        temperatureData.get("T6").add(log.getTemperature6());
        temperatureData.get("T7").add(log.getTemperature7());
        temperatureData.get("T8").add(log.getTemperature8());
        temperatureData.get("T9").add(log.getTemperature9());
        temperatureData.get("T10").add(log.getTemperature10());

    }



    // 格式化数据
    private Map<String, Object> formatDataForDevice(Map<String, List<String>> data, List<String> xAxisData) {
        Map<String, Object> formattedData = new LinkedHashMap<>();
        data.forEach((key, value) -> formattedData.put(key, value));
        formattedData.put("xAxis", xAxisData); // 为每个设备单独添加 xAxis
        return formattedData;
    }
}
