package com.tdk.sae.collect.module.ics.adjust;

import cn.hutool.extra.spring.SpringUtil;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.model.adjust.ICSAdjustRulesPopulate;
import com.tdk.sae.collect.module.ics.service.ICSAdjustRulesApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class RuleHandleProcessor {

    private final ICSAdjustRulesApplyService icsAdjustRulesApplyService;


    /**
     * ruleType从大到小, ruleWeight从大到小定义规则链执行顺序
     * @param equip equipmentDTO
     * @return handler
     */
    public AbstractRulesHandler populateRules(EquipmentDTO equip) {
        Long equipIdV = Long.parseLong(equip.getId());
        List<ICSAdjustRulesPopulate> rulesPopulatesList = icsAdjustRulesApplyService.getRulesPopulate(equipIdV);
        if (rulesPopulatesList.size() > 0) {
            AbstractRulesHandler.Builder builder = new AbstractRulesHandler.Builder();
            rulesPopulatesList.forEach(rule -> {
                if(rule.getRuleType()==6){
                    return;
                }
                AbstractRulesHandler r = SpringUtil.getBean(rule.getClassz());
                r.setEquipment(equip);
                r.setRulePopulate(rule);
                builder.addHandler(r);
            });
            return builder.build();
        }
        return null;
    }

}
