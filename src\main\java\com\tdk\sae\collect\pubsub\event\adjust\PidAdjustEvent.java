package com.tdk.sae.collect.pubsub.event.adjust;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class PidAdjustEvent extends ApplicationEvent {

    private String parameter;
    private String equid;

    public PidAdjustEvent(Object source, String parameter, String equid) {
        super(source);
        this.parameter = parameter;
        this.equid = equid;
    }
}