package com.tdk.sae.collect.module.ics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO;
import com.tdk.sae.collect.module.ics.model.dto.ICSPidLogDto;
import com.tdk.sae.collect.module.ics.model.po.ICSAdjustLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSPidLogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ICSPidLogMapper extends BaseMapper<ICSPidLogPO> {

    List<ICSPidLogDto> getChartDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> selectedDateTimes);

    List<ICSPidLogDto> getChartDataLatest(@Param("equipId") String equipId, @Param("param") String parameter, @Param("lastTime") LocalDateTime lastTime);

    List<ICSPidLogDto> getAdjustDataInTimeRange(@Param("equipId") String equipId, @Param("param") String parameter, @Param("times") List<LocalDateTime> selectedDateTimes);

}
