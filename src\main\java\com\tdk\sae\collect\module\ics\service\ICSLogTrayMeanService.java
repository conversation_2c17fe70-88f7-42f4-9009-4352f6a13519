package com.tdk.sae.collect.module.ics.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdk.sae.collect.domain.common.SystemProperties;
import com.tdk.sae.collect.module.equipment.model.dto.EquipmentDTO;
import com.tdk.sae.collect.module.ics.enums.SpecialEquipmentEnum;
import com.tdk.sae.collect.module.ics.mapper.ICSLogTrayMeanMapper;
import com.tdk.sae.collect.module.ics.model.SpecInfo;
import com.tdk.sae.collect.module.ics.model.dto.ICSAdjustChartDTO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogPO;
import com.tdk.sae.collect.module.ics.model.po.ICSLogTrayMeanPO;
import com.tdk.sae.collect.pubsub.event.adjust.AdjustEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

@RequiredArgsConstructor
@Service
public class ICSLogTrayMeanService extends ServiceImpl<ICSLogTrayMeanMapper, ICSLogTrayMeanPO> {

    private final ICSLogTrayMeanMapper icsLogTrayMeanMapper;

    private final SpecInfo specInfo;

    private final ApplicationEventPublisher eventPublisher;

    private final SystemProperties systemProperties;

    private static final String[] PARAMETER = new String[]{"ED1","ED2","EX1","EX2","EY1","EY2"};

    private static final String[] PARAMETERFOROTHER = new String[]{"ED1","ED2","EX1","EX2","EY1","EY2","ED3","EX3","EY3"};

    private static final List<Integer> TRAY_ASC = Arrays.asList(1,2,3,4,5,6,7,8,9,10);
    private static final List<Integer> TRAY_DESC = Arrays.asList(10,9,8,7,6,5,4,3,2,1);
    
    private List<Integer> getTrayOrder() {
        if (SpecialEquipmentEnum.isExist(systemProperties.getClientCode(), SpecialEquipmentEnum.Type.TRAY)) {
            return TRAY_DESC;
        }
        return TRAY_ASC;
    }

    /**
     * 获取托盘起始位置
     */
    private int getFirstTrayPosition() {
        List<Integer> trayOrder = getTrayOrder();
        return trayOrder.get(0);
    }
    
    @Transactional
    public void calculateTrayMean(EquipmentDTO equip, List<ICSLogPO> icsLogPOList) {
        List<ICSLogTrayMeanPO> calculatedMeans = new ArrayList<>();

        ICSLogPO[] oneMean = new ICSLogPO[10];
        int oneMeanSize = 0;
        Queue<Integer> tray = new ArrayDeque<>(getTrayOrder());
        for (ICSLogPO icsLogPO : icsLogPOList) {
            boolean master=checkMaster(icsLogPO);
            if(!master){
                tray = new ArrayDeque<>(getTrayOrder());
                oneMean = new ICSLogPO[10];
                oneMeanSize = 0;
                continue;
            }
            int pos = Integer.parseInt(icsLogPO.getPos().trim());
            if (Objects.equals(tray.poll(), pos)) {
                oneMean[pos - 1] = icsLogPO;
                oneMeanSize++;
            } else {
                tray = new ArrayDeque<>(getTrayOrder());
                oneMean = new ICSLogPO[10];
                oneMeanSize = 0;
                if (pos == getFirstTrayPosition()) {
                    tray.poll();
                    oneMean[pos - 1] = icsLogPO;
                    oneMeanSize++;
                }
            }
            if (oneMeanSize == 10) {
                LocalDateTime now = LocalDateTime.now();
                Long eId = Long.parseLong(equip.getId());
                List<ICSLogTrayMeanPO> means = new ArrayList<>();
                ICSLogTrayMeanPO ed1Mean = new ICSLogTrayMeanPO();
                ed1Mean.setEquipId(eId);
                ed1Mean.setParameter("ED1");
                ed1Mean.setCalculatedTime(now);
                means.add(ed1Mean);
                ICSLogTrayMeanPO ed2Mean = new ICSLogTrayMeanPO();
                ed2Mean.setEquipId(eId);
                ed2Mean.setParameter("ED2");
                ed2Mean.setCalculatedTime(now);
                means.add(ed2Mean);
                ICSLogTrayMeanPO ex1Mean = new ICSLogTrayMeanPO();
                ex1Mean.setEquipId(eId);
                ex1Mean.setParameter("EX1");
                ex1Mean.setCalculatedTime(now);
                means.add(ex1Mean);
                ICSLogTrayMeanPO ex2Mean = new ICSLogTrayMeanPO();
                ex2Mean.setEquipId(eId);
                ex2Mean.setParameter("EX2");
                ex2Mean.setCalculatedTime(now);
                means.add(ex2Mean);
                ICSLogTrayMeanPO ey1Mean = new ICSLogTrayMeanPO();
                ey1Mean.setEquipId(eId);
                ey1Mean.setParameter("EY1");
                ey1Mean.setCalculatedTime(now);
                means.add(ey1Mean);
                ICSLogTrayMeanPO ey2Mean = new ICSLogTrayMeanPO();
                ey2Mean.setEquipId(eId);
                ey2Mean.setParameter("EY2");
                ey2Mean.setCalculatedTime(now);
                means.add(ey2Mean);

                BigDecimal ED1 = new BigDecimal(0);
                BigDecimal ED2 = new BigDecimal(0);
                BigDecimal EX1 = new BigDecimal(0);
                BigDecimal EX2 = new BigDecimal(0);
                BigDecimal EY1 = new BigDecimal(0);
                BigDecimal EY2 = new BigDecimal(0);
                for (int i = 0; i < oneMean.length; i++) {
                    ICSLogPO[] finalOneMean = oneMean;
                    int finalI = i;
                    if (i == 0) {
                        means.forEach(m -> m.setStartTime(finalOneMean[finalI].getLogTime()));
                    }
                    if (i == 9) {
                        means.forEach(m -> m.setEndTime(finalOneMean[finalI].getLogTime()));
                    }
                    ICSLogPO log = oneMean[i];
                    String[] columns = log.getDescription().split(",");
                    String[] values = log.getRawData().split(",");
                    for (int i1 = 0; i1 < columns.length; i1++) {
                        switch (columns[i1]) {
                            case "EX1":
                                EX1 = EX1.add(new BigDecimal(values[i1]));
                                break;
                            case "EX2":
                                EX2 = EX2.add(new BigDecimal(values[i1]));
                                break;
                            case "EY1":
                                EY1 = EY1.add(new BigDecimal(values[i1]));
                                break;
                            case "EY2":
                                EY2 = EY2.add(new BigDecimal(values[i1]));
                                break;
                            case "ED1":
                            case "EW1":
                                ED1 = ED1.add(new BigDecimal(values[i1]));
                                break;
                            case "ED2":
                            case "EW2":
                                ED2 = ED2.add(new BigDecimal(values[i1]));
                                break;
                        }
                    }
                }
                ed1Mean.setMeanValue(ED1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ed2Mean.setMeanValue(ED2.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ex1Mean.setMeanValue(EX1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ex2Mean.setMeanValue(EX2.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ey1Mean.setMeanValue(EY1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ey2Mean.setMeanValue(EY2.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));

                calculatedMeans.addAll(means);
                tray = new ArrayDeque<>(getTrayOrder());
                oneMean = new ICSLogPO[10];
                oneMeanSize = 0;
            }
        }
        if (calculatedMeans.size() > 0) {
            this.saveBatch(calculatedMeans);
            //mean值变化，推送数据到前端实现图表自动刷新
            System.out.println("推送数据");
            for (String address : PARAMETER) {
                eventPublisher.publishEvent(new AdjustEvent(this,address,equip.getId()));
            }
        }
    }

    @Transactional
    public void calculateTrayMeanByOther(EquipmentDTO equip, List<ICSLogPO> icsLogPOList) {
        List<ICSLogTrayMeanPO> calculatedMeans = new ArrayList<>();

        ICSLogPO[] oneMean = new ICSLogPO[10];
        int oneMeanSize = 0;
        Queue<Integer> tray = new ArrayDeque<>(getTrayOrder());
        for (ICSLogPO icsLogPO : icsLogPOList) {
            boolean master=checkMaster(icsLogPO);
            if(!master){
                tray = new ArrayDeque<>(getTrayOrder());
                oneMean = new ICSLogPO[10];
                oneMeanSize = 0;
                continue;
            }
            int pos = Integer.parseInt(icsLogPO.getPos().trim());
            if (Objects.equals(tray.poll(), pos)) {
                oneMean[pos - 1] = icsLogPO;
                oneMeanSize++;
            } else {
                tray = new ArrayDeque<>(getTrayOrder());
                oneMean = new ICSLogPO[10];
                oneMeanSize = 0;
                if (pos == getFirstTrayPosition()) {
                    tray.poll();
                    oneMean[pos - 1] = icsLogPO;
                    oneMeanSize++;
                }
            }
            if (oneMeanSize == 10) {
                LocalDateTime now = LocalDateTime.now();
                Long eId = Long.parseLong(equip.getId());
                List<ICSLogTrayMeanPO> means = new ArrayList<>();
                ICSLogTrayMeanPO ed1Mean = new ICSLogTrayMeanPO();
                ed1Mean.setEquipId(eId);
                ed1Mean.setParameter("ED1");
                ed1Mean.setCalculatedTime(now);
                means.add(ed1Mean);
                ICSLogTrayMeanPO ed2Mean = new ICSLogTrayMeanPO();
                ed2Mean.setEquipId(eId);
                ed2Mean.setParameter("ED2");
                ed2Mean.setCalculatedTime(now);
                means.add(ed2Mean);
                ICSLogTrayMeanPO ed3Mean = new ICSLogTrayMeanPO();
                ed3Mean.setEquipId(eId);
                ed3Mean.setParameter("ED3");
                ed3Mean.setCalculatedTime(now);
                means.add(ed3Mean);
                ICSLogTrayMeanPO ex1Mean = new ICSLogTrayMeanPO();
                ex1Mean.setEquipId(eId);
                ex1Mean.setParameter("EX1");
                ex1Mean.setCalculatedTime(now);
                means.add(ex1Mean);
                ICSLogTrayMeanPO ex2Mean = new ICSLogTrayMeanPO();
                ex2Mean.setEquipId(eId);
                ex2Mean.setParameter("EX2");
                ex2Mean.setCalculatedTime(now);
                means.add(ex2Mean);
                ICSLogTrayMeanPO ex3Mean = new ICSLogTrayMeanPO();
                ex3Mean.setEquipId(eId);
                ex3Mean.setParameter("EX3");
                ex3Mean.setCalculatedTime(now);
                means.add(ex3Mean);
                ICSLogTrayMeanPO ey1Mean = new ICSLogTrayMeanPO();
                ey1Mean.setEquipId(eId);
                ey1Mean.setParameter("EY1");
                ey1Mean.setCalculatedTime(now);
                means.add(ey1Mean);
                ICSLogTrayMeanPO ey2Mean = new ICSLogTrayMeanPO();
                ey2Mean.setEquipId(eId);
                ey2Mean.setParameter("EY2");
                ey2Mean.setCalculatedTime(now);
                means.add(ey2Mean);
                ICSLogTrayMeanPO ey3Mean = new ICSLogTrayMeanPO();
                ey3Mean.setEquipId(eId);
                ey3Mean.setParameter("EY3");
                ey3Mean.setCalculatedTime(now);
                means.add(ey3Mean);

                BigDecimal ED1 = new BigDecimal(0);
                BigDecimal ED2 = new BigDecimal(0);
                BigDecimal ED3 = new BigDecimal(0);
                BigDecimal EX1 = new BigDecimal(0);
                BigDecimal EX2 = new BigDecimal(0);
                BigDecimal EX3 = new BigDecimal(0);
                BigDecimal EY1 = new BigDecimal(0);
                BigDecimal EY2 = new BigDecimal(0);
                BigDecimal EY3 = new BigDecimal(0);
                for (int i = 0; i < oneMean.length; i++) {
                    ICSLogPO[] finalOneMean = oneMean;
                    int finalI = i;
                    if (i == 0) {
                        means.forEach(m -> m.setStartTime(finalOneMean[finalI].getLogTime()));
                    }
                    if (i == 9) {
                        means.forEach(m -> m.setEndTime(finalOneMean[finalI].getLogTime()));
                    }
                    ICSLogPO log = oneMean[i];
                    String[] columns = log.getDescription().split(",");
                    String[] values = log.getRawData().split(",");
                    for (int i1 = 0; i1 < columns.length; i1++) {
                        switch (columns[i1]) {
                            case "EX1":
                                EX1 = EX1.add(new BigDecimal(values[i1]));
                                break;
                            case "EX2":
                                EX2 = EX2.add(new BigDecimal(values[i1]));
                                break;
                            case "EX3":
                                EX3 = EX3.add(new BigDecimal(values[i1]));
                                break;
                            case "EY1":
                                EY1 = EY1.add(new BigDecimal(values[i1]));
                                break;
                            case "EY2":
                                EY2 = EY2.add(new BigDecimal(values[i1]));
                                break;
                            case "EY3":
                                EY3 = EY3.add(new BigDecimal(values[i1]));
                                break;
                            case "ED1":
                                ED1 = ED1.add(new BigDecimal(values[i1]));
                                break;
                            case "EW1":
                                ED1 = ED1.add(new BigDecimal(values[i1]));
                                break;
                            case "ED2":
                                ED2 = ED2.add(new BigDecimal(values[i1]));
                                break;
                            case "EW2":
                                ED2 = ED2.add(new BigDecimal(values[i1]));
                                break;
                            case "ED3":
                                ED3 = ED3.add(new BigDecimal(values[i1]));
                                break;
                            case "EW3":
                                ED3 = ED3.add(new BigDecimal(values[i1]));
                                break;
                        }
                    }
                }
                ed1Mean.setMeanValue(ED1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ed2Mean.setMeanValue(ED2.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ed3Mean.setMeanValue(ED3.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ex1Mean.setMeanValue(EX1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ex2Mean.setMeanValue(EX2.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ex3Mean.setMeanValue(EX3.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ey1Mean.setMeanValue(EY1.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ey2Mean.setMeanValue(EY2.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));
                ey3Mean.setMeanValue(EY3.divide(new BigDecimal(oneMeanSize), 2, RoundingMode.HALF_UP));

                calculatedMeans.addAll(means);
                tray = new ArrayDeque<>(getTrayOrder());
                oneMean = new ICSLogPO[10];
                oneMeanSize = 0;
            }
        }
        if (calculatedMeans.size() > 0) {
            this.saveBatch(calculatedMeans);
            //mean值变化，推送数据到前端实现图表自动刷新
            System.out.println("推送数据");
            for (String address : PARAMETERFOROTHER) {
                eventPublisher.publishEvent(new AdjustEvent(this,address,equip.getId()));
            }
        }
    }

    public List<ICSAdjustChartDTO> getChartDataInTimeRange(String equipId, String parameter, List<LocalDateTime> selectedDateTimes) {
        return icsLogTrayMeanMapper.getChartDataInTimeRange(equipId, parameter, selectedDateTimes);
    }

    public List<ICSAdjustChartDTO> getChartDataLatest(String equipId, String parameter, LocalDateTime lastTime) {
        return icsLogTrayMeanMapper.getChartDataLatest(equipId, parameter, lastTime);
    }

    public Map<String, Object> buildMeanChart(String equipId, String parameter, List<LocalDateTime> selectedDateTimes) {
        List<ICSAdjustChartDTO> chartDTOList = getChartDataInTimeRange(equipId, parameter, selectedDateTimes);
        return doBuildMeanChart(equipId, parameter, chartDTOList);
    }

    // 为什么要重新查询调整参数：调整参数过程中可能会有延迟，导致前端如果请求了数据但是调整参数还未更新的话就会丢失掉这次调整的显示。所以现在改为每次都按选中的日期获取所有调整参数
    public Map<String, Object> buildMeanChart(String equipId, String parameter, LocalDateTime lastTime, List<LocalDateTime> selectedDateTimes) {
        List<ICSAdjustChartDTO> chartDTOList = getChartDataLatest(equipId, parameter, lastTime);
        Map<String, Object> result = doBuildMeanChart(equipId, parameter, chartDTOList);
        // override adjust points
        List<Object[]> adjustData = new ArrayList<>();
        List<ICSAdjustChartDTO> adjustChartDTOList = icsLogTrayMeanMapper.getAdjustDataInTimeRange(equipId, parameter, selectedDateTimes);
        if (adjustChartDTOList.size() > 0) {
            adjustChartDTOList.forEach(point -> {
                Object[] tmp = new Object[2];
                tmp[0] = LocalDateTimeUtil.format(point.getEndTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                tmp[1] = point.getToValue();
                adjustData.add(tmp);
            });
            result.put("adjustData", adjustData);
        }
        return result;
    }

    public Map<String, Object> doBuildMeanChart(String equipId, String parameter, List<ICSAdjustChartDTO> chartDTOList) {
        Map<String, Object> result = new HashMap<>(6);
        List<String> xAxisData = new ArrayList<>();
        List<LocalDateTime> xAxisTimes = new ArrayList<>();
        List<BigDecimal> meanData = new ArrayList<>();
        List<Object[]> adjustData = new ArrayList<>();
        chartDTOList.forEach(point -> {
            xAxisTimes.add(point.getEndTime());
            String time = LocalDateTimeUtil.format(point.getEndTime(), "yyyy-MM-dd HH:mm:ss.SSS");
            xAxisData.add(time);
            meanData.add(point.getMeanValue());
            if (StrUtil.isNotEmpty(point.getToValue())) {
                Object[] tmp = new Object[2];
                tmp[0] = time;
                tmp[1] = point.getToValue();
                adjustData.add(tmp);
            }
        });

        Map<String, Map<String, BigDecimal>> specMap = specInfo.getSpecInDbFirst(equipId);
        BigDecimal mus = specMap.get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
        BigDecimal mls = specMap.get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
        BigDecimal muc = specMap.get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
        BigDecimal mlc = specMap.get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
        BigDecimal mui = specMap.get(parameter).get("ADJUST_INNER_UPPER_CONTROL_LIMIT");
        BigDecimal mli = specMap.get(parameter).get("ADJUST_INNER_LOWER_CONTROL_LIMIT");
        BigDecimal target = specMap.get(parameter).get("TARGET");

//        BigDecimal mus = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_SPEC_LIMIT");
//        BigDecimal mls = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_SPEC_LIMIT");
//        BigDecimal muc = SpecInfo.getSpec().get(parameter).get("ADJUST_UPPER_CONTROL_LIMIT");
//        BigDecimal mlc = SpecInfo.getSpec().get(parameter).get("ADJUST_LOWER_CONTROL_LIMIT");
//        BigDecimal target = SpecInfo.getSpec().get(parameter).get("TARGET");
        Map<String, String> spec = new HashMap<>();
        spec.put("mus", mus.toPlainString());
        spec.put("mls", mls.toPlainString());
        spec.put("target", target.toPlainString());
        spec.put("muc", muc.toPlainString());
        spec.put("mlc", mlc.toPlainString());
        if (!StringUtils.isEmpty(mli)){
            spec.put("mui", mui.toPlainString());
            spec.put("mli", mli.toPlainString());
        }
        result.put("specs", spec);

        result.put("xAxisTimes", xAxisTimes);
        result.put("xAxisData", xAxisData);
        result.put("meanData", meanData);
        result.put("adjustData", adjustData);
        return result;
    }

    public boolean checkMaster(ICSLogPO log){
        String[] values = log.getRawData().split(",");
        String HgaMaster=values[2].trim();
        return HgaMaster.equals("N") || HgaMaster.equals("0");
    }

}
