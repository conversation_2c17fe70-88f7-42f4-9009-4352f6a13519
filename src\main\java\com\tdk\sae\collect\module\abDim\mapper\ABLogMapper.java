package com.tdk.sae.collect.module.abDim.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tdk.sae.collect.module.abDim.model.po.ABLogPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ABLogMapper extends BaseMapper<ABLogPO> {
    @Delete("DELETE FROM t_biz_ab_log WHERE log_time < #{time} order by log_time asc limit 10000")
    int deleteData(@Param("time") String time);
}
