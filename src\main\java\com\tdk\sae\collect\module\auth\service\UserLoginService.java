package com.tdk.sae.collect.module.auth.service;

import cn.hutool.core.bean.BeanUtil;
import com.tdk.sae.collect.domain.enums.ResponseCodeEnum;
import com.tdk.sae.collect.exceptions.LoginException;
import com.tdk.sae.collect.module.auth.cache.LoggedInUserCache;
import com.tdk.sae.collect.module.auth.cache.LoginParamsCache;
import com.tdk.sae.collect.module.auth.dto.LoggedInUser;
import com.tdk.sae.collect.module.auth.dto.LoginUser;
import com.tdk.sae.collect.module.auth.dto.UserDTO;
import com.tdk.sae.collect.module.auth.param.LoginParams;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class UserLoginService {

    private final LoginParamsCache loginParamsCache;

    private final LoggedInUserCache loggedInUserCache;

    private final UserInfoService userInfoService;

    public LoginUser login(LoginParams loginParams) {
        LoggedInUser loggedInUser = null;
        loginParamsCache.set(loginParams, loginParams.getUsername());
        if ((loggedInUser = this.authenticate(loginParams)) != null) {
            loggedInUserCache.set(loggedInUser, loginParams.getUsername());
            LoginUser loginUser = BeanUtil.toBean(loggedInUser, LoginUser.class);
            loginUser.setAccessToken(loginParams.getUsername());
            return loginUser;
        } else {
            throw new LoginException(ResponseCodeEnum.USE_LOGIN_ERROR, "用户名或者密码错误");
        }
    }

    private LoggedInUser authenticate(LoginParams loginParams) {
        UserDTO userDTO = userInfoService.loadUserByUsername(loginParams.getUsername());
        if (userDTO != null) {
            return BeanUtil.toBean(userDTO, LoggedInUser.class);
        }
        return null;
    }

}
