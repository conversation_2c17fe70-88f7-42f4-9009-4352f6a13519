package com.tdk.sae.collect.module.auth.cache;

import com.tdk.sae.collect.cache.redis.RedisCache;
import com.tdk.sae.collect.module.auth.dto.TreeMenuDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MenuCache extends RedisCache<List<TreeMenuDTO>> {

    private static final MenuKeyDefine MENU_KEY_DEFINE = new MenuKeyDefine();

    @Override
    protected void initKeyDefine() {
        keyDefine = MENU_KEY_DEFINE;
    }

}
