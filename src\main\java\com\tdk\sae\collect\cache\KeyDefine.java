package com.tdk.sae.collect.cache;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;

import java.util.concurrent.TimeUnit;

public abstract class KeyDefine {

    private static final CacheProperties cacheProperties;
    static {
        cacheProperties = SpringUtil.getBean(CacheProperties.class);
    }

    public abstract String getName();

    public abstract String getPrefix();

    public abstract long getExpire();

    public abstract TimeUnit getTimeUnit();

    public abstract Class<?> getClazz();

    public final String getFullPrefix() {
        return normalize(getPrefix());
    }

    public static String normalize(String key) {
        if (StrUtil.isBlank(key)) {
            return null;
        }
        return String.format("%s:%s", cacheProperties.getPrefix(), key);
    }

}
