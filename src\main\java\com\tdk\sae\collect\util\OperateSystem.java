package com.tdk.sae.collect.util;


import lombok.experimental.UtilityClass;

@UtilityClass
public class OperateSystem {

    private Boolean isLinux = null;

    public  boolean getIsLinux(){

        if(isLinux != null){
            return isLinux;
        }

        String os = System.getProperty("os.name");

        isLinux = !os.toLowerCase().startsWith("win");

        return isLinux;
    }




}
