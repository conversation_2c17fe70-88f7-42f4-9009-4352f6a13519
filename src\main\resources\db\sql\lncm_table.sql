-- hdslocal.t_biz_ics_lncm_hot_laser_log definition

CREATE TABLE IF NOT EXISTS  `t_biz_ics_lncm_hot_laser_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `temperature1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature6` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature7` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature8` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature9` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `temperature10` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_trend_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_trend_log_time` (`created_at`),
  KEY `idx_t_biz_ics_trend_log_sync` (`synced`),
  KEY `t_biz_ics_trend_log_description_IDX` (`description`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$



CREATE TABLE IF NOT EXISTS  `t_biz_ics_trend_log` (
  `id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `equip_id` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `barcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `temperature5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow3` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow4` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `flow5` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT (_utf8mb4''),
  `N2_purity` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `synced` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `is_deleted` tinyint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `created_at` timestamp NOT NULL DEFAULT (now()),
  `updated_by` bigint NOT NULL DEFAULT (_utf8mb4'0'),
  `updated_at` timestamp NOT NULL DEFAULT (now()),
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT null,
  PRIMARY KEY (`id`),
  KEY `idx_t_biz_ics_trend_log_eid` (`equip_id`),
  KEY `idx_t_biz_ics_trend_log_barcode` (`barcode`),
  KEY `idx_t_biz_ics_trend_log_time` (`created_at`),
  KEY `idx_t_biz_ics_trend_log_sync` (`synced`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci$$