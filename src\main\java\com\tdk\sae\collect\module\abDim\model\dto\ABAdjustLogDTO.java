package com.tdk.sae.collect.module.abDim.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode
public class ABAdjustLogDTO implements Serializable {

    private Long id;

    private Long equipId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer adjusted;

    private Integer adjustType;

    private Long adjustBy;

    private Integer isDeleted = 0;

    private Integer synced = 0;

    @JsonIgnore
    private List<ABAdjustLogDetailDTO> logDetails;

    @JsonIgnore
    public ABAdjustLogDetailDTO newDetail() {
        ABAdjustLogDetailDTO newDetail = new ABAdjustLogDetailDTO();
        newDetail.setLogId(id);
        logDetails.add(newDetail);
        return newDetail;
    }

}
